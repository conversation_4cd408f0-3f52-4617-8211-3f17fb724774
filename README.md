# OpenDRIVE 到 MetaDrive 场景转换工具

`convert_xodr_to_pkl.py` 是将 OpenDRIVE 1.4 标准地图转换为 MetaDrive 兼容格式的核心工具。

## 📥 安装依赖
```bash
pip install -r requirements.txt
```
requirements.txt 内容：
```
lxml==4.9.3
numpy==1.23.5
metadrive-simulator==*******
```

## 🚀 快速开始
### 命令行模式
```bash
python convert_xodr_to_pkl.py \
    --input path/to/input.xodr \
    --output path/to/output.pkl \
    --sample-step 0.5 \
    --coord-precision 6
```

### 参数说明
| 参数 | 默认值 | 说明 |
|------|--------|-----|
| --input | 必填 | 输入.xodr文件路径 |
| --output | 必填 | 输出.pkl文件路径 |
| --sample-step | 0.5 | 几何采样步长（米） |
| --coord-precision | 6 | 坐标小数位数 |

## 🗺️ 输入文件规范
1. 必须包含至少 1 条非交叉口道路
2. 支持的道路几何类型：
   - 直线（line）
   - 螺旋线（spiral）
   - 三次多项式曲线（poly3）
3. 车道类型要求：
   ```python
   VALID_LANE_TYPES = ["driving", "entry", "exit", "onRamp", "offRamp"]
   ```

## 🧬 输出数据结构
### 道路特征 (map_features)
```python
{
    "road_<ID>": {
        "polyline": [[x,y],...],  # 采样点坐标
        "headings": [θ1, θ2,...], # 航向角序列
        "lane_sections": [{
            "s": 0.0,            # 起始弧长
            "length": 150.0,     # 路段长度
            "lanes": [{
                "id": "1",
                "type": "driving",
                "width": [{
                    "start_offset": 0.0,
                    "length": 20.0,
                    "coefficients": [3.0, 0.0, -0.02, 0.001]
                }]
            }]
        }]
    }
}
```

### 交叉口系统 (junctions)
```python
{
    "junction_<ID>": {
        "connections": [{
            "incomingRoad": "road_A",
            "connectingRoad": "road_B",
            "contactPoint": "start",
            "laneLinks": [
                {"from": "1", "to": "-1"}  # 车道连接映射
            ]
        }]
    }
}
```

## ⚙️ 高级配置
### 自定义采样精度
```python
# 修改 process_road_geometry 函数
s += 0.5  # 改为更小的值（如0.2）可提高采样密度
```

### 扩展车道类型支持
```python
# 修改 process_lane_section 函数
if lane.type in ["driving", "entry", "exit", "onRamp", "offRamp", "shoulder"]:
```

## 🐛 调试建议
1. 可视化检查工具：
```bash
python -m metadrive.utils.plot_scenario output.pkl
```

2. 常见错误处理：
- **几何不连续**：检查原始xodr的相邻道路s坐标连续性
- **车道连接断裂**：验证junction中的laneLink定义
- **坐标系偏移**：确认xodr使用笛卡尔坐标系

## 📚 开发文档
### 核心函数说明
| 函数 | 功能 | 关键参数 |
|------|------|---------|
| `process_road_geometry` | 道路几何解析 | road: 道路对象 |
| `process_lane_section` | 车道属性提取 | lane_section: 车道段对象 |
| `find_connecting_roads` | 连接关系分析 | road: 当前道路对象 |

### 坐标系转换流程
```
OpenDRIVE局部坐标系 → 全局笛卡尔坐标系 → MetaDrive世界坐标系
```

## 📜 版本记录
- v1.0.0 (2025-05-09)
  - 支持基础道路网络转换
  - 实现车道级拓扑连接
  - 提供交叉口处理能力

## 📞 技术支持
遇到问题请提交 issue 至：
https://github.com/metadriverse/opendrive-converter/issues