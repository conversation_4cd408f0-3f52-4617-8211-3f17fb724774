import os
import time
import numpy as np
from tqdm import tqdm
from ray.rllib.algorithms.bc import BCConfig
from ray.tune.registry import register_env
from ray.rllib.policy.policy import Policy
import ray
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.envs.gym_wrapper import createGymWrapper
from torch.utils.tensorboard import SummaryWriter
from ray.rllib.algorithms.ppo import PPOConfig
from datetime import datetime
from ray.rllib.models import ModelCatalog
from models.attention_net import CustomAttentionNet

def env_creator(env_config):
    env = createGymWrapper(MetaDriveEnv)(config={"use_render": False})
    return env


register_env('metadrive-env', env_creator)


class BehaviorCloneTrainer:
    def __init__(self, bc_data_path, train_batch_size=128, iteration_num=1000, log_dir='./tensorboard_logs'):
        self.bc_data_path = bc_data_path
        self.train_batch_size = train_batch_size
        self.iteration_num = iteration_num
        self.log_dir = log_dir
        self.logger = TrainingLogger(log_dir=self.log_dir)
        self.setup_environment()
        self.setup_config()
        self.algo = self.config.build()

    def setup_environment(self):
        self.game = env_creator({})

    def setup_config(self):
        config = BCConfig()
        config.postprocess_inputs = False  # BC不需要计算优势函数
        # 注册自定义模型
        ModelCatalog.register_custom_model("attention_net", CustomAttentionNet)

        # 配置模型
        config.model.update({
            "custom_model": "attention_net",
            "custom_model_config": {
                "attention_num_transformer_units": 1,
                "attention_dim": 64,
                "attention_num_heads": 8,
                "attention_head_dim": 32,
                "attention_memory_inference": 50,
                "attention_memory_training": 50,
                "attention_position_wise_mlp_dim": 64
            }
        })

        # 设置序列和Batch配置
        sequence_length = 20
        config.replay_sequence_length = sequence_length
        config.rollout_fragment_length = sequence_length
        config.train_batch_size = self.train_batch_size * 8  # 累积多个batch
        config.model["max_seq_len"] = sequence_length

        # 训练相关配置
        config.framework('torch')
        config.beta = 0.0  # BC算法要求
        config.training(
            lr=1e-5,
            beta=0.0,
            grad_clip=0.5,
            gamma=0.99,
        )

        # 环境配置
        config.environment(env='metadrive-env',
                         disable_env_checking=False,
                         observation_space=self.game.observation_space,
                         action_space=self.game.action_space)

        # 其他配置
        config.checkpointing(export_native_model_files=True)
        config.offline_data(input_=self.bc_data_path)

        # 配置评估参数
        config.evaluation(
            evaluation_num_workers=0,  # 减少worker数量，专注于单GPU训练
            evaluation_duration=10
        )

        # 配置资源
        config.resources(
            num_gpus=0,  # 禁用GPU训练
            num_cpus_per_worker=2
        )

        # 配置调试参数
        config.debugging(log_level='ERROR')

        # 将配置赋值给实例变量
        self.config = config
        # 打印配置

        print(config.to_dict())

    def train(self):
        best_loss = float('inf')
        patience = 50
        bad_iterations = 0
        
        for iteration in tqdm(range(self.iteration_num)):
            try:
                result = self.algo.train()
                learner_stats = result['info']['learner']['default_policy']['learner_stats']
                total_loss = learner_stats['total_loss']
                policy_loss = learner_stats['policy_loss']
                
                # 检查损失值是否有效
                if np.isnan(total_loss) or np.isnan(policy_loss):
                    print("Warning: Loss is NaN, skipping iteration")
                    continue
                    
                # 检查是否有显著改善
                if total_loss < best_loss:
                    best_loss = total_loss
                    bad_iterations = 0
                    # 保存最佳模型
                    if iteration > 0 and iteration % 1000 == 0:
                        now = datetime.now()
                        formatted_date = now.strftime("%Y-%m-%d-%H-%M-%S")
                        self.algo.save(f'./bc_models/BC_best_model_{iteration}_'+ formatted_date)
                else:
                    bad_iterations += 1
                
                # Early stopping
                if bad_iterations >= patience:
                    print(f"\nStopping early at iteration {iteration} due to lack of improvement")
                    break
                    
                print(f'----- iteration={iteration}, total_loss={total_loss:.5f}, policy_loss={policy_loss:.5f}')
                self.logger.log_metrics(iteration, total_loss, policy_loss)
                
            except Exception as e:
                print(f"Error during training: {str(e)}")
                continue
        now = datetime.now()
        formatted_date = now.strftime("%Y-%m-%d-%H-%M-%S")
        file_name = self.algo.save(f'./bc_models/BC_iteration_{iteration}_model.pth'+ str(formatted_date))
        self.logger.close()


class TrainingLogger:
    def __init__(self, log_dir='./tensorboard_logs'):
        # 初始化TensorBoard写入器
        self.writer = SummaryWriter(log_dir=log_dir,flush_secs=10)
    
    def log_metrics(self, iteration, total_loss, policy_loss):
        # 记录总损失和策略损失到TensorBoard
        self.writer.add_scalar('Loss/Total', total_loss, iteration)   # total_loss\policy_loss  公式    自定义的loss 用的是什么函数  预测值是什么action a1 a2?  算loss,要不要换lossfuction. 模仿->强化
        """
        定量定性评价模仿学习
        1. 定量评价
            分析的数据对象:loss -> 收敛的趋势
            评价指标: 外部指标  车成功到达终点次数, 时长,路面边界.
            
            加载好模仿模型后用到reward /home/<USER>/rl/RL-IL/RL-IL/load_model.py  加载模仿学习的模型
        2. 定性评价
            可视化  
        """
        self.writer.add_scalar('Loss/Policy', policy_loss, iteration)
    
    def close(self):
        # 关闭TensorBoard写入器
        self.writer.close()


if __name__ == "__main__":
    bc_data_path = "/home/<USER>/data/bc_data/200/output-2024-12-26_21-16-35_worker-0_0.json"
    trainer = BehaviorCloneTrainer(bc_data_path=bc_data_path)
    trainer.train()
