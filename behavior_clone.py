#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import time
from datetime import datetime

import ray
from metadrive.envs.gym_wrapper import createGymWrapper
from metadrive.envs.metadrive_env import MetaDriveEnv
from ray.rllib.algorithms.bc import BCConfig
from ray.rllib.utils.framework import try_import_torch
from ray.tune.registry import register_env
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm
from ray.tune.logger import pretty_print

from bc_policy import MyCustomMARWILTorchPolicy

torch, _ = try_import_torch()
torch.backends.cudnn.enabled = True

os.environ["GRPC_VERBOSITY"] = "ERROR"
os.environ["GRPC_TRACE"] = ""

def env_creator(env_config):
    env = createGymWrapper(MetaDriveEnv)(config={"use_render": False})
    return env

@ray.remote
class ParameterServer:
    def __init__(self):
        self.current_weights = None
        
    def update_weights(self, worker_id, weights):
        """接收worker的权重更新"""
        self.current_weights = weights
        return True
        
    def get_weights(self):
        """返回当前的权重"""
        return self.current_weights

@ray.remote
class TrainingLogger:
    def __init__(self, log_dir):
        self.writer = SummaryWriter(log_dir=log_dir, flush_secs=10)
    
    def log_metrics(self, iteration, total_loss, policy_loss):
        self.writer.add_scalar('Loss/Total', total_loss, iteration)
        self.writer.add_scalar('Loss/Policy', policy_loss, iteration)
    
    def close(self):
        self.writer.close()

@ray.remote(num_gpus=0.1)
class BCTrainer:
    def __init__(self, config, trainer_id, ps):
        self.trainer_id = trainer_id
        self.ps = ps
        register_env('metadrive-env', env_creator)
        self.algo = config.build()
        
    def train_iteration(self, global_iteration):
        # 获取参数服务器的最新权重
        weights = ray.get(self.ps.get_weights.remote())
        if weights is not None:
            self.algo.get_policy().set_weights(weights)
            
        # 执行训练
        result = self.algo.train()
        
        # 将更新后的权重发送到参数服务器
        self.ps.update_weights.remote(self.trainer_id, self.algo.get_policy().get_weights())
        
        return {
            'trainer_id': self.trainer_id,
            'iteration': global_iteration,
            'learner_stats': result['info']['learner']['default_policy']['learner_stats'],
            'time_total_s': result['time_total_s']
        }
    
    def save_checkpoint(self, path):
        self.algo.save(path)
    
    def get_weights(self):
        return self.algo.get_policy().get_weights()

def setup_config(bc_data_path, train_batch_size=51200):
    game = env_creator({})
    
    config = BCConfig().environment(
        env='metadrive-env',
    ).training(
        train_batch_size=train_batch_size // 4,  # 将batch size分配给4个worker
        lr=1e-6,
        grad_clip=1.0,
    ).resources(
        num_gpus=1,
        num_cpus_per_worker=0.1,
    ).rollouts(
        num_rollout_workers=2,
        num_envs_per_worker=1,
        batch_mode="truncate_episodes"
    ).framework(
        "torch"
    ).offline_data(
        input_=bc_data_path
    ).debugging(log_level="ERROR")

    config.update_from_dict({
        "multiagent": {
            "policies": {
                "default_policy": (
                    MyCustomMARWILTorchPolicy,
                    game.observation_space,
                    game.action_space,
                    {}
                )
            }
        }
    })
    
    return config

def main():
    ray.init(
        num_cpus=22,
        num_gpus=1,
        # include_dashboard=False,
        # ignore_reinit_error=True,
    )

    num_trainers = 5
    total_iterations = 5000
    save_interval = 2500

    bc_data_path = "/home/<USER>/data/bc_data/200/output-2025-03-06_12-00-18_worker-0_0.json"
    base_log_dir = f'/home/<USER>/data/tensorboard_logs/{time.strftime("%Y-%m-%d-%H-%M", time.localtime())}_BC'
    
    # 创建参数服务器
    ps = ParameterServer.remote()
    
    # 创建训练器和日志记录器
    config = setup_config(bc_data_path)
    trainers = [BCTrainer.remote(config, i, ps) for i in range(num_trainers)]
    loggers = [TrainingLogger.remote(f"{base_log_dir}_trainer_{i}") for i in range(num_trainers)]
    
    # 初始化参数服务器的权重
    initial_weights = ray.get(trainers[0].get_weights.remote())
    ps.update_weights.remote(0, initial_weights)
    
    now = datetime.now()
    formatted_date = now.strftime("%Y-%m-%d-%H-%M-%S")
    
    try:
        for iteration in tqdm(range(0, total_iterations, num_trainers)):
            # 并行执行训练
            train_tasks = [trainer.train_iteration.remote(iteration + i) for i, trainer in enumerate(trainers)]
            results = ray.get(train_tasks)
            
            # 记录结果
            log_tasks = []
            for result in results:
                trainer_id = result['trainer_id']
                stats = result['learner_stats']
                log_tasks.append(loggers[trainer_id].log_metrics.remote(
                    iteration + trainer_id,
                    stats['total_loss'],
                    stats['policy_loss']
                ))
                print(f"Global Iteration {iteration + trainer_id}")
                print(pretty_print(stats))
            
            ray.get(log_tasks)
            
            # 定期保存模型
            if iteration % save_interval == 0:
                path = f'/home/<USER>/data/bc_models/BC_iter_{iteration}_model.pth{formatted_date}'
                ray.get(trainers[0].save_checkpoint.remote(path))
        
        # 保存最终模型
        final_path = f'/home/<USER>/data/bc_models/BC_final_model.pth{formatted_date}'
        ray.get(trainers[0].save_checkpoint.remote(final_path))
        
    except KeyboardInterrupt:
        print("\n训练被中断，正在保存模型...")
        path = f'/home/<USER>/data/bc_models/BC_interrupted_model.pth{formatted_date}'
        ray.get(trainers[0].save_checkpoint.remote(path))
    
    finally:
        ray.get([logger.close.remote() for logger in loggers])
        ray.shutdown()

if __name__ == "__main__":
    main()
