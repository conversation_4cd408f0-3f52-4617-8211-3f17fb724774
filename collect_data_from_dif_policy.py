from ray.rllib.evaluation.sample_batch_builder import SampleBatchBuilder
from ray.rllib.offline.json_writer import JsonWriter
import os
from metadrive.envs.gym_wrapper import createGymWrapper
from metadrive.envs.metadrive_env import MetaDriveEnv

from metadrive.policy.expert_policy import ExpertPolicy
from metadrive.policy.idm_policy import IDMPolicy

class DataCollector:

    def __init__(self, arr_episode, drive_policy, use_render):
        self.config = {
            'crash_vehicle_done': True,
            "use_render": use_render,
            'map':'O',
            # "data_directory": '/home/<USER>/data/scenes/raw_scenes_500' ,# "/home/<USER>/data/scenes/turn_scenes",
            "num_scenarios": 1000,
            'out_of_route_done': True,
            # 'horizon':91,
            "agent_policy": drive_policy,  # 直接在这里设置专家策略
            "traffic_density": 0.1, 
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50, "num_others": 3},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            }
        }
        
        self.use_render = use_render
        self.env = self._create_env()
        self.batch_builder = SampleBatchBuilder()
        # 确保bc_data目录存在
        os.makedirs('data/bc_data', exist_ok=True)

        self.writer = JsonWriter(os.path.join('data/bc_data', str(arr_episode)), compress_columns=[])
        self.episode = 0
        self.data_buffer = []  # 用于缓存数据的列表
        self.arr_episode = arr_episode
        # 记录成功通过的场景
        self.success_scenes = []

    def _create_env(self):
        env = createGymWrapper(MetaDriveEnv)(config=self.config)
        return env

    def collect_data(self):
        obs = self.env.reset()
        obs_list = []
        action_list = []
        arrive_num = 0
        fail_num = 0
        all_step = 0
        arr_step = 0
        reward_total = 0
        while arrive_num < self.arr_episode:
            all_step += 1
            # 直接使用step传入动作
            action = [0.0, 0.0]  # 专家策略会在环境内部被调用
            new_obs, reward, done, info = self.env.step(action)
            reward_total += reward
            obs = new_obs
            obs_list.append(obs)
            # print(info)
            action = info['action']
            
            print (action)
            action_list.append(action)


            if info.get('out_of_road', True):
                fail_num += 1
                obs_list = []
                action_list = []
                print(f"Failed at scene: {self.env.current_seed}")
            if info.get('arrive_dest', True):
                # 记录成功通过的场景
                current_scene = self.env.current_seed
                self.success_scenes.append(current_scene)

                for obs, action in zip(obs_list, action_list):
                    self.batch_builder.add_values(obs=obs, actions=action)
                arrive_num += 1
                arr_step += len(obs_list)
                obs_list.clear()
                action_list.clear()
                success_rate = arrive_num/(arrive_num+fail_num) * 100
                print(f'到达目标! 当前成功率: {success_rate:.2f}%  (成功:{arrive_num} 失败:{fail_num}),arr_step:{arr_step}')
            if done:
                obs = self.env.reset()
            
            if self.use_render:
                self.env.render()
            
            # print(f'all_step:{all_step},arr_step:{arr_step},arrive_num:{arrive_num},fail_num:{fail_num},arrive_rate:{arrive_num/(arrive_num+fail_num+0.1)},reward_total:{reward_total},mean_eposide_reward:{reward_total/(arrive_num+fail_num+0.1)}')
        
        # 打印最终统计信息
        final_success_rate = arrive_num/(arrive_num+fail_num) * 100
        print("\n========== 最终统计 ==========")
        print(f"总成功率: {final_success_rate:.2f}%")
        print(f"总尝试次数: {arrive_num+fail_num}")
        print(f"成功次数: {arrive_num}")
        print(f"失败次数: {fail_num}")
        print("============================\n")
        self.writer.write(self.batch_builder.build_and_reset())

if __name__ == "__main__":
    collector = DataCollector(drive_policy=IDMPolicy, use_render=False, arr_episode=12)
    collector.collect_data()
