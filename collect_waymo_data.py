
from ray.rllib.evaluation.sample_batch_builder import <PERSON><PERSON><PERSON>atch<PERSON>uilder
from ray.rllib.offline.json_writer import JsonWriter
import os
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy
from metadrive.envs.gym_wrapper import createGymWrapper
import random
from metadrive.policy.expert_policy import ExpertPolicy
from metadrive.policy.lange_change_policy import LaneChangePolicy

class DataCollector:
    """
    Collects data from different driving policies to generate offline datasets
    for behavior cloning or other offline learning methods.
    """

    def __init__(self, arr_episode,drive_policy,use_render):
        self.config = MetaDriveEnv.default_config()
        self.drive_policy = drive_policy
        self.use_render = use_render
        self.env = self._create_env()
        self.batch_builder = SampleBatchBuilder()
        self.writer = JsonWriter(os.path.join(f'./bc_data/{arr_episode}'),compress_columns=[])  # ,compress_columns=['obs','actions']
        self.episode = 0
        self.data_buffer = []  # 用于缓存数据的列表
        self.arr_episode = arr_episode


    def _create_env(self):
        print(self.config)
        self.config.update({
            # 'decision_repeat':2, 
            "use_render": self.use_render,
            # "manual_control": True,
            # 'use_lateral_reward': True, 
            # "horizon":5000,
            "traffic_density": 0.1,
            "num_scenarios":400, # 线路数量
            "start_seed": random.randint(0,500),
            # "random_lane_width":False,
            # "random_agent_model":False,
            # "map":'O',
            'random_spawn_lane_index': True,
            'agent_configs':{
                'default_agent':{
                    # 'spawn_lane_index': ('>', '>>', 2),
                    # 'max_speed_km_h': 15,
                    # 'spawn_lateral':random.uniform(-2, 2),
                    }
                },
            'vehicle_config': {
                # 'no_wheel_friction': True, 
                # "vehicle_model": "xl",
                # 'show_dest_mark': True, 
                # 'show_line_to_navi_mark': True,
                # 'spawn_velocity': [random.uniform(0, 5),random.uniform(0, 1)],
                # 'spawn_position_heading': [[10,0],random.uniform(0,10)], 
                # 'show_lidar': True, 
                # 'random_color': True,
                "lidar": {"num_lasers": 30, "distance": 50},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}


                }
            })
        if self.drive_policy == "IDMPolicy":
            print("IDM Policy selected")
            self.config["agent_policy"] = IDMPolicy
        elif self.drive_policy == "ExpertPolicy":
            print("ExpertPolicy selected")
            self.config["agent_policy"] = ExpertPolicy 
        elif self.drive_policy == "Manual Control":
            print("Manual Control selected")
            self.config["manual_control"] = True
        else:
            raise ValueError(f"Unknown drive policy: {self.drive_policy}")
        env = createGymWrapper(MetaDriveEnv)(config=self.config)
        print("Environment created successfully")
        return env

    def collect_data(self):
        obs = self.env.reset()
        obs_list = []
        action_list = []
        arrive_num = 0
        fail_num = 0
        all_step = 0
        arr_step = 0
        reward_total = 0
        while arrive_num < self.arr_episode:
            all_step += 1
            action = self.env.action_space.sample()  # 不起作用
            new_obs, reward, done, info = self.env.step(action)
            reward_total += reward
            obs = new_obs #[:19]
            obs_list.append(obs)
            action_list.append(info['action'])
            # print(f'info:{info["step_reward"]}')
            if info.get('out_of_road',True):
                fail_num += 1
                obs_list = []
                action_list = []
            if info.get('arrive_dest', True):
                for obs, action in zip(obs_list, action_list):
                    self.batch_builder.add_values(obs=obs, actions=action)
                arrive_num += 1
                arr_step += len(obs_list)
                obs_list.clear()
                action_list.clear()
            if done:
                obs = self.env.reset()
                
            # if(all_step % 1000 == 0):
            # print(info['velocity'])
            # print(info)
            # print(f'info:{info}')
            print(f'all_step:{all_step},arr_step:{arr_step},arrive_num:{arrive_num},fail_num:{fail_num},arrive_rate:{arrive_num/(arrive_num+fail_num+0.1)},reward_total:{reward_total},mean_eposide_reward:{reward_total/(arrive_num+fail_num+0.1)}')
        self.batch_builder.add_values(arr_num=arrive_num,arr_step=arr_step)
        self.writer.write(self.batch_builder.build_and_reset())
if __name__ == "__main__":
    collector = DataCollector(drive_policy="IDMPolicy", use_render=False,arr_episode=1)  # ExpertPolicy IDMPolicy
    collector.collect_data()