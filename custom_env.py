from metadrive.envs.metadrive_env import MetaDriveEnv
# from load_model import MetaDriveEnvWrapper
from typing import Union, Dict, AnyStr
import numpy as np
from metadrive.utils.config import Config
from gym.spaces import Box
from ray.rllib.algorithms.ppo import PPO

"""
调用示例:
from custom_env import Custom_env
def env_creator(self, env_config):
    env_config = dict(
        map="O",
        # 模型路径
        checkpoint_path='/home/<USER>/rl/RL-IL/bc_models/BC_iteration_2999_model.pth2024-12-31-11-00-05/checkpoint_003000',
        # 停止参数
        stop_bc_step_num=1e4,
    )
    env = createGymWrapper(Custom_env)(config=env_config)
    return env
"""

class Custom_env(MetaDriveEnv):
    @classmethod
    def default_config(cls) -> Config:
        """
        1. 获取父类的默认配置
        2. 加入自定义键
        3. 返回该新配置
        """
        cfg = super().default_config()
        # 设置默认参数,不然直接在训练程序直接加入参数会报错
        cfg.update(
            {
                "stop_bc_step_num": 10000,
                "checkpoint_path": "/home/<USER>/rl/RL-IL/bc_models/BC_iteration_2999_model.pth2024-12-31-11-00-05/checkpoint_003000"
            },
            allow_add_new_key=True
        )
        return cfg

    def __init__(self, config=None):
        # 如果外部传进来 config，就先和本类的 default_config 合并
        default_cfg = self.default_config()
        if config is not None:
            default_cfg.update(config, allow_add_new_key=True)
        config = default_cfg
        
        # 调用父类构造
        super().__init__(config)

        # 读取自定义键
        self.stop_bc_step_num = self.config["stop_bc_step_num"]
        self.checkpoint_path = self.config["checkpoint_path"]
        self.step_num_total = 0
        
        
        self.my_observation_space = Box(low=-0.0, high=1.0, shape=(259,), dtype=np.float32)
        self.my_action_space = Box(low=-1.0, high=1.0, shape=(2,), dtype=np.float32)
        self.trainer = self.load_pretrained_policy(self.checkpoint_path, self.my_observation_space, self.my_action_space)

        # # 初始化模型
        # self.model_wrapper = self._init_model()
        
        
    def load_pretrained_policy(self,checkpoint_path, observation_space, action_space):
        """
        从 checkpoint 文件加载辅助模型，并返回 PPOTrainer 实例
        """
        # 创建 PPO 配置
        config = {
            "env": None,
            "framework": "torch",
            "num_workers": 0,  # 不使用worker
            "explore": False,
            "observation_space": observation_space,
            "action_space": action_space,
        }

        # 初始化 PPOTrainer
        trainer = PPO(config=config)

        try:
            # 加载 checkpoint 文件
            trainer.restore(checkpoint_path)
            print(f"辅助模型加载成功：{checkpoint_path}")
        except Exception as e:
            print(f"加载辅助模型失败：{e}")
            raise e

        return trainer
        
        
    # def _init_model(self):
    #     wrapper = MetaDriveEnvWrapper(
    #         checkpoint_path=self.checkpoint_path,
    #         # use_render=False,
    #         # test_episode=100
    #     )
    #     wrapper.setup()
    #     return wrapper 
    def step(self, actions: Union[Union[np.ndarray, list], Dict[AnyStr, Union[list, np.ndarray]], int]):
        self.step_num_total += 1
        obs_obj = self.observations['default_agent']
        full_obs = obs_obj.observe(self.agents['default_agent'])
        # if self.step_num_total %1000 ==  1:
        #     print(f'RL actions:{actions}')
        # if self.step_num_total < self.stop_bc_step_num:
            # prob = max(0, 1 - self.step_num_total/10000)  # 线性减少概率
            # if np.random.random() < prob:
            #     actions = self.model_wrapper.algo.compute_single_action(full_obs)
            # actions = self.model_wrapper.algo.compute_single_action(full_obs)
        # BC_actions = self.trainer.compute_single_action(full_obs)
            
        # if self.step_num_total %1000 ==  1:
        #     if actions.all() == BC_actions.all():
        #         print('Using BC')
        #     else:
        #         print('Using RL')
        actions = self._preprocess_actions(actions)
        engine_info = self._step_simulator(actions)
        while self.in_stop:
            self.engine.taskMgr.step()
        return self._get_step_return(actions, engine_info=engine_info)
    
    # def step(self, actions: Union[Union[np.ndarray, list], Dict[AnyStr, Union[list, np.ndarray]], int]):
    #     actions = self._preprocess_actions(actions)  # preprocess environment input
    #     engine_info = self._step_simulator(actions)  # step the simulation
    #     while self.in_stop:
    #         self.engine.taskMgr.step()  # pause simulation
    #     return self._get_step_return(actions, engine_info=engine_info)  # collect observation, reward, termination