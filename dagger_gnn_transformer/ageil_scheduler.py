#!/usr/bin/env python3
"""
AGEIL自适应专家策略调度器
相比传统DAGGER的固定衰减，根据性能反馈动态调整专家策略使用比例
"""

import numpy as np
from typing import Optional, List
import torch

class AdaptiveExpertScheduler:
    """
    自适应专家策略调度器 - AGEIL的核心创新
    根据模型性能、损失趋势等动态调整专家策略使用比例
    """
    
    def __init__(self, 
                 max_beta: float = 1.0,
                 min_beta: float = 0.1,
                 performance_threshold: float = 0.7,
                 loss_window_size: int = 5,
                 smooth_factor: float = 0.2):
        """
        初始化自适应调度器
        
        Args:
            max_beta: 最大专家策略比例
            min_beta: 最小专家策略比例  
            performance_threshold: 性能阈值，低于此值增加专家指导
            loss_window_size: 损失趋势分析窗口大小
            smooth_factor: 平滑因子，避免剧烈波动
        """
        self.max_beta = max_beta
        self.min_beta = min_beta
        self.performance_threshold = performance_threshold
        self.loss_window_size = loss_window_size
        self.smooth_factor = smooth_factor
        
        # 历史记录
        self.loss_history = []
        self.performance_history = []
        self.beta_history = []
        
    def calculate_adaptive_beta(self, 
                               iteration: int,
                               total_iterations: int,
                               current_loss: Optional[float] = None,
                               current_performance: Optional[float] = None) -> float:
        """
        计算自适应的beta值
        
        Args:
            iteration: 当前迭代次数
            total_iterations: 总迭代次数
            current_loss: 当前损失值
            current_performance: 当前性能指标(路线完成率等)
            
        Returns:
            调整后的beta值
        """
        # 1. 基础保守衰减 (比传统DAGGER更保守)
        progress = iteration / total_iterations
        base_beta = self.max_beta * (1 - progress) ** 0.3  # 指数0.3比线性衰减更保守
        
        # 2. 性能反馈调整
        performance_adjustment = 0.0
        if current_performance is not None:
            self.performance_history.append(current_performance)
            
            # 性能低于阈值，增加专家指导
            if current_performance < self.performance_threshold:
                performance_adjustment = +0.2
            
            # 性能下降趋势，增加专家指导
            if len(self.performance_history) >= 3:
                recent_trend = np.mean(self.performance_history[-3:]) - np.mean(self.performance_history[-6:-3])
                if recent_trend < -0.05:  # 性能下降5%以上
                    performance_adjustment += 0.15
        
        # 3. 损失趋势调整
        loss_adjustment = 0.0
        if current_loss is not None:
            self.loss_history.append(current_loss)
            
            # 损失上升趋势，增加专家指导
            if len(self.loss_history) >= self.loss_window_size:
                recent_losses = self.loss_history[-self.loss_window_size:]
                if len(recent_losses) >= 3:
                    # 计算损失趋势
                    trend = np.polyfit(range(len(recent_losses)), recent_losses, 1)[0]
                    if trend > 0:  # 损失上升
                        loss_adjustment = +0.15
        
        # 4. 平滑处理，避免剧烈波动
        raw_beta = base_beta + performance_adjustment + loss_adjustment
        
        # 约束到合理范围
        raw_beta = np.clip(raw_beta, self.min_beta, self.max_beta)
        
        # 与历史值平滑
        if len(self.beta_history) > 0:
            smoothed_beta = (1 - self.smooth_factor) * self.beta_history[-1] + self.smooth_factor * raw_beta
        else:
            smoothed_beta = raw_beta
            
        final_beta = np.clip(smoothed_beta, self.min_beta, self.max_beta)
        self.beta_history.append(final_beta)
        
        return final_beta
    
    def get_schedule_info(self) -> dict:
        """获取调度器状态信息"""
        return {
            'current_beta': self.beta_history[-1] if self.beta_history else self.max_beta,
            'loss_trend': np.polyfit(range(len(self.loss_history)), self.loss_history, 1)[0] if len(self.loss_history) >= 3 else 0,
            'performance_trend': np.mean(self.performance_history[-3:]) - np.mean(self.performance_history[-6:-3]) if len(self.performance_history) >= 6 else 0,
            'beta_stability': np.std(self.beta_history[-5:]) if len(self.beta_history) >= 5 else 0
        }

class PrioritizedDataBuffer:
    """
    优先级经验回放缓冲区 - AGEIL的第二个创新
    根据样本重要性进行优先级采样
    """
    
    def __init__(self, max_size: int = 50000, alpha: float = 0.6, beta: float = 0.4):
        """
        初始化优先级缓冲区
        
        Args:
            max_size: 最大缓冲区大小
            alpha: 优先级指数，控制优先级采样强度
            beta: 重要性权重指数，用于偏差校正
        """
        self.max_size = max_size
        self.alpha = alpha
        self.beta = beta
        self.buffer = []
        self.priorities = []
        self.pos = 0
        
    def add(self, experience: dict, priority: float = 1.0):
        """添加经验到缓冲区"""
        if len(self.buffer) < self.max_size:
            self.buffer.append(experience)
            self.priorities.append(priority)
        else:
            self.buffer[self.pos] = experience
            self.priorities[self.pos] = priority
            self.pos = (self.pos + 1) % self.max_size
            
    def sample(self, batch_size: int):
        """基于优先级采样经验"""
        if len(self.buffer) == 0:
            return [], []
            
        # 计算采样概率
        priorities = np.array(self.priorities)
        probs = priorities ** self.alpha
        probs = probs / np.sum(probs)
        
        # 采样
        indices = np.random.choice(len(self.buffer), size=min(batch_size, len(self.buffer)), p=probs, replace=False)
        
        # 计算重要性权重
        weights = (len(self.buffer) * probs[indices]) ** (-self.beta)
        weights = weights / np.max(weights)  # 归一化
        
        experiences = [self.buffer[i] for i in indices]
        
        return experiences, weights, indices
    
    def update_priorities(self, indices: List[int], priorities: List[float]):
        """更新样本优先级"""
        for idx, priority in zip(indices, priorities):
            self.priorities[idx] = priority
            
    def __len__(self):
        return len(self.buffer)

class MultiObjectiveLoss:
    """
    多目标损失函数 - AGEIL的第三个创新
    平衡模仿、安全、平滑等多个目标
    """
    
    def __init__(self, 
                 imitation_weight: float = 1.0,
                 safety_weight: float = 0.1,
                 smoothness_weight: float = 0.05,
                 consistency_weight: float = 0.02):
        """
        初始化多目标损失
        
        Args:
            imitation_weight: 模仿损失权重
            safety_weight: 安全损失权重
            smoothness_weight: 平滑损失权重  
            consistency_weight: 一致性损失权重
        """
        self.imitation_weight = imitation_weight
        self.safety_weight = safety_weight
        self.smoothness_weight = smoothness_weight
        self.consistency_weight = consistency_weight
        
    def compute_loss(self, 
                    predicted_actions: torch.Tensor,
                    expert_actions: torch.Tensor,
                    prev_actions: Optional[torch.Tensor] = None,
                    safety_features: Optional[torch.Tensor] = None) -> dict:
        """
        计算多目标损失
        
        Args:
            predicted_actions: 预测动作
            expert_actions: 专家动作
            prev_actions: 前一步动作(用于平滑性)
            safety_features: 安全特征(距离、速度等)
            
        Returns:
            包含各项损失的字典
        """
        losses = {}
        
        # 1. 基础模仿损失
        imitation_loss = torch.nn.functional.mse_loss(predicted_actions, expert_actions)
        losses['imitation_loss'] = imitation_loss
        
        # 2. 安全损失 (惩罚危险动作)
        safety_loss = torch.tensor(0.0, device=predicted_actions.device)
        if safety_features is not None:
            # 基于安全特征计算安全损失
            # 例如：距离太近时惩罚大油门，速度太快时惩罚小制动
            distances = safety_features[:, 0]  # 假设第一个特征是距离
            speeds = safety_features[:, 1]     # 假设第二个特征是速度
            
            # 距离近时惩罚大油门
            close_mask = distances < 0.3
            throttle_actions = predicted_actions[:, 1]  # 假设第二个动作是油门
            safety_loss += torch.mean(torch.relu(throttle_actions[close_mask] - 0.5) ** 2)
            
            # 速度快时惩罚小制动
            fast_mask = speeds > 0.8
            brake_actions = -torch.relu(-throttle_actions)  # 负油门表示制动
            safety_loss += torch.mean(torch.relu(0.3 - brake_actions[fast_mask]) ** 2)
            
        losses['safety_loss'] = safety_loss
        
        # 3. 平滑损失 (动作变化平滑)
        smoothness_loss = torch.tensor(0.0, device=predicted_actions.device)
        if prev_actions is not None:
            action_diff = predicted_actions - prev_actions
            smoothness_loss = torch.mean(action_diff ** 2)
        losses['smoothness_loss'] = smoothness_loss
        
        # 4. 一致性损失 (与专家策略的一致性)
        consistency_loss = torch.mean(torch.abs(predicted_actions - expert_actions))
        losses['consistency_loss'] = consistency_loss
        
        # 5. 总损失
        total_loss = (
            self.imitation_weight * imitation_loss +
            self.safety_weight * safety_loss +
            self.smoothness_weight * smoothness_loss +
            self.consistency_weight * consistency_loss
        )
        losses['total_loss'] = total_loss
        
        return losses
    
    def update_weights(self, iteration: int, total_iterations: int):
        """动态调整损失权重"""
        progress = iteration / total_iterations
        
        # 早期更注重模仿，后期更注重安全和平滑
        self.imitation_weight = 1.0 - 0.2 * progress
        self.safety_weight = 0.1 + 0.1 * progress
        self.smoothness_weight = 0.05 + 0.05 * progress
