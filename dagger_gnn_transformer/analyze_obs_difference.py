#!/usr/bin/env python3
"""
分析普通观测和图像观测的差异
"""

import numpy as np
from metadrive import MetaDriveEnv
import matplotlib.pyplot as plt

def analyze_observation_difference():
    """详细分析两种观测模式的差异"""
    
    # 基础配置
    base_config = {
        "num_scenarios": 1,
        "traffic_density": 0.1,
        "start_seed": 10,
        "map": "S",
        "vehicle_config": {
            "lidar": {"num_lasers": 30, "distance": 50},
            "side_detector": {"num_lasers": 30},
            "lane_line_detector": {"num_lasers": 12}
        }
    }
    
    print("=== 分析普通观测模式 ===")
    config_normal = base_config.copy()
    config_normal["use_render"] = False
    
    env_normal = MetaDriveEnv(config_normal)
    obs_normal, info_normal = env_normal.reset(seed=10)
    
    print(f"普通观测形状: {obs_normal.shape}")
    print(f"普通观测范围: [{obs_normal.min():.3f}, {obs_normal.max():.3f}]")
    print(f"普通观测前10维: {obs_normal[:10]}")
    print(f"普通观测后10维: {obs_normal[-10:]}")
    
    # 分析传感器数据分布
    print("\n传感器数据分析 (88维):")
    side_detector = obs_normal[0:30]
    lane_line = obs_normal[36:48]
    lidar = obs_normal[58:88]
    
    print(f"Side detector (0:30): 范围[{side_detector.min():.3f}, {side_detector.max():.3f}], 均值{side_detector.mean():.3f}")
    print(f"Lane line (36:48): 范围[{lane_line.min():.3f}, {lane_line.max():.3f}], 均值{lane_line.mean():.3f}")
    print(f"Lidar (58:88): 范围[{lidar.min():.3f}, {lidar.max():.3f}], 均值{lidar.mean():.3f}")
    
    # 执行几步，看看数据变化
    print("\n执行几步后的观测变化:")
    for i in range(3):
        action = [0.0, 0.5]  # 直行
        obs_normal, _, _, _, _ = env_normal.step(action)
        print(f"Step {i+1}: 范围[{obs_normal.min():.3f}, {obs_normal.max():.3f}], 前5维: {obs_normal[:5]}")
    
    env_normal.close()
    
    print("\n" + "="*60)
    print("=== 分析图像观测模式 ===")
    
    config_image = base_config.copy()
    config_image.update({
        "image_observation": True,
        "vehicle_config": {
            **base_config["vehicle_config"],
            "image_source": "main_camera"
        },
        "norm_pixel": False,
        "use_render": True,
        "stack_size": 1,
    })
    
    env_image = MetaDriveEnv(config_image)
    obs_image, info_image = env_image.reset(seed=10)
    
    print(f"图像观测类型: {type(obs_image)}")
    print(f"图像观测键: {obs_image.keys()}")
    
    state_vector = obs_image['state']
    print(f"状态向量形状: {state_vector.shape}")
    print(f"状态向量范围: [{state_vector.min():.3f}, {state_vector.max():.3f}]")
    print(f"状态向量前10维: {state_vector[:10]}")
    print(f"状态向量后10维: {state_vector[-10:]}")
    
    # 尝试找到传感器数据的对应关系
    print("\n尝试映射传感器数据 (58维):")
    
    # 方法1: 假设前面是side_detector相关数据
    if len(state_vector) >= 30:
        potential_side = state_vector[:30]
        print(f"潜在side detector (0:30): 范围[{potential_side.min():.3f}, {potential_side.max():.3f}], 均值{potential_side.mean():.3f}")
    
    # 方法2: 假设中间是lane_line相关数据
    if len(state_vector) >= 42:
        potential_lane = state_vector[30:42]
        print(f"潜在lane line (30:42): 范围[{potential_lane.min():.3f}, {potential_lane.max():.3f}], 均值{potential_lane.mean():.3f}")
    
    # 方法3: 查看剩余数据
    if len(state_vector) > 42:
        remaining = state_vector[42:]
        print(f"剩余数据 (42:): 长度{len(remaining)}, 范围[{remaining.min():.3f}, {remaining.max():.3f}], 均值{remaining.mean():.3f}")
    
    # 执行几步，看看数据变化
    print("\n执行几步后的状态变化:")
    for i in range(3):
        action = [0.0, 0.5]  # 直行
        obs_image, _, _, _, _ = env_image.step(action)
        state_vector = obs_image['state']
        print(f"Step {i+1}: 范围[{state_vector.min():.3f}, {state_vector.max():.3f}], 前5维: {state_vector[:5]}")
    
    env_image.close()
    
    print("\n" + "="*60)
    print("=== 对比分析 ===")
    
    # 重新创建环境进行对比
    env_normal = MetaDriveEnv(config_normal)
    env_image = MetaDriveEnv(config_image)
    
    obs_normal, _ = env_normal.reset(seed=10)
    obs_image, _ = env_image.reset(seed=10)
    
    state_58 = obs_image['state']
    
    print("尝试找到58维状态与88维观测的对应关系:")
    
    # 比较数据分布
    print(f"\n88维观测统计:")
    print(f"  均值: {obs_normal.mean():.3f}, 标准差: {obs_normal.std():.3f}")
    print(f"  最小值: {obs_normal.min():.3f}, 最大值: {obs_normal.max():.3f}")
    
    print(f"\n58维状态统计:")
    print(f"  均值: {state_58.mean():.3f}, 标准差: {state_58.std():.3f}")
    print(f"  最小值: {state_58.min():.3f}, 最大值: {state_58.max():.3f}")
    
    # 查看是否有相似的数值模式
    print(f"\n数值模式对比:")
    print(f"88维前10维: {obs_normal[:10]}")
    print(f"58维前10维: {state_58[:10]}")
    
    env_normal.close()
    env_image.close()

if __name__ == "__main__":
    analyze_observation_difference()
