#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2024/12/9 15:49
# <AUTHOR> <PERSON><PERSON><PERSON>
# @File    : behavior_clone.py

import os
import time
import numpy as np
from tqdm import tqdm
from ray.rllib.algorithms.marwil import MARWILConfig
from ray.tune.registry import register_env
from ray.rllib.policy.policy import Policy
import ray
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.envs.gym_wrapper import createGymWrapper
from torch.utils.tensorboard import SummaryWriter
import torch

# 设置PyTorch的随机种子以提高稳定性
torch.manual_seed(42)
torch.cuda.manual_seed_all(42)

# 禁用一些可能导致深拷贝问题的功能
os.environ["RAY_DISABLE_IMPORT_WARNING"] = "1"


def env_creator(env_config):
    # 使用与数据收集时相同的配置，确保观测空间维度一致
    config = {
        'crash_vehicle_done': True,
        "use_render": False,
        'map':'CXO',
        "num_scenarios": 1,
        "start_seed": 4,
        'out_of_route_done': True,
        "traffic_density": 0.1,
        "vehicle_config": {
            "lidar": {"num_lasers": 30, "distance": 50},
            "side_detector": {"num_lasers": 30},
            "lane_line_detector": {"num_lasers": 12}
        }
    }
    env = createGymWrapper(MetaDriveEnv)(config=config)
    return env


register_env('metadrive-env', env_creator)


class BehaviorCloneTrainer:
    def __init__(self, bc_data_path, train_batch_size=256, iteration_num=200, log_dir='./tensorboard_logs'):
        self.bc_data_path = bc_data_path
        self.train_batch_size = train_batch_size
        self.iteration_num = iteration_num
        self.log_dir = log_dir
        self.logger = TrainingLogger(log_dir=self.log_dir)
        self.setup_environment()
        self.setup_config()
        self.algo = self.config.build()

    def setup_environment(self):
        self.game = env_creator({})

    def setup_config(self):
        # 创建 MARWILConfig 实例，设置beta=0.0实现行为克隆
        config = MARWILConfig()

        # 配置环境
        config.environment(
            disable_env_checking=False,
            env='metadrive-env',
            observation_space=self.game.observation_space,
            action_space=self.game.action_space,
        )

        # 配置检查点
        config.checkpointing(export_native_model_files=True)

        # 配置离线数据
        config.offline_data(input_=self.bc_data_path)

        # 配置深度学习框架
        config.framework('torch')

        # 配置训练参数
        config.training(
            lr=1e-5,
            beta=0.0,  # 设置为0实现纯行为克隆
            grad_clip=1.0,
            train_batch_size=self.train_batch_size,
            bc_logstd_coeff=1
        )

        # 配置评估参数 - 禁用评估以避免深拷贝问题
        config.evaluation(
            evaluation_num_workers=0,  # 设置为0禁用评估
            evaluation_duration=1,     # 必须>0，但由于num_workers=0所以不会实际运行
            evaluation_interval=None   # 禁用自动评估
        )

        # 配置资源
        config.resources(
            num_gpus=1,
            num_cpus_per_worker=1,  # 减少CPU使用
            num_gpus_per_worker=0  # 设置为0避免GPU分配问题
        )

        # 配置rollouts以避免深拷贝问题
        config.rollouts(
            num_rollout_workers=0,  # 使用本地worker避免多进程问题
            create_env_on_local_worker=True
        )

        # 配置调试参数
        config.debugging(log_level='ERROR')

        # 将配置赋值给实例变量
        self.config = config

    def train(self):
        for iteration in tqdm(range(self.iteration_num)):
            result = self.algo.train()
            learner_stats = result['info']['learner']['default_policy']['learner_stats']
            total_loss = learner_stats['total_loss']
            policy_loss = learner_stats['policy_loss']
            print(f'----- iteration={iteration}, total_loss={total_loss:.2f}, policy_loss={policy_loss:.2f}')
            self.logger.log_metrics(iteration, total_loss, policy_loss)
        # 保存模型
        save_path = f'/home/<USER>/rl/RL-IL/dagger_gnn_transformer/compared_method/BC_MLP/bc_models/BC_iteration_{iteration}_model.pth'
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        file_name = self.algo.save(save_path)
        print(f'模型已保存到: {file_name}')
        self.logger.close()


class TrainingLogger:
    def __init__(self, log_dir='./tensorboard_logs'):
        # 初始化TensorBoard写入器
        self.writer = SummaryWriter(log_dir=log_dir,flush_secs=10)
    
    def log_metrics(self, iteration, total_loss, policy_loss):
        # 记录总损失和策略损失到TensorBoard
        self.writer.add_scalar('Loss/Total', total_loss, iteration)   # total_loss\policy_loss  公式    自定义的loss 用的是什么函数  预测值是什么action a1 a2?  算loss,要不要换lossfuction. 模仿->强化
        """
        定量定性评价模仿学习
        1. 定量评价
            分析的数据对象:loss -> 收敛的趋势
            评价指标: 外部指标  车成功到达终点次数, 时长,路面边界.
            
            加载好模仿模型后用到reward /home/<USER>/rl/RL-IL/RL-IL/load_model.py  加载模仿学习的模型
        2. 定性评价
            可视化  
        """
        self.writer.add_scalar('Loss/Policy', policy_loss, iteration)
    
    def close(self):
        # 关闭TensorBoard写入器
        self.writer.close()


if __name__ == "__main__":
    bc_data_path = '/home/<USER>/rl/RL-IL/data/bc_data/50/output-2025-07-16_23-04-11_worker-0_0.json'
    trainer = BehaviorCloneTrainer(bc_data_path=bc_data_path)
    trainer.train()