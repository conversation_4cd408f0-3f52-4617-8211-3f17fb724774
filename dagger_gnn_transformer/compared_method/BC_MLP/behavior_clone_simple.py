#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的行为克隆训练脚本，避免Ray RLlib的深拷贝问题
"""

import os
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm
import pickle

class BCDataset(Dataset):
    """行为克隆数据集"""
    
    def __init__(self, data_path):
        self.data = []
        self.load_data(data_path)
    
    def load_data(self, data_path):
        """加载JSON格式的数据"""
        print(f"正在加载数据: {data_path}")

        with open(data_path, 'r') as f:
            for line in f:
                try:
                    sample = json.loads(line.strip())
                    if 'obs' in sample and 'actions' in sample:
                        # 处理批量数据：obs和actions都是列表
                        obs_list = sample['obs']
                        actions_list = sample['actions']

                        # 确保obs和actions长度一致
                        min_len = min(len(obs_list), len(actions_list))

                        for i in range(min_len):
                            obs = np.array(obs_list[i], dtype=np.float32)
                            action = np.array(actions_list[i], dtype=np.float32)

                            # 检查数据维度
                            if obs.shape[0] == 88 and action.shape[0] == 2:
                                self.data.append((obs, action))
                            else:
                                print(f"跳过维度不匹配的数据: obs={obs.shape}, action={action.shape}")

                except json.JSONDecodeError as e:
                    print(f"JSON解析错误: {e}")
                    continue

        print(f"成功加载 {len(self.data)} 个样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        obs, action = self.data[idx]
        return torch.from_numpy(obs), torch.from_numpy(action)

class SimpleMLP(nn.Module):
    """简单的MLP网络用于行为克隆"""
    
    def __init__(self, obs_dim, action_dim, hidden_dims=[256, 256]):
        super().__init__()
        
        layers = []
        input_dim = obs_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            input_dim = hidden_dim
        
        layers.append(nn.Linear(input_dim, action_dim))
        
        self.network = nn.Sequential(*layers)
        
        # 初始化权重
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.zeros_(m.bias)
    
    def forward(self, x):
        return self.network(x)

class SimpleBCTrainer:
    """简化的行为克隆训练器"""
    
    def __init__(self, data_path, obs_dim=88, action_dim=2, 
                 batch_size=256, lr=1e-4, device='cuda'):
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        print(f"使用设备: {self.device}")
        
        # 加载数据
        self.dataset = BCDataset(data_path)
        self.dataloader = DataLoader(
            self.dataset, 
            batch_size=batch_size, 
            shuffle=True, 
            num_workers=4
        )
        
        # 创建模型
        self.model = SimpleMLP(obs_dim, action_dim).to(self.device)
        
        # 优化器和损失函数
        self.optimizer = optim.Adam(self.model.parameters(), lr=lr, weight_decay=1e-4)
        self.criterion = nn.MSELoss()
        
        # TensorBoard
        self.writer = SummaryWriter('./tensorboard_logs/simple_bc')
        
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters())}")
    
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for obs, actions in tqdm(self.dataloader, desc="训练中"):
            obs = obs.to(self.device)
            actions = actions.to(self.device)
            
            # 前向传播
            pred_actions = self.model(obs)
            loss = self.criterion(pred_actions, actions)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
        
        return total_loss / num_batches
    
    def train(self, num_epochs=100):
        """训练模型"""
        print(f"开始训练，共 {num_epochs} 个epoch")
        
        for epoch in range(num_epochs):
            avg_loss = self.train_epoch()
            
            # 记录到TensorBoard
            self.writer.add_scalar('Loss/Train', avg_loss, epoch)
            
            print(f"Epoch {epoch+1}/{num_epochs}, Loss: {avg_loss:.6f}")
            
            # 每10个epoch保存一次模型
            if (epoch + 1) % 10 == 0:
                self.save_model(f'./bc_models/simple_bc_epoch_{epoch+1}.pth')
        
        # 保存最终模型
        self.save_model('./bc_models/simple_bc_final.pth')
        self.writer.close()
        print("训练完成！")
    
    def save_model(self, path):
        """保存模型"""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'obs_dim': 88,
            'action_dim': 2
        }, path)
        print(f"模型已保存到: {path}")

if __name__ == "__main__":
    # 数据路径
    data_path = '/home/<USER>/rl/RL-IL/data/bc_data/50/output-2025-07-16_23-04-11_worker-0_0.json'
    
    # 检查数据文件是否存在
    if not os.path.exists(data_path):
        print(f"错误: 数据文件不存在: {data_path}")
        exit(1)
    
    # 创建训练器并开始训练
    trainer = SimpleBCTrainer(
        data_path=data_path,
        obs_dim=88,
        action_dim=2,
        batch_size=256,
        lr=1e-3
    )
    
    trainer.train(num_epochs=500)
