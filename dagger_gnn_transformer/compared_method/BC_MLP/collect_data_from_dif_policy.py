from ray.rllib.evaluation.sample_batch_builder import Sam<PERSON><PERSON>atchBuilder
from ray.rllib.offline.json_writer import JsonWriter
import os
from metadrive.envs.gym_wrapper import createGymWrapper
from metadrive.envs.metadrive_env import MetaDriveEnv

from metadrive.policy.expert_policy import ExpertPolicy
from metadrive.policy.idm_policy import IDMPolicy

# 创建专门用于NPC车辆的低速IDM策略
class NPCSlowIDMPolicy(IDMPolicy):
    """
    专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
    """
    NORMAL_SPEED = 10  # km/h
    MAX_SPEED = 10  # km/h
    CREEP_SPEED = 10  # km/h
    ACC_FACTOR = 0.5  # 降低加速度因子

    def __init__(self, control_object, random_seed):
        super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
        self.target_speed = self.NORMAL_SPEED

def patch_traffic_manager_for_slow_npc():
    """
    通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy
    这样只有NPC车辆会使用低速，自车保持正常速度
    """
    import metadrive.manager.traffic_manager as tm

    # 保存原始的add_policy方法
    original_add_policy = tm.PGTrafficManager.add_policy

    def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
        # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
        if policy_class == IDMPolicy:
            policy_class = NPCSlowIDMPolicy
        return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

    # 应用patch
    tm.PGTrafficManager.add_policy = patched_add_policy

class DataCollector:

    def __init__(self, arr_episode, drive_policy, use_render, use_slow_npc=True):
        # 如果启用低速NPC，应用猴子补丁
        if use_slow_npc:
            patch_traffic_manager_for_slow_npc()
            print("✅ 已将NPC车辆速度降低到10 km/h，自车保持正常速度")

        self.config = {
            "agent_policy": drive_policy,  # 直接在这里设置专家策略
            "out_of_road_done": True,
            "use_render": False,
            "num_scenarios": 10,
            "traffic_density": 0.1,
            "start_seed": 1,
            "map": "SSSSS",
            "accident_prob": 0.0,
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            },
            # 交通车辆配置
            "traffic_vehicle_config": {
                "show_navi_mark": False,
                "show_dest_mark": False,
                "enable_reverse": False,
                "show_lidar": False,
                "show_lane_line_detector": False,
                "show_side_detector": False,
            }
            
            
        }
        
        self.use_render = use_render
        self.env = self._create_env()
        self.batch_builder = SampleBatchBuilder()
        # 确保bc_data目录存在
        os.makedirs('data/bc_data', exist_ok=True)

        self.writer = JsonWriter(os.path.join('data/bc_data', str(arr_episode)), compress_columns=[])
        self.episode = 0
        self.data_buffer = []  # 用于缓存数据的列表
        self.arr_episode = arr_episode
        # 记录成功通过的场景
        self.success_scenes = []

    def _create_env(self):
        env = createGymWrapper(MetaDriveEnv)(config=self.config)
        return env

    def collect_data(self):
        obs = self.env.reset()
        obs_list = []
        action_list = []
        arrive_num = 0
        fail_num = 0
        all_step = 0
        arr_step = 0
        reward_total = 0
        while arrive_num < self.arr_episode:
            all_step += 1
            # 直接使用step传入动作
            action = [0.0, 0.0]  # 专家策略会在环境内部被调用
            new_obs, reward, done, info = self.env.step(action)
            reward_total += reward
            obs = new_obs
            obs_list.append(obs)
            # print(info)
            action = info['action']
            
            print (action)
            action_list.append(action)


            if info.get('out_of_road', True):
                fail_num += 1
                obs_list = []
                action_list = []
                print(f"Failed at scene: {self.env.current_seed}")
            if info.get('arrive_dest', True):
                # 记录成功通过的场景
                current_scene = self.env.current_seed
                self.success_scenes.append(current_scene)

                for obs, action in zip(obs_list, action_list):
                    self.batch_builder.add_values(obs=obs, actions=action)
                arrive_num += 1
                arr_step += len(obs_list)
                obs_list.clear()
                action_list.clear()
                success_rate = arrive_num/(arrive_num+fail_num) * 100
                print(f'到达目标! 当前成功率: {success_rate:.2f}%  (成功:{arrive_num} 失败:{fail_num}),arr_step:{arr_step}')
            if done:
                obs = self.env.reset()
            
            if self.use_render:
                self.env.render()
            
            # print(f'all_step:{all_step},arr_step:{arr_step},arrive_num:{arrive_num},fail_num:{fail_num},arrive_rate:{arrive_num/(arrive_num+fail_num+0.1)},reward_total:{reward_total},mean_eposide_reward:{reward_total/(arrive_num+fail_num+0.1)}')
        
        # 打印最终统计信息
        final_success_rate = arrive_num/(arrive_num+fail_num) * 100
        print("\n========== 最终统计 ==========")
        print(f"总成功率: {final_success_rate:.2f}%")
        print(f"总尝试次数: {arrive_num+fail_num}")
        print(f"成功次数: {arrive_num}")
        print(f"失败次数: {fail_num}")
        print("============================\n")
        self.writer.write(self.batch_builder.build_and_reset())

if __name__ == "__main__":
    collector = DataCollector(drive_policy=IDMPolicy, use_render=False, arr_episode=1, use_slow_npc=True)
    collector.collect_data()
