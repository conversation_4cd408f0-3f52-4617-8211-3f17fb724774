import ray
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.algorithms.bc import BCConfig

from ray.tune.registry import register_env
import os
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.envs.gym_wrapper import createGymWrapper
import warnings
import logging
import json

# 抑制警告
warnings.filterwarnings("ignore")
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
logging.getLogger('ray').setLevel(logging.ERROR)


class ModelTester:
    def __init__(self, checkpoint_path):
        self.checkpoint_path = checkpoint_path
        self.env_name = 'metadrive-env'
        self.algo = None
        self.env = None

        self.register_environment()
        ray.shutdown()
        ray.init()
        self.configure_algorithm()

    def register_environment(self):
        def env_creator(env_config):
            # 使用与训练时相同的配置，确保观测空间维度一致
            config = {
                'crash_vehicle_done': True,
                "use_render": True,  # 测试时开启渲染
                'map':'CXO',
                "num_scenarios": 1,
                "start_seed": 4,
                'out_of_route_done': True,
                "traffic_density": 0.1,
                "vehicle_config": {
                    "lidar": {"num_lasers": 30, "distance": 50},
                    "side_detector": {"num_lasers": 30},
                    "lane_line_detector": {"num_lasers": 12}
                }
            }
            return createGymWrapper(MetaDriveEnv)(config=config)

        register_env(self.env_name, env_creator)

    def configure_algorithm(self):
        config = (
            BCConfig()
            .environment(self.env_name, disable_env_checking=True)
            .framework('torch')
        )
        self.algo = config.build()
        self.load_checkpoint()

    def load_checkpoint(self):
        if os.path.exists(self.checkpoint_path):
            self.algo.restore(self.checkpoint_path)
            print(f"Model loaded from checkpoint: {self.checkpoint_path}")
        else:
            raise FileNotFoundError(f"Checkpoint path {self.checkpoint_path} not found.")

    def test_model(self, num_episodes=100):
        """测试模型性能，统计各项指标"""
        # 统计指标
        success_count = 0
        out_of_road_count = 0
        crash_count = 0
        total_steps = 0
        route_completion_rates = []
        episode_rewards = []

        print(f"开始测试BC模型，共{num_episodes}个回合...")

        # 创建一个环境实例，重复使用
        self.env = self.create_env()

        try:
            for episode in range(num_episodes):
                try:
                    obs = self.env.reset()
                    done = False
                    episode_step = 0
                    episode_reward = 0

                    while not done:
                        action = self.algo.compute_single_action(obs)
                        obs, reward, done, info = self.env.step(action)
                        episode_step += 1
                        episode_reward += reward

                        # 防止无限循环
                        if episode_step > 2000:
                            break

                    # 统计结果
                    total_steps += episode_step
                    episode_rewards.append(episode_reward)

                    # 获取环境信息
                    agent = self.env.agent

                    # 直接从环境的done_function检查结束原因
                    # MetaDrive环境的done_function会设置相应的标志
                    crash = False
                    out_of_road = False
                    arrive_dest = False

                    # 检查各种终止条件
                    if hasattr(agent, 'crash_vehicle') and agent.crash_vehicle:
                        crash = True
                    elif hasattr(agent, 'crash_object') and agent.crash_object:
                        crash = True
                    elif hasattr(agent, 'out_of_road') and agent.out_of_road:
                        out_of_road = True
                    elif hasattr(agent, 'arrive_dest') and agent.arrive_dest:
                        arrive_dest = True
                    else:
                        # 如果没有明确的标志，根据路线完成度和其他状态判断
                        if hasattr(agent, 'navigation'):
                            completion = agent.navigation.route_completion
                            if completion > 0.95:  # 接近完成，认为是成功
                                arrive_dest = True
                            elif not getattr(agent, 'on_lane', True):
                                out_of_road = True
                            else:
                                out_of_road = True  # 大部分情况下是出路
                        else:
                            out_of_road = True

                    # 计算路线完成度
                    if hasattr(agent, 'navigation'):
                        route_completion = agent.navigation.route_completion
                    else:
                        route_completion = 0.0

                    route_completion_rates.append(route_completion)

                    # 统计各种结束情况
                    if arrive_dest:
                        success_count += 1
                    elif crash:
                        crash_count += 1
                    elif out_of_road:
                        out_of_road_count += 1
                    else:
                        # 其他情况（如超时）
                        out_of_road_count += 1  # 默认归类为出路

                    # 打印进度
                    if (episode + 1) % 10 == 0:
                        print(f"已完成 {episode + 1}/{num_episodes} 回合 - 成功率: {success_count/(episode+1)*100:.1f}%")

                except Exception:
                    # 静默处理错误，记录为出路
                    route_completion_rates.append(0.0)
                    episode_rewards.append(0.0)
                    total_steps += 0
                    # 大部分错误情况都是出路
                    out_of_road_count += 1
                    continue

        finally:
            if hasattr(self, 'env') and self.env:
                self.env.close()

        # 计算统计结果
        success_rate = success_count / num_episodes * 100
        out_of_road_rate = out_of_road_count / num_episodes * 100
        crash_rate = crash_count / num_episodes * 100
        avg_steps = total_steps / num_episodes
        avg_route_completion = sum(route_completion_rates) / num_episodes * 100
        avg_reward = sum(episode_rewards) / num_episodes

        # 打印结果
        print("\n" + "="*60)
        print("BC模型测试结果统计")
        print("="*60)
        print(f"测试回合数: {num_episodes}")
        print(f"成功率: {success_rate:.2f}% ({success_count}/{num_episodes})")
        print(f"出路率: {out_of_road_rate:.2f}% ({out_of_road_count}/{num_episodes})")
        print(f"碰撞率: {crash_rate:.2f}% ({crash_count}/{num_episodes})")
        print(f"平均步长: {avg_steps:.2f}")
        print(f"平均路线完成度: {avg_route_completion:.2f}%")
        print(f"平均奖励: {avg_reward:.2f}")
        print("="*60)

        return {
            'success_rate': success_rate,
            'out_of_road_rate': out_of_road_rate,
            'crash_rate': crash_rate,
            'avg_steps': avg_steps,
            'avg_route_completion': avg_route_completion,
            'avg_reward': avg_reward,
            'total_episodes': num_episodes
        }

    @staticmethod
    def create_env():
        # 使用与训练时相同的配置，确保观测空间维度一致
        config = {
            'crash_vehicle_done': True,
            "use_render": False,  # 测试时关闭渲染以提高速度
            'map':'CXO',
            "num_scenarios": 1,
            "start_seed": 4,
            'out_of_route_done': True,
            "traffic_density": 0.1,
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            }
        }
        return createGymWrapper(MetaDriveEnv)(config=config)


if __name__ == "__main__":
    checkpoint = '/home/<USER>/rl/RL-IL/dagger_gnn_transformer/compared_method/BC_MLP/bc_models/BC_iteration_199_model.pth/checkpoint_000200'
    tester = ModelTester(checkpoint)

    # 测试100个回合并统计各项指标
    results = tester.test_model(num_episodes=100)

    # 可以将结果保存到文件
    import json
    with open('bc_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    print("\n测试结果已保存到 bc_test_results.json")
