#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
使用Ray RLlib的行为克隆训练脚本
"""

import os
import ray
import json
import numpy as np
from ray.rllib.algorithms.bc import BCConfig
from ray.rllib.algorithms.marwil import MARWILConfig
from ray.tune.registry import register_env
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy
import tempfile
import shutil
import gym
from gym import spaces

# 创建专门用于NPC车辆的低速IDM策略
class NPCSlowIDMPolicy(IDMPolicy):
    """
    专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
    """
    NORMAL_SPEED = 10  # km/h
    MAX_SPEED = 10  # km/h
    CREEP_SPEED = 10  # km/h
    ACC_FACTOR = 0.5  # 降低加速度因子

    def __init__(self, control_object, random_seed):
        super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
        self.target_speed = self.NORMAL_SPEED

def patch_traffic_manager_for_slow_npc():
    """
    通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy
    这样只有NPC车辆会使用低速，自车保持正常速度
    """
    import metadrive.manager.traffic_manager as tm

    # 保存原始的add_policy方法
    original_add_policy = tm.PGTrafficManager.add_policy

    def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
        # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
        if policy_class == IDMPolicy:
            policy_class = NPCSlowIDMPolicy
        return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

    # 应用patch
    tm.PGTrafficManager.add_policy = patched_add_policy

def create_metadrive_env(env_config):
    """创建MetaDrive环境"""
    # 应用NPC限速
    patch_traffic_manager_for_slow_npc()

    # 使用简化的配置
    config = {
        "use_render": False,
        "manual_control": False,
        "traffic_density": 0.1,
        "num_scenarios": 10,
        "map": "S",
        "start_seed": 1000,
        "accident_prob": 0.0,
        "success_reward": 0,
        "driving_reward": 0,
        "speed_reward": 0,
        "use_lateral_reward": False
    }

    # 更新配置
    config.update(env_config)

    # 创建环境
    env = MetaDriveEnv(config)

    return env

def convert_json_to_rllib_format(json_file_path, output_dir):
    """
    将JSON格式的数据转换为RLlib离线数据格式
    """
    print(f"正在转换数据: {json_file_path}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    episodes = []
    current_episode = {
        "observations": [],
        "actions": [],
        "rewards": [],
        "dones": [],
        "infos": []
    }
    
    with open(json_file_path, 'r') as f:
        for line in f:
            try:
                sample = json.loads(line.strip())
                if 'obs' in sample and 'actions' in sample:
                    obs_list = sample['obs']
                    actions_list = sample['actions']
                    
                    # 确保obs和actions长度一致
                    min_len = min(len(obs_list), len(actions_list))
                    
                    for i in range(min_len):
                        obs = np.array(obs_list[i], dtype=np.float32)
                        action = np.array(actions_list[i], dtype=np.float32)
                        
                        # 检查数据维度
                        if obs.shape[0] == 88 and action.shape[0] == 2:
                            current_episode["observations"].append(obs)
                            current_episode["actions"].append(action)
                            current_episode["rewards"].append(0.0)  # BC不需要奖励
                            current_episode["dones"].append(False)
                            current_episode["infos"].append({})
                    
                    # 标记episode结束
                    if current_episode["observations"]:
                        current_episode["dones"][-1] = True
                        episodes.append(current_episode)
                        current_episode = {
                            "observations": [],
                            "actions": [],
                            "rewards": [],
                            "dones": [],
                            "infos": []
                        }
                        
            except json.JSONDecodeError as e:
                print(f"JSON解析错误: {e}")
                continue
    
    # 保存为RLlib格式
    output_file = os.path.join(output_dir, "data.json")
    with open(output_file, 'w') as f:
        for episode in episodes:
            if episode["observations"]:
                episode_data = {
                    "type": "SampleBatch",
                    "obs": [obs.tolist() for obs in episode["observations"]],
                    "actions": [action.tolist() for action in episode["actions"]],
                    "rewards": episode["rewards"],
                    "dones": episode["dones"],
                    "infos": episode["infos"]
                }
                f.write(json.dumps(episode_data) + '\n')
    
    print(f"转换完成，共{len(episodes)}个episode，保存到: {output_file}")
    return output_file

class RayBCTrainer:
    """Ray RLlib行为克隆训练器"""
    
    def __init__(self, data_path, use_slow_npc=True):
        self.data_path = data_path
        self.use_slow_npc = use_slow_npc
        self.temp_dir = None
        
        # 初始化Ray
        if not ray.is_initialized():
            ray.init(ignore_reinit_error=True)
        
        # 注册环境
        register_env("metadrive", create_metadrive_env)
        
        print("✅ Ray初始化完成，环境已注册")
    
    def prepare_data(self):
        """准备训练数据"""
        # 创建临时目录
        self.temp_dir = tempfile.mkdtemp()
        
        # 转换数据格式
        rllib_data_file = convert_json_to_rllib_format(self.data_path, self.temp_dir)
        
        return rllib_data_file
    
    def train(self, num_iterations=100):
        """训练BC模型"""
        print("开始准备数据...")
        data_file = self.prepare_data()
        
        print("配置BC算法...")
        
        # 使用MARWIL算法，设置beta=0实现纯行为克隆
        config = MARWILConfig()

        # 框架配置
        config.framework("torch")

        # 环境配置
        config.environment(
            env="metadrive",
            observation_space=spaces.Box(low=-np.inf, high=np.inf, shape=(88,), dtype=np.float32),
            action_space=spaces.Box(low=-1.0, high=1.0, shape=(2,), dtype=np.float32)
        )
        
        # 训练配置
        config.training(
            lr=1e-4,
            beta=0.0,  # 设置为0实现纯行为克隆
            train_batch_size=512,
            grad_clip=1.0
        )
        
        # 离线数据配置
        config.offline_data(
            input_=[data_file],
            input_config={}
        )
        
        # 资源配置
        config.resources(
            num_gpus=1 if ray.get_gpu_ids() else 0,
            num_cpus_per_worker=1
        )
        
        # 评估配置
        config.evaluation(
            evaluation_num_workers=0,
            evaluation_duration=1,
            evaluation_interval=None
        )
        
        # Rollout配置
        config.rollouts(
            num_rollout_workers=0,
            create_env_on_local_worker=True
        )

        # 禁用环境检查
        config.environment(disable_env_checking=True)
        
        # 创建算法实例
        print("创建MARWIL算法实例...")
        algo = config.build()
        
        print(f"开始训练，共{num_iterations}次迭代...")
        
        for i in range(num_iterations):
            result = algo.train()
            
            # 打印训练进度
            if i % 10 == 0:
                print(f"Iteration {i}: Loss = {result.get('info', {}).get('learner', {}).get('default_policy', {}).get('learner_stats', {}).get('total_loss', 'N/A')}")
            
            # 保存模型
            if (i + 1) % 20 == 0:
                checkpoint_path = f"./bc_models/ray_bc_iteration_{i+1}"
                os.makedirs(checkpoint_path, exist_ok=True)
                algo.save(checkpoint_path)
                print(f"模型已保存到: {checkpoint_path}")
        
        # 保存最终模型
        final_checkpoint = "./bc_models/ray_bc_final"
        os.makedirs(final_checkpoint, exist_ok=True)
        algo.save(final_checkpoint)
        print(f"最终模型已保存到: {final_checkpoint}")
        
        # 清理
        algo.stop()
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
        
        print("训练完成！")

if __name__ == "__main__":
    # 数据路径
    data_path = '/home/<USER>/rl/RL-IL/data/bc_data/50/output-2025-07-16_23-04-11_worker-0_0.json'
    
    # 检查数据文件是否存在
    if not os.path.exists(data_path):
        print(f"错误: 数据文件不存在: {data_path}")
        exit(1)
    
    # 创建训练器并开始训练
    trainer = RayBCTrainer(
        data_path=data_path,
        use_slow_npc=True
    )
    
    trainer.train(num_iterations=100)
