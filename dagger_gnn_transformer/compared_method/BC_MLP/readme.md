# 收集数据

ExpertPolicy

'crash_vehicle_done': True,

"use_render": use_render,

'map':'CXO',

"num_scenarios": 1,

"start_seed": 4,

'out_of_route_done': True,

# 'horizon':91,

"agent_policy": drive_policy,  # 直接在这里设置专家策略

"traffic_density": 0.1,

"vehicle_config": {

"lidar": {"num_lasers": 30, "distance": 50, "num_others": 3},

"side_detector": {"num_lasers": 30},

"lane_line_detector": {"num_lasers": 12}

    }

    }

总成功率: 100.00%

总尝试次数: 100

成功次数: 100

2025-07-07 16:05:31,620 INFO json_writer.py:107 -- Writing to new output file 

/home/<USER>/rl/RL-IL/dagger_gnn_transformer/data/bc_data/100/output-2025-07-07_16-05-31_worker-0_0.json




========== 最终统计 ==========
总成功率: 100.00%
总尝试次数: 50
成功次数: 50
失败次数: 0
===========

2025-07-07 17:07:58,741 INFO json_writer.py:107 -- Writing to new output file <_io.TextIOWrapper name='/home/<USER>/rl/RL-IL/dagger_gnn_transformer/data/bc_data/50/output-2025-07-07_17-07-58_worker-0_0.json' mode='w' encoding='UTF-8'>
