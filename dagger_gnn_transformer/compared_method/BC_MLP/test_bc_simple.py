#!/usr/bin/env python3
"""
简化的BC模型测试脚本，专门用于统计各项指标
"""

import ray
from ray.rllib.algorithms.marwil import MARWIL
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.envs.gym_wrapper import createGymWrapper
from metadrive.policy.idm_policy import IDMPolicy
import json
import time
import re
import sys
from io import StringIO
import contextlib

# 创建专门用于NPC车辆的低速IDM策略
class NPCSlowIDMPolicy(IDMPolicy):
    """
    专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
    """
    NORMAL_SPEED = 10  # km/h
    MAX_SPEED = 10  # km/h
    CREEP_SPEED = 10  # km/h
    ACC_FACTOR = 0.5  # 降低加速度因子

    def __init__(self, control_object, random_seed):
        super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
        self.target_speed = self.NORMAL_SPEED

def patch_traffic_manager_for_slow_npc():
    """
    通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy
    这样只有NPC车辆会使用低速，自车保持正常速度
    """
    import metadrive.manager.traffic_manager as tm

    # 保存原始的add_policy方法
    original_add_policy = tm.PGTrafficManager.add_policy

    def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
        # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
        if policy_class == IDMPolicy:
            policy_class = NPCSlowIDMPolicy
        return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

    # 应用patch
    tm.PGTrafficManager.add_policy = patched_add_policy

class LogCapture:
    """捕获日志输出以解析结束原因"""
    def __init__(self):
        self.logs = []
        
    def write(self, text):
        self.logs.append(text)
        sys.__stdout__.write(text)  # 同时输出到控制台
        
    def flush(self):
        pass
        
    def get_last_episode_reason(self):
        """从日志中获取最后一个episode的结束原因"""
        for log in reversed(self.logs):
            if "Episode ended!" in log and "Reason:" in log:
                if "out_of_road" in log:
                    return "out_of_road"
                elif "crash" in log:
                    return "crash"
                elif "arrive_dest" in log:
                    return "arrive_dest"
        return "unknown"

class SimpleBCTester:
    def __init__(self, checkpoint_path, use_slow_npc=True):
        self.checkpoint_path = checkpoint_path
        self.use_slow_npc = use_slow_npc
        self.algo = None
        self.env = None
        self.log_capture = LogCapture()

        # 如果启用低速NPC，应用猴子补丁
        if use_slow_npc:
            patch_traffic_manager_for_slow_npc()
            print("✅ 已将NPC车辆速度降低到10 km/h，自车保持正常速度")
        
    def setup(self):
        """初始化Ray和算法"""
        if not ray.is_initialized():
            ray.init()
            
        # 注册环境
        def env_creator(env_config):
            config = {
                'crash_vehicle_done': True,
                "use_render": False,
                'map':'CXO',
                "num_scenarios": 1,
                "start_seed": 4,
                'out_of_route_done': True,
                "traffic_density": 0.1, 
                "vehicle_config": {
                    "lidar": {"num_lasers": 30, "distance": 50, "num_others": 3},
                    "side_detector": {"num_lasers": 30},
                    "lane_line_detector": {"num_lasers": 12}
                }
            }
            return createGymWrapper(MetaDriveEnv)(config=config)
            
        from ray.tune.registry import register_env
        register_env("MetaDriveEnv-v0", env_creator)
        
        # 创建算法配置
        config = {
            "env": "MetaDriveEnv-v0",
            "framework": "torch",
            "num_workers": 0,
            "evaluation_num_workers": 0,
        }
        
        # 创建算法实例
        self.algo = MARWIL(config=config)
        
        # 加载模型
        self.algo.restore(self.checkpoint_path)
        print(f"模型已从检查点加载: {self.checkpoint_path}")
        
        # 创建测试环境
        self.env = env_creator({})
        
    def test_single_episode(self):
        """测试单个episode"""
        # 重定向stdout来捕获日志
        old_stdout = sys.stdout
        sys.stdout = self.log_capture
        
        try:
            obs = self.env.reset()
            done = False
            episode_step = 0
            episode_reward = 0
            
            while not done and episode_step < 2000:  # 防止无限循环
                action = self.algo.compute_single_action(obs)
                obs, reward, done, info = self.env.step(action)
                episode_step += 1
                episode_reward += reward
            
            # 恢复stdout
            sys.stdout = old_stdout
            
            # 从日志中获取结束原因
            reason = self.log_capture.get_last_episode_reason()
            
            # 获取路线完成度
            agent = self.env.agent
            if hasattr(agent, 'navigation'):
                route_completion = agent.navigation.route_completion
            else:
                route_completion = 0.0
                
            return {
                'reason': reason,
                'steps': episode_step,
                'reward': episode_reward,
                'route_completion': route_completion
            }
            
        except Exception as e:
            sys.stdout = old_stdout
            print(f"Episode出现错误: {e}")
            return {
                'reason': 'error',
                'steps': 0,
                'reward': 0,
                'route_completion': 0.0
            }
    
    def test_model(self, num_episodes=100):
        """测试模型性能"""
        print(f"开始测试BC模型，共{num_episodes}个回合...")
        
        # 统计指标
        success_count = 0
        out_of_road_count = 0
        crash_count = 0
        error_count = 0
        total_steps = 0
        route_completion_rates = []
        episode_rewards = []
        
        for episode in range(num_episodes):
            result = self.test_single_episode()
            
            # 统计结果
            total_steps += result['steps']
            episode_rewards.append(result['reward'])
            route_completion_rates.append(result['route_completion'])
            
            # 统计结束原因
            if result['reason'] == 'arrive_dest':
                success_count += 1
            elif result['reason'] == 'out_of_road':
                out_of_road_count += 1
            elif result['reason'] == 'crash':
                crash_count += 1
            else:
                error_count += 1
            
            # 打印进度
            if (episode + 1) % 10 == 0:
                current_success_rate = success_count / (episode + 1) * 100
                current_out_of_road_rate = out_of_road_count / (episode + 1) * 100
                print(f"已完成 {episode + 1}/{num_episodes} 回合 - 成功率: {current_success_rate:.1f}%, 出路率: {current_out_of_road_rate:.1f}%")
        
        # 计算最终统计结果
        success_rate = success_count / num_episodes * 100
        out_of_road_rate = out_of_road_count / num_episodes * 100
        crash_rate = crash_count / num_episodes * 100
        error_rate = error_count / num_episodes * 100
        avg_steps = total_steps / num_episodes
        avg_route_completion = sum(route_completion_rates) / num_episodes * 100
        avg_reward = sum(episode_rewards) / num_episodes
        
        # 打印结果
        print("\n" + "="*60)
        print("BC模型测试结果统计")
        print("="*60)
        print(f"测试回合数: {num_episodes}")
        print(f"成功率: {success_rate:.2f}% ({success_count}/{num_episodes})")
        print(f"出路率: {out_of_road_rate:.2f}% ({out_of_road_count}/{num_episodes})")
        print(f"碰撞率: {crash_rate:.2f}% ({crash_count}/{num_episodes})")
        print(f"错误率: {error_rate:.2f}% ({error_count}/{num_episodes})")
        print(f"平均步长: {avg_steps:.2f}")
        print(f"平均路线完成度: {avg_route_completion:.2f}%")
        print(f"平均奖励: {avg_reward:.2f}")
        print("="*60)
        
        results = {
            'success_rate': success_rate,
            'out_of_road_rate': out_of_road_rate,
            'crash_rate': crash_rate,
            'error_rate': error_rate,
            'avg_steps': avg_steps,
            'avg_route_completion': avg_route_completion,
            'avg_reward': avg_reward,
            'total_episodes': num_episodes,
            'success_count': success_count,
            'out_of_road_count': out_of_road_count,
            'crash_count': crash_count,
            'error_count': error_count
        }
        
        return results
    
    def cleanup(self):
        """清理资源"""
        if self.env:
            self.env.close()
        if ray.is_initialized():
            ray.shutdown()

if __name__ == "__main__":
    checkpoint = './bc_models/simple_bc_final.pth'

    tester = SimpleBCTester(checkpoint, use_slow_npc=True)
    
    try:
        tester.setup()
        results = tester.test_model(num_episodes=100)
        
        # 保存结果
        with open('bc_test_results_simple.json', 'w') as f:
            json.dump(results, f, indent=2)
        print("\n测试结果已保存到 bc_test_results_simple.json")
        
    finally:
        tester.cleanup()
