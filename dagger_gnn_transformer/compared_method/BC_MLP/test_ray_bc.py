#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Ray RLlib训练的BC模型
"""

import os
import ray
import numpy as np
from ray.rllib.algorithms.marwil import MARWIL, MARWILConfig
from ray.tune.registry import register_env
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy
from gym import spaces
import time

# 创建专门用于NPC车辆的低速IDM策略
class NPCSlowIDMPolicy(IDMPolicy):
    """
    专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
    """
    NORMAL_SPEED = 10  # km/h
    MAX_SPEED = 10  # km/h
    CREEP_SPEED = 10  # km/h
    ACC_FACTOR = 0.5  # 降低加速度因子

    def __init__(self, control_object, random_seed):
        super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
        self.target_speed = self.NORMAL_SPEED

def patch_traffic_manager_for_slow_npc():
    """
    通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy
    这样只有NPC车辆会使用低速，自车保持正常速度
    """
    import metadrive.manager.traffic_manager as tm

    # 保存原始的add_policy方法
    original_add_policy = tm.PGTrafficManager.add_policy

    def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
        # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
        if policy_class == IDMPolicy:
            policy_class = NPCSlowIDMPolicy
        return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

    # 应用patch
    tm.PGTrafficManager.add_policy = patched_add_policy

def create_metadrive_env(env_config):
    """创建MetaDrive环境"""
    # 应用NPC限速
    patch_traffic_manager_for_slow_npc()
    
    # 使用简化的配置
    config = {
        "use_render": False,
        "manual_control": False,
        "traffic_density": 0.1,
        "num_scenarios": 10,
        "map": "S",
        "start_seed": 1000,
        "accident_prob": 0.0,
        "success_reward": 0,
        "driving_reward": 0,
        "speed_reward": 0,
        "use_lateral_reward": False
    }
    
    # 更新配置
    config.update(env_config)
    
    # 创建环境
    env = MetaDriveEnv(config)
    
    return env

class RayBCTester:
    """Ray RLlib BC模型测试器"""
    
    def __init__(self, checkpoint_path, use_slow_npc=True):
        self.checkpoint_path = checkpoint_path
        self.use_slow_npc = use_slow_npc
        self.algo = None
        
        # 初始化Ray
        if not ray.is_initialized():
            ray.init(ignore_reinit_error=True)
        
        # 注册环境
        register_env("metadrive", create_metadrive_env)
        
        print("✅ Ray初始化完成，环境已注册")
        if use_slow_npc:
            print("✅ 已将NPC车辆速度降低到10 km/h，自车保持正常速度")
    
    def load_model(self):
        """加载训练好的模型"""
        print(f"正在加载模型: {self.checkpoint_path}")

        try:
            # 重新创建算法配置（与训练时保持一致）
            config = MARWILConfig()

            # 框架配置
            config.framework("torch")

            # 环境配置
            config.environment(
                env="metadrive",
                observation_space=spaces.Box(low=-np.inf, high=np.inf, shape=(88,), dtype=np.float32),
                action_space=spaces.Box(low=-1.0, high=1.0, shape=(2,), dtype=np.float32),
                disable_env_checking=True
            )

            # 训练配置
            config.training(
                lr=1e-4,
                beta=0.0,  # 设置为0实现纯行为克隆
                train_batch_size=512,
                grad_clip=1.0
            )

            # 资源配置
            config.resources(
                num_gpus=0,  # 测试时不需要GPU
                num_cpus_per_worker=1
            )

            # 评估配置
            config.evaluation(
                evaluation_num_workers=0,
                evaluation_duration=1,
                evaluation_interval=None
            )

            # Rollout配置
            config.rollouts(
                num_rollout_workers=0,
                create_env_on_local_worker=True
            )

            # 创建算法实例
            self.algo = config.build()

            # 恢复权重
            self.algo.restore(self.checkpoint_path)

            print("✅ 模型加载成功")
            return True
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
    
    def test_episodes(self, num_episodes=10, max_steps=1000, render=False):
        """测试多个episode"""
        if self.algo is None:
            print("❌ 请先加载模型")
            return
        
        print(f"开始测试 {num_episodes} 个episodes...")
        
        # 创建测试环境
        env_config = {"use_render": render}
        env = create_metadrive_env(env_config)
        
        results = {
            "success_count": 0,
            "crash_count": 0,
            "timeout_count": 0,
            "total_episodes": num_episodes,
            "episode_lengths": [],
            "episode_rewards": []
        }
        
        for episode in range(num_episodes):
            print(f"\n--- Episode {episode + 1}/{num_episodes} ---")
            
            obs = env.reset()
            total_reward = 0
            step_count = 0
            done = False
            
            start_time = time.time()
            
            while not done and step_count < max_steps:
                # 使用训练好的策略预测动作
                action = self.algo.compute_single_action(obs)
                
                # 执行动作
                obs, reward, done, info = env.step(action)
                total_reward += reward
                step_count += 1
                
                # 打印一些调试信息
                if step_count % 100 == 0:
                    print(f"  Step {step_count}: Reward = {reward:.3f}, Done = {done}")
            
            episode_time = time.time() - start_time
            
            # 记录结果
            results["episode_lengths"].append(step_count)
            results["episode_rewards"].append(total_reward)
            
            # 判断结束原因
            if info.get("arrive_dest", False):
                results["success_count"] += 1
                status = "✅ 成功到达目标"
            elif info.get("crash", False):
                results["crash_count"] += 1
                status = "💥 发生碰撞"
            else:
                results["timeout_count"] += 1
                status = "⏰ 超时结束"
            
            print(f"  结果: {status}")
            print(f"  步数: {step_count}, 奖励: {total_reward:.3f}, 时间: {episode_time:.2f}s")
        
        env.close()
        
        # 打印总结
        self.print_summary(results)
        
        return results
    
    def print_summary(self, results):
        """打印测试总结"""
        print("\n" + "="*50)
        print("📊 测试结果总结")
        print("="*50)
        
        total = results["total_episodes"]
        success_rate = results["success_count"] / total * 100
        crash_rate = results["crash_count"] / total * 100
        timeout_rate = results["timeout_count"] / total * 100
        
        print(f"总episodes: {total}")
        print(f"成功率: {success_rate:.1f}% ({results['success_count']}/{total})")
        print(f"碰撞率: {crash_rate:.1f}% ({results['crash_count']}/{total})")
        print(f"超时率: {timeout_rate:.1f}% ({results['timeout_count']}/{total})")
        
        if results["episode_lengths"]:
            avg_length = np.mean(results["episode_lengths"])
            avg_reward = np.mean(results["episode_rewards"])
            print(f"平均步数: {avg_length:.1f}")
            print(f"平均奖励: {avg_reward:.3f}")
        
        print("="*50)
    
    def cleanup(self):
        """清理资源"""
        if self.algo:
            self.algo.stop()
        ray.shutdown()

if __name__ == "__main__":
    # 模型路径 - 使用最终训练的模型
    checkpoint_path = './bc_models/ray_bc_final/checkpoint_000100'
    
    # 检查模型文件是否存在
    if not os.path.exists(checkpoint_path):
        print(f"❌ 错误: 模型文件不存在: {checkpoint_path}")
        print("请先运行 ray_bc_trainer.py 训练模型")
        exit(1)
    
    # 创建测试器
    tester = RayBCTester(
        checkpoint_path=checkpoint_path,
        use_slow_npc=True
    )
    
    try:
        # 加载模型
        if tester.load_model():
            # 测试模型
            results = tester.test_episodes(
                num_episodes=10,
                max_steps=1000,
                render=False  # 设置为True可以看到可视化
            )
        else:
            print("❌ 模型加载失败，无法进行测试")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
    
    finally:
        # 清理资源
        tester.cleanup()
        print("🧹 资源清理完成")
