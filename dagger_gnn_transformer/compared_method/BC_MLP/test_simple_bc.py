#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试简单PyTorch BC模型
"""

import os
import torch
import torch.nn as nn
import numpy as np
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy
import time

# 创建专门用于NPC车辆的低速IDM策略
class NPCSlowIDMPolicy(IDMPolicy):
    """
    专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
    """
    NORMAL_SPEED = 10  # km/h
    MAX_SPEED = 10  # km/h
    CREEP_SPEED = 10  # km/h
    ACC_FACTOR = 0.5  # 降低加速度因子

    def __init__(self, control_object, random_seed):
        super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
        self.target_speed = self.NORMAL_SPEED

def patch_traffic_manager_for_slow_npc():
    """
    通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy
    这样只有NPC车辆会使用低速，自车保持正常速度
    """
    import metadrive.manager.traffic_manager as tm

    # 保存原始的add_policy方法
    original_add_policy = tm.PGTrafficManager.add_policy

    def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
        # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
        if policy_class == IDMPolicy:
            policy_class = NPCSlowIDMPolicy
        return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

    # 应用patch
    tm.PGTrafficManager.add_policy = patched_add_policy

class SimpleMLP(nn.Module):
    """简单的MLP网络用于行为克隆"""
    
    def __init__(self, obs_dim, action_dim, hidden_dims=[256, 256]):
        super().__init__()
        
        layers = []
        input_dim = obs_dim
        
        for hidden_dim in hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.1)
            ])
            input_dim = hidden_dim
        
        layers.append(nn.Linear(input_dim, action_dim))
        
        self.network = nn.Sequential(*layers)
        
        # 初始化权重
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight)
                nn.init.zeros_(m.bias)
    
    def forward(self, x):
        return self.network(x)

class SimpleBCTester:
    """简单BC模型测试器"""
    
    def __init__(self, model_path, use_slow_npc=True, device='cuda'):
        self.model_path = model_path
        self.use_slow_npc = use_slow_npc
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.model = None
        
        print(f"使用设备: {self.device}")
        if use_slow_npc:
            patch_traffic_manager_for_slow_npc()
            print("✅ 已将NPC车辆速度降低到10 km/h，自车保持正常速度")
    
    def load_model(self):
        """加载训练好的模型"""
        print(f"正在加载模型: {self.model_path}")
        
        try:
            # 加载模型checkpoint
            checkpoint = torch.load(self.model_path, map_location=self.device)
            
            # 创建模型
            obs_dim = checkpoint.get('obs_dim', 88)
            action_dim = checkpoint.get('action_dim', 2)
            self.model = SimpleMLP(obs_dim, action_dim).to(self.device)
            
            # 加载权重
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.eval()
            
            print("✅ 模型加载成功")
            print(f"观测维度: {obs_dim}, 动作维度: {action_dim}")
            return True
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            return False
    
    def predict_action(self, obs):
        """预测动作"""
        with torch.no_grad():
            # 处理观测数据，如果是tuple则取第一个元素
            if isinstance(obs, tuple):
                obs = obs[0]

            # 确保obs是numpy数组
            if not isinstance(obs, np.ndarray):
                obs = np.array(obs)

            # 检查观测维度并调整
            print(f"观测维度: {obs.shape}")
            if obs.shape[0] != 88:
                # 如果维度不匹配，截取前88维或填充到88维
                if obs.shape[0] > 88:
                    obs = obs[:88]
                else:
                    # 填充到88维
                    padding = np.zeros(88 - obs.shape[0])
                    obs = np.concatenate([obs, padding])

            obs_tensor = torch.from_numpy(obs).float().to(self.device)
            if obs_tensor.dim() == 1:
                obs_tensor = obs_tensor.unsqueeze(0)
            action = self.model(obs_tensor)
            return action.cpu().numpy().squeeze()
    
    def test_episodes(self, num_episodes=10, max_steps=1000, render=False):
        """测试多个episode"""
        if self.model is None:
            print("❌ 请先加载模型")
            return
        
        print(f"开始测试 {num_episodes} 个episodes...")
        
        # 创建测试环境
        config = {
            "use_render": render,
            "manual_control": False,
            "traffic_density": 0.1,
            "num_scenarios": 10,
            "map": "S",
            "start_seed": 1000,
            "accident_prob": 0.0,
            "success_reward": 0,
            "driving_reward": 0,
            "speed_reward": 0,
            "use_lateral_reward": False
        }
        
        env = MetaDriveEnv(config)
        
        results = {
            "success_count": 0,
            "crash_count": 0,
            "timeout_count": 0,
            "total_episodes": num_episodes,
            "episode_lengths": [],
            "episode_rewards": []
        }
        
        for episode in range(num_episodes):
            print(f"\n--- Episode {episode + 1}/{num_episodes} ---")
            
            obs = env.reset()
            total_reward = 0
            step_count = 0
            done = False
            
            start_time = time.time()
            
            while not done and step_count < max_steps:
                # 使用训练好的策略预测动作
                action = self.predict_action(obs)
                
                # 执行动作
                step_result = env.step(action)
                if len(step_result) == 4:
                    obs, reward, done, info = step_result
                else:
                    obs, reward, terminated, truncated, info = step_result
                    done = terminated or truncated
                total_reward += reward
                step_count += 1
                
                # 打印一些调试信息
                if step_count % 100 == 0:
                    print(f"  Step {step_count}: Action = [{action[0]:.3f}, {action[1]:.3f}], Reward = {reward:.3f}")
            
            episode_time = time.time() - start_time
            
            # 记录结果
            results["episode_lengths"].append(step_count)
            results["episode_rewards"].append(total_reward)
            
            # 判断结束原因
            if info.get("arrive_dest", False):
                results["success_count"] += 1
                status = "✅ 成功到达目标"
            elif info.get("crash", False):
                results["crash_count"] += 1
                status = "💥 发生碰撞"
            else:
                results["timeout_count"] += 1
                status = "⏰ 超时结束"
            
            print(f"  结果: {status}")
            print(f"  步数: {step_count}, 奖励: {total_reward:.3f}, 时间: {episode_time:.2f}s")
        
        env.close()
        
        # 打印总结
        self.print_summary(results)
        
        return results
    
    def print_summary(self, results):
        """打印测试总结"""
        print("\n" + "="*50)
        print("📊 测试结果总结")
        print("="*50)
        
        total = results["total_episodes"]
        success_rate = results["success_count"] / total * 100
        crash_rate = results["crash_count"] / total * 100
        timeout_rate = results["timeout_count"] / total * 100
        
        print(f"总episodes: {total}")
        print(f"成功率: {success_rate:.1f}% ({results['success_count']}/{total})")
        print(f"碰撞率: {crash_rate:.1f}% ({results['crash_count']}/{total})")
        print(f"超时率: {timeout_rate:.1f}% ({results['timeout_count']}/{total})")
        
        if results["episode_lengths"]:
            avg_length = np.mean(results["episode_lengths"])
            avg_reward = np.mean(results["episode_rewards"])
            print(f"平均步数: {avg_length:.1f}")
            print(f"平均奖励: {avg_reward:.3f}")
        
        print("="*50)

if __name__ == "__main__":
    # 模型路径 - 使用PyTorch训练的模型
    model_path = './bc_models/simple_bc_final.pth'
    
    # 检查模型文件是否存在
    if not os.path.exists(model_path):
        print(f"❌ 错误: 模型文件不存在: {model_path}")
        print("请先运行 behavior_clone_simple.py 训练模型")
        exit(1)
    
    # 创建测试器
    tester = SimpleBCTester(
        model_path=model_path,
        use_slow_npc=True,
        device='cuda'
    )
    
    try:
        # 加载模型
        if tester.load_model():
            # 测试模型
            results = tester.test_episodes(
                num_episodes=10,
                max_steps=1000,
                render=False  # 设置为True可以看到可视化
            )
        else:
            print("❌ 模型加载失败，无法进行测试")
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("🧹 测试完成")
