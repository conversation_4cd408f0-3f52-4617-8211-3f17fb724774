import os
import time
import numpy as np
from tqdm import tqdm
from ray.rllib.algorithms.bc import BCConfig
from ray.tune.registry import register_env
from ray.rllib.policy.policy import Policy
import ray
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.envs.gym_wrapper import createGymWrapper
from torch.utils.tensorboard import SummaryWriter
from ray.rllib.algorithms.ppo import PPOConfig
from datetime import datetime
from ray.rllib.models import ModelCatalog
from models.attention_net_simple import SimpleAttentionNet

config = {
            'crash_vehicle_done': True,
            "use_render": False,
            'map':'O',
            "num_scenarios": 1000,
            'out_of_route_done': True,
            "traffic_density": 0.1, 
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50, "num_others": 3},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            }
        }

def env_creator(env_config):
    env = createGymWrapper(MetaDriveEnv)(config=config)
    return env

register_env('metadrive-env', env_creator)

class SimpleBehaviorCloneTrainer:
    def __init__(self, bc_data_path, train_batch_size=1024, iteration_num=500, log_dir='./tensorboard_logs'):
        self.bc_data_path = bc_data_path
        self.train_batch_size = train_batch_size
        self.iteration_num = iteration_num
        self.log_dir = log_dir
        self.logger = TrainingLogger(log_dir=self.log_dir)
        self.setup_environment()
        self.setup_config()
        self.algo = self.config.build()

    def setup_environment(self):
        self.game = env_creator({})

    def setup_config(self):
        config = BCConfig()
        config.postprocess_inputs = False
        
        # 注册简化版本的模型
        ModelCatalog.register_custom_model("simple_attention_net", SimpleAttentionNet)

        # 优化配置 - 确保所有参数都被正确使用
        config.model.update({
            "custom_model": "simple_attention_net",
            "custom_model_config": {
                # === 优化后的配置 ===
                "attention_num_transformer_units": 2,  # 增加层数
                "attention_dim": 128,                  # 增加模型容量
                "attention_num_heads": 8,              # 8个注意力头
                "attention_position_wise_mlp_dim": 256 # 增加FF维度
            }
        })

        # 序列配置
        sequence_length = 20
        config.replay_sequence_length = sequence_length
        config.rollout_fragment_length = sequence_length
        config.train_batch_size = self.train_batch_size * 8
        config.model["max_seq_len"] = sequence_length

        # 训练配置
        config.framework('torch')
        config.beta = 0.0
        config.training(
            lr=3e-5,  # 适中的学习率
            beta=0.0,
            grad_clip=0.5,
            gamma=0.99,
        )

        # 环境配置
        config.environment(env='metadrive-env',
                         disable_env_checking=False,
                         observation_space=self.game.observation_space,
                         action_space=self.game.action_space)

        # 其他配置
        config.checkpointing(export_native_model_files=True)
        config.offline_data(input_=self.bc_data_path)

        # 评估配置
        config.evaluation(
            evaluation_num_workers=0,
            evaluation_duration=10
        )

        # 资源配置
        config.resources(
            num_gpus=1,  
            num_cpus_per_worker=5
        )

        config.debugging(log_level='ERROR')
        self.config = config
        
        print("=== 简化且稳定的模型配置 ===")
        print(f"模型类型: SimpleAttentionNet")
        print("✅ 确保维度匹配和稳定性")

    def train(self):
        best_loss = float('inf')
        patience = 50
        bad_iterations = 0
        
        for iteration in tqdm(range(self.iteration_num)):
            try:
                result = self.algo.train()
                learner_stats = result['info']['learner']['default_policy']['learner_stats']
                total_loss = learner_stats['total_loss']
                policy_loss = learner_stats['policy_loss']
                
                if np.isnan(total_loss) or np.isnan(policy_loss):
                    print("Warning: Loss is NaN, skipping iteration")
                    continue
                    
                if total_loss < best_loss:
                    best_loss = total_loss
                    bad_iterations = 0
                    if iteration > 0 and iteration % 1000 == 0:
                        now = datetime.now()
                        formatted_date = now.strftime("%Y-%m-%d-%H-%M-%S")
                        self.algo.save(f'./bc_models/BC_simple_best_{iteration}_'+ formatted_date)
                else:
                    bad_iterations += 1
                
                if bad_iterations >= patience:
                    print(f"\nEarly stopping at iteration {iteration}")
                    break
                    
                print(f'----- iteration={iteration}, total_loss={total_loss:.5f}, policy_loss={policy_loss:.5f}')
                self.logger.log_metrics(iteration, total_loss, policy_loss)
                
            except Exception as e:
                print(f"Error during training: {str(e)}")
                continue
                
        now = datetime.now()
        formatted_date = now.strftime("%Y-%m-%d-%H-%M-%S")
        file_name = self.algo.save(f'./bc_models/BC_simple_final_{iteration}_'+ formatted_date)
        self.logger.close()
        print(f"Final model saved: {file_name}")

class TrainingLogger:
    def __init__(self, log_dir='./tensorboard_logs'):
        self.writer = SummaryWriter(log_dir=log_dir, flush_secs=10)
    
    def log_metrics(self, iteration, total_loss, policy_loss):
        self.writer.add_scalar('Loss/Total', total_loss, iteration)
        self.writer.add_scalar('Loss/Policy', policy_loss, iteration)
        
    def close(self):
        self.writer.close()

# 测试函数
def test_model():
    """测试模型是否能正常工作"""
    try:
        from models.attention_net_simple import SimpleAttentionNet
        import torch
        import gym
        
        # 模拟环境设置
        obs_space = gym.spaces.Box(low=-1, high=1, shape=(100,))
        action_space = gym.spaces.Box(low=-1, high=1, shape=(2,))
        
        # 使用您的原始配置测试
        model_config = {
            'custom_model_config': {
                "attention_num_transformer_units": 1,
                "attention_dim": 64,
                "attention_num_heads": 8,
                "attention_head_dim": 32,
                "attention_memory_inference": 50,
                "attention_memory_training": 50,
                "attention_position_wise_mlp_dim": 64
            }
        }
        
        print("初始化简化版本模型...")
        model = SimpleAttentionNet(obs_space, action_space, 2, model_config, 'test')
        print('✅ 模型初始化成功')
        
        # 测试前向传播
        dummy_input = {'obs_flat': torch.randn(4, 100)}
        logits, state = model.forward(dummy_input, [], torch.tensor([1, 1, 1, 1]))
        print(f'✅ 前向传播成功，输出形状: {logits.shape}')
        print('✅ 简化版本模型测试通过!')
        return True
        
    except Exception as e:
        print(f'❌ 模型测试失败: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== 测试简化版本模型 ===")
    if test_model():
        print("\n=== 开始训练简化版本 ===")
        bc_data_path = "/work/home/<USER>/hui/data/bc_data/100/output-2025-06-10_21-14-28_worker-0_0.json"
        trainer = SimpleBehaviorCloneTrainer(bc_data_path=bc_data_path)
        trainer.train()
    else:
        print("模型测试失败，请检查配置")
