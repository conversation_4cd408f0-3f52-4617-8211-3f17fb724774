import numpy as np
import gym
import torch
import torch.nn as nn
from torch.nn import functional as F
from ray.rllib.models.torch.torch_modelv2 import TorchModelV2
from ray.rllib.models.modelv2 import ModelV2
from ray.rllib.utils.annotations import override
from ray.rllib.utils.framework import try_import_torch
from ray.rllib.utils.typing import Dict, TensorType, List, ModelConfigDict

torch, nn = try_import_torch()

class SimpleMultiHeadAttention(nn.Module):
    def __init__(self, d_model, num_heads):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        
        self.qkv = nn.Linear(d_model, 3 * d_model)
        self.proj = nn.Linear(d_model, d_model)
        self.dropout = nn.Dropout(0.1)
        
    def forward(self, x, mask=None):
        batch_size, seq_len, d_model = x.size()
        
        qkv = self.qkv(x)
        qkv = qkv.reshape(batch_size, seq_len, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        scores = torch.matmul(q, k.transpose(-2, -1)) / np.sqrt(self.head_dim)
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        attention = F.softmax(scores, dim=-1)
        attention = self.dropout(attention)
        
        x = torch.matmul(attention, v)
        x = x.transpose(1, 2).reshape(batch_size, seq_len, d_model)
        x = self.proj(x)
        return x

class SimpleTransformerBlock(nn.Module):
    def __init__(self, d_model, num_heads, ff_dim, dropout=0.1):
        super().__init__()
        self.attention = SimpleMultiHeadAttention(d_model, num_heads)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.ff = nn.Sequential(
            nn.Linear(d_model, ff_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(ff_dim, d_model)
        )
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        att_output = self.attention(x)
        x = self.norm1(x + self.dropout(att_output))
        ff_output = self.ff(x)
        x = self.norm2(x + self.dropout(ff_output))
        return x

class SimpleAttentionNet(TorchModelV2, nn.Module):
    def __init__(self, obs_space, action_space, num_outputs, model_config, name):
        TorchModelV2.__init__(self, obs_space, action_space, num_outputs, model_config, name)
        nn.Module.__init__(self)
        
        custom_config = model_config.get("custom_model_config", {})
        
        # 读取配置参数
        d_model = custom_config.get("attention_dim", 64)
        num_heads = custom_config.get("attention_num_heads", 8)
        num_layers = custom_config.get("attention_num_transformer_units", 1)
        ff_dim = custom_config.get("attention_position_wise_mlp_dim", 64)
        
        # 简化：忽略复杂的head_dim配置，确保维度正确
        # 确保d_model能被num_heads整除
        if d_model % num_heads != 0:
            d_model = ((d_model // num_heads) + 1) * num_heads
            print(f"调整d_model到 {d_model} 以确保能被num_heads={num_heads}整除")
        
        # 动态获取观察空间维度
        obs_dim = int(np.product(obs_space.shape))
        print(f"观察空间维度: {obs_dim}")
        print(f"模型配置: d_model={d_model}, num_heads={num_heads}, 层数={num_layers}")
        
        # 输入嵌入层
        self.embedding = nn.Sequential(
            nn.Linear(obs_dim, d_model),
            nn.LayerNorm(d_model),
            nn.Dropout(0.1)
        )
        
        # Transformer块
        self.transformer_blocks = nn.ModuleList([
            SimpleTransformerBlock(d_model, num_heads, ff_dim, dropout=0.1)
            for _ in range(num_layers)
        ])
        
        # 输出层
        self.policy_head = nn.Sequential(
            nn.Linear(d_model, 256),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.GELU(),
            nn.Linear(128, num_outputs)
        )
        
        # Value head
        self.value_head = nn.Sequential(
            nn.Linear(d_model, 256),
            nn.GELU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.GELU(),
            nn.Linear(128, 1)
        )
        
    @override(ModelV2)
    def forward(self, input_dict: Dict[str, TensorType], state: List[TensorType],
               seq_lens: TensorType) -> (TensorType, List[TensorType]):
        x = input_dict["obs_flat"].float()
        batch_size = x.shape[0]
        
        # 嵌入观察
        x_embed = self.embedding(x)  # [batch, d_model]
        
        # 添加序列维度，但保持简单
        x_seq = x_embed.unsqueeze(1)  # [batch, 1, d_model]
        
        # 通过Transformer块
        for block in self.transformer_blocks:
            x_seq = block(x_seq)
        
        # 移除序列维度
        x_out = x_seq.squeeze(1)  # [batch, d_model]
        
        # 计算输出
        logits = self.policy_head(x_out)
        self._value = self.value_head(x_out).squeeze(-1)
        
        return logits, state

    @override(ModelV2)
    def value_function(self) -> TensorType:
        return self._value
