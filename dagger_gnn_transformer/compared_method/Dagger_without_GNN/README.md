# DAGGER训练 - 消融实验（去掉图神经网络）

## 🎯 实验目的

这是一个消融实验，用于验证图神经网络（GNN）在DAGGER训练中的有效性。通过去掉GNN组件，只使用纯Transformer架构，我们可以量化GNN对模型性能的贡献。

## 🏗️ 架构对比

### 原始架构（GNN + Transformer）
```
传感器特征 (72维) ──┐
                  ├─→ 特征融合 ──→ Transformer ──→ 动作预测
车辆图数据 ──→ GNN ──┘
```

### 消融实验架构（纯Transformer）
```
传感器特征 (72维) ──┐
                  ├─→ 特征拼接 ──→ Transformer ──→ 动作预测
自车状态 (7维) ────┘
```

## 🔧 主要修改

1. **去掉图构建**: 不再构建车辆间的图结构
2. **去掉GNN模块**: 移除所有图神经网络相关代码
3. **简化特征处理**: 直接拼接传感器特征和自车状态特征
4. **保持其他组件不变**: DAGGER训练流程、Transformer架构、损失函数等保持一致

## 📁 文件结构

```
Dagger_without_GNN/
├── models/
│   └── transformer_model.py      # 纯Transformer模型
├── tools/
│   ├── dagger_trainer.py         # DAGGER训练器（无GNN版本）
│   └── suppress_logs.py          # 日志抑制工具
├── main.py                       # 主训练脚本
├── test_model.py                 # 模型测试脚本
└── README.md                     # 本文件
```

## 🚀 使用方法

### 1. 训练模型

```bash
cd /home/<USER>/rl/RL-IL/dagger_gnn_transformer/compared_method/Dagger_without_GNN
python main.py
```

### 2. 测试模型

```bash
python test_model.py --model_path path/to/best_model.pth --num_episodes 50
```

### 3. 可视化测试（可选）

```bash
python test_model.py --model_path path/to/best_model.pth --num_episodes 10 --render
```

## ⚙️ 配置参数

主要配置参数在 `main.py` 中：

```python
config = {
    # 模型配置
    'transformer_dim': 256,        # Transformer维度
    'num_transformer_layers': 4,   # Transformer层数
    'num_heads': 8,                # 注意力头数
    'sequence_dim': 72,            # 传感器特征维度
    'ego_node_dim': 7,             # 自车状态维度
    
    # 训练配置
    'learning_rate': 1e-5,
    'batch_size': 128,
    'num_iterations': 50,          # DAGGER迭代次数
    'episodes_per_iteration': 20,  # 每次迭代的episode数
    
    # 环境配置
    'env_config': {
        'traffic_density': 0.1,
        'map': 'S',                # 简单地图
        # ... 其他环境参数
    }
}
```

## 📊 评估指标

模型将在以下指标上进行评估：

1. **成功率**: 成功到达目的地的比例
2. **碰撞率**: 发生碰撞的比例
3. **出路率**: 驶出道路的比例
4. **平均路线完成度**: 平均完成路线的百分比
5. **平均奖励**: 每个episode的平均奖励
6. **平均步数**: 每个episode的平均步数

## 🔍 验证机制

训练过程中每3回合自动验证一次：

- **验证频率**: 每3次迭代验证一次
- **验证episodes**: 10个episodes（可配置）
- **最佳模型保存**: 根据路线完成度保存最佳模型
- **TensorBoard记录**: 自动记录验证指标到TensorBoard

### 模型保存策略

1. **best_model_by_loss.pth**: 基于训练损失的最佳模型
2. **best_model_by_completion.pth**: 基于路线完成度的最佳模型
3. **checkpoint.pth**: 最新的检查点模型

## 🔬 实验假设

我们预期：

1. **性能下降**: 去掉GNN后，模型在复杂交通场景中的表现会下降
2. **简单场景影响较小**: 在交通密度较低的简单场景中，性能差异可能较小
3. **复杂场景影响较大**: 在多车辆交互的复杂场景中，缺少GNN会显著影响性能

## 📈 与原始模型对比

训练完成后，可以通过以下方式对比：

1. **训练损失曲线**: 比较两个模型的收敛速度和最终损失
2. **测试性能**: 在相同测试集上比较各项指标
3. **复杂度分析**: 比较模型参数量和推理速度

## 🎯 预期结论

这个消融实验将帮助我们：

1. **量化GNN的贡献**: 通过性能差异量化GNN的重要性
2. **验证架构设计**: 证明GNN+Transformer混合架构的合理性
3. **指导未来改进**: 为进一步的架构优化提供依据

## 📝 注意事项

1. **环境一致性**: 确保训练和测试环境配置与原始实验一致
2. **随机种子**: 使用相同的随机种子确保结果可重现
3. **硬件要求**: 建议使用GPU加速训练，CPU训练会很慢
4. **内存使用**: 注意监控内存使用，必要时调整batch_size

## 🔄 实验流程

1. **训练消融模型**: 运行 `main.py` 训练纯Transformer模型
2. **测试消融模型**: 使用 `test_model.py` 评估模型性能
3. **对比分析**: 将结果与原始GNN+Transformer模型对比
4. **撰写报告**: 分析性能差异，得出结论
