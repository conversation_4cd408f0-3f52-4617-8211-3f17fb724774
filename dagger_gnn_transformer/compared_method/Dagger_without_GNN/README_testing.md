# 模型性能测试工具使用说明

## 📋 概述

本目录提供了两个模型性能测试脚本，用于全面评估训练好的DAGGER模型的性能：

1. **`test_model_performance.py`** - 全面性能测试（推荐）
2. **`quick_test.py`** - 快速性能测试

## 🎯 测试指标

### 基础成功指标
- **成功率**: 成功到达目的地的比例
- **碰撞率**: 发生碰撞的比例  
- **出路率**: 驶出道路的比例
- **超时率**: 超过最大步数的比例

### 路线完成度
- **平均完成度**: 平均路线完成百分比
- **完成度分布**: 最高、最低、标准差

### 时间与效率
- **平均步数**: 每个episode的平均步数
- **平均时间**: 每个episode的平均耗时
- **总测试时间**: 完整测试的总耗时

### 速度表现
- **平均速度**: 行驶过程中的平均速度
- **速度范围**: 最高和最低速度
- **速度稳定性**: 速度变化的标准差

### 奖励表现
- **总奖励**: 每个episode的累计奖励
- **步奖励**: 每步的平均奖励

### 驾驶行为分析
- **转向方差**: 转向动作的变化程度
- **油门方差**: 油门动作的变化程度  
- **动作平滑度**: 相邻动作的差异程度

### 行驶统计
- **行驶距离**: 实际行驶的距离
- **燃油效率**: 距离/时间比率

### 安全表现
- **安全违规**: 距离道路边界过近的次数
- **险情次数**: 与其他车辆距离过近的次数
- **车道变换**: 车道变换的次数

### 综合评分
基于多个指标的加权综合评分（0-100分）

## 🚀 使用方法

### 1. 全面性能测试（推荐）

```bash
# 自动查找最新模型并测试
python test_model_performance.py

# 测试指定模型
python test_model_performance.py --model_path saved_model/xxx/best_model_by_completion.pth --episodes 100 --render
```

**特点**：
- 测试100个episodes（可调整）
- 提供详细的统计分析
- 自动保存测试结果到JSON文件
- 计算综合评分

### 2. 快速性能测试

```bash
# 快速测试（20个episodes）
python quick_test.py
```

**特点**：
- 快速测试，只需几分钟
- 提供基础性能指标
- 适合快速验证模型效果

## 📊 输出示例

### 控制台输出
```
📊 模型性能测试结果
============================================================

🎯 基础成功指标:
   总测试episodes: 100
   成功率: 85.00%
   碰撞率: 5.00%
   出路率: 10.00%
   超时率: 0.00%

🛣️ 路线完成度:
   平均完成度: 92.50% (±15.30%)
   最高完成度: 100.00%
   最低完成度: 45.20%

⏱️ 时间与步数:
   平均步数: 456.7 (±123.4)
   步数范围: 234 - 789
   平均episode时间: 12.34s (±3.45s)
   总测试时间: 1234.56s (20.6分钟)

🚗 速度表现:
   平均速度: 45.67 km/h (±8.90)
   速度范围: 12.34 - 78.90 km/h

🏆 奖励表现:
   平均总奖励: 123.45 (±23.45)
   平均步奖励: 0.2345

🎮 驾驶行为:
   转向方差: 0.012345
   油门方差: 0.023456
   动作平滑度: 0.034567

📏 行驶统计:
   平均行驶距离: 1234.56m
   总行驶距离: 123456.78m
   平均燃油效率: 12.34 m/s

🛡️ 安全表现:
   平均安全违规: 2.34次/episode
   总安全违规: 234次
   平均险情: 1.23次/episode
   总险情: 123次
   平均车道变换: 3.45次/episode
   总车道变换: 345次

⭐ 综合评分: 87.65/100
```

### 保存的文件

测试完成后会在`test_results/`目录下保存：

1. **`detailed_results_YYYYMMDD_HHMMSS.json`** - 详细结果
   - 包含每个episode的完整数据
   - 总体统计信息
   - 测试配置信息

2. **`summary_YYYYMMDD_HHMMSS.json`** - 简化统计
   - 关键性能指标
   - 综合评分
   - 模型信息

## 🔧 自定义配置

### 修改测试参数

在脚本中可以调整以下参数：

```python
# 测试episodes数量
test_episodes = 100

# 是否显示渲染
render = False

# 最大步数
max_steps = 1000

# 测试环境配置
test_env_config = {
    "traffic_density": 0.1,  # 交通密度
    "map": "SSSSS",          # 地图类型
    "start_seed": 1000,      # 随机种子
    # ... 其他配置
}
```

### 修改评分权重

在`_calculate_overall_score`函数中可以调整评分权重：

```python
# 成功率权重40%
success_score = stats['success_rate'] * 0.4

# 路线完成度权重30%  
completion_score = min(stats['avg_route_completion'], 100) * 0.3

# 安全性权重20%
safety_score = max(0, 100 - stats['collision_rate']) * 0.2

# 效率权重10%
efficiency_score = min(stats['avg_speed'] / 60 * 100, 100) * 0.1
```

## 📈 结果分析建议

### 优秀模型的指标参考
- **成功率**: > 80%
- **碰撞率**: < 10%
- **平均路线完成度**: > 85%
- **综合评分**: > 80分

### 问题诊断
- **成功率低**: 可能需要更多训练或调整网络结构
- **碰撞率高**: 需要加强安全性训练
- **完成度低**: 可能路径规划有问题
- **速度过低**: 可能过于保守，需要调整奖励函数

## 🛠️ 故障排除

### 常见问题

1. **找不到模型文件**
   ```
   ❌ 没有找到saved_model目录，请先训练模型
   ```
   解决：确保已经运行过训练脚本并生成了模型文件

2. **CUDA内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   解决：减少batch_size或使用CPU测试

3. **环境初始化失败**
   ```
   MetaDrive environment initialization failed
   ```
   解决：检查MetaDrive安装是否正确

### 性能优化

- 使用GPU可以显著加速测试
- 关闭渲染可以提高测试速度
- 减少测试episodes数量可以快速获得初步结果

## 📝 注意事项

1. 测试环境配置应与训练时保持一致
2. 建议在不同地图和交通密度下进行测试
3. 多次测试取平均值可以获得更稳定的结果
4. 保存的测试结果可用于模型对比和分析
