# 架构对比分析：原版 vs 消融实验版本

## 📊 总体对比

| 方面 | 原版 (dagger_gnn_transformer) | 消融实验版本 (Dagger_without_GNN) |
|------|-------------------------------|-----------------------------------|
| **核心架构** | GNN + Transformer | 纯 Transformer |
| **模型文件** | GraphTransformerModel | TransformerModel |
| **输入处理** | 图数据 + 序列数据 | 传感器数据 + 自车状态 |
| **特征融合** | 图特征 + 序列特征 | 直接拼接特征 |

## 🏗️ 模型架构差异

### 原版 (GraphTransformerModel)
```
输入: 
- graph_batch (图数据): 车辆节点 + 边关系
- sequence_features (序列数据): 传感器特征 [72维]

处理流程:
1. GNN处理图数据 → 节点嵌入
2. 全局池化 → 图特征 [hidden_dim]
3. 图特征投影 → [transformer_dim // 2]
4. 序列特征投影 → [transformer_dim // 2]
5. 特征拼接 → [transformer_dim]
6. 特征融合层
7. Transformer处理
8. 动作/价值预测
```

### 消融实验版本 (TransformerModel)
```
输入:
- sequence_features: 传感器特征 [72维]
- ego_features: 自车状态 [7维]

处理流程:
1. 特征拼接 → [79维]
2. 输入投影 → [transformer_dim]
3. Transformer处理
4. 动作/价值预测
```

## ⚙️ 配置参数对比

### 模型配置差异

| 参数 | 原版 | 消融版本 | 说明 |
|------|------|----------|------|
| `hidden_dim` | 128 | ❌ 无 | GNN隐藏层维度 |
| `num_layers` | 2 | ❌ 无 | GNN层数 |
| `max_vehicles` | 10 | ❌ 无 | 图中最大车辆数 |
| `npc_node_dim` | 7 | ❌ 无 | NPC节点特征维度 |
| `max_npc_distance` | 30 | ❌ 无 | NPC距离阈值 |
| `transformer_dim` | 256 | ✅ 256 | Transformer维度 |
| `num_transformer_layers` | 4 | ✅ 4 | Transformer层数 |
| `num_heads` | 8 | ✅ 8 | 注意力头数 |
| `sequence_dim` | 72 | ✅ 72 | 传感器特征维度 |
| `ego_node_dim` | 7 | ✅ 7 | 自车状态维度 |

### 训练配置对比

| 参数 | 原版 | 消融版本 | 差异 |
|------|------|----------|------|
| `episodes_per_iteration` | 10 | 1 | 🔴 不同 |
| `num_workers` | 10 | 1 | 🔴 不同 |
| `use_render` | False | True | 🔴 不同 |
| 其他训练参数 | ✅ 相同 | ✅ 相同 | ✅ 一致 |

## 🔄 数据处理差异

### 原版数据处理
```python
# 需要构建图数据
graph_data = Data(
    x=node_features,      # [num_nodes, node_dim]
    edge_index=edge_index # [2, num_edges]
)

batch_data = {
    'graph_batch': Batch.from_data_list([graph_data]),
    'sequence_features': sensor_features
}
```

### 消融版本数据处理
```python
# 直接使用特征
batch_data = {
    'sequence_features': sensor_features,  # [72]
    'ego_features': ego_state             # [7]
}
```

## 🧠 网络复杂度对比

### 参数量对比
- **原版**: 约 4.2M 参数 (GNN + Transformer + 融合层)
- **消融版本**: 约 3.2M 参数 (纯 Transformer)

### 计算复杂度
- **原版**: O(V²) (图计算) + O(L²) (Transformer)
- **消融版本**: O(L²) (Transformer)

其中 V = 车辆数量, L = 序列长度

## ✅ 保持一致的部分

1. **专家策略**: 都使用 IDM 策略
2. **DAGGER算法**: 相同的 beta 调度和训练流程
3. **NPC限速**: 相同的 NPC 车辆速度控制
4. **损失函数**: 相同的 MSE 损失 + 安全损失
5. **动作约束**: 相同的转向和油门限制
6. **环境配置**: 相同的 MetaDrive 环境设置
7. **数据收集**: 相同的并行数据收集机制

## 🎯 实验目的

消融实验版本的目的是验证：
1. **GNN的必要性**: 图神经网络是否对性能有显著贡献
2. **架构简化**: 纯Transformer是否足够处理驾驶任务
3. **计算效率**: 去掉GNN后的性能和效率变化

## 📝 结论

两个版本的**唯一核心差异**就是：
- ✅ **原版**: 使用 GNN 处理车辆间空间关系 + Transformer 处理序列信息
- ✅ **消融版本**: 只使用 Transformer 处理拼接后的特征

其他所有方面（训练算法、专家策略、环境配置、NPC限速等）都保持一致，确保了对比实验的有效性。
