#!/usr/bin/env python3
"""
结果对比分析脚本
对比GNN+Transformer vs 纯Transformer的性能
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import argparse

def load_results(result_file):
    """加载测试结果"""
    if not Path(result_file).exists():
        return None
    
    with open(result_file, 'r') as f:
        return json.load(f)

def compare_models(gnn_results, no_gnn_results):
    """对比两个模型的结果"""
    print("📊 模型性能对比分析")
    print("="*60)
    
    metrics = [
        ('成功率', 'success_rate'),
        ('碰撞率', 'collision_rate'), 
        ('出路率', 'out_of_road_rate'),
        ('平均路线完成度', 'avg_route_completion'),
        ('平均奖励', 'avg_reward'),
        ('平均步数', 'avg_steps')
    ]
    
    print(f"{'指标':<15} {'GNN+Transformer':<20} {'纯Transformer':<20} {'差异':<15}")
    print("-" * 70)
    
    improvements = {}
    
    for metric_name, metric_key in metrics:
        gnn_val = gnn_results.get(metric_key, 0)
        no_gnn_val = no_gnn_results.get(metric_key, 0)
        
        if metric_key in ['success_rate', 'avg_route_completion', 'avg_reward', 'avg_steps']:
            # 这些指标越高越好
            diff = gnn_val - no_gnn_val
            diff_pct = (diff / max(no_gnn_val, 1e-6)) * 100
        else:
            # 碰撞率、出路率越低越好
            diff = no_gnn_val - gnn_val  # 反向计算
            diff_pct = (diff / max(gnn_val, 1e-6)) * 100
        
        improvements[metric_key] = diff_pct
        
        if metric_key in ['success_rate', 'collision_rate', 'out_of_road_rate', 'avg_route_completion']:
            gnn_str = f"{gnn_val:.1%}"
            no_gnn_str = f"{no_gnn_val:.1%}"
        else:
            gnn_str = f"{gnn_val:.2f}"
            no_gnn_str = f"{no_gnn_val:.2f}"
        
        diff_str = f"{diff_pct:+.1f}%"
        print(f"{metric_name:<15} {gnn_str:<20} {no_gnn_str:<20} {diff_str:<15}")
    
    print("\n🔍 分析总结:")
    
    # 计算总体性能提升
    key_metrics = ['success_rate', 'avg_route_completion']
    avg_improvement = np.mean([improvements[k] for k in key_metrics])
    
    if avg_improvement > 5:
        print(f"   ✅ GNN显著提升性能 (平均提升 {avg_improvement:.1f}%)")
    elif avg_improvement > 1:
        print(f"   ✅ GNN轻微提升性能 (平均提升 {avg_improvement:.1f}%)")
    elif avg_improvement > -1:
        print(f"   ⚖️ 两种架构性能相近 (差异 {avg_improvement:.1f}%)")
    else:
        print(f"   ❌ GNN未能提升性能 (平均下降 {abs(avg_improvement):.1f}%)")
    
    # 安全性分析
    safety_improvement = (improvements.get('collision_rate', 0) + improvements.get('out_of_road_rate', 0)) / 2
    if safety_improvement > 5:
        print(f"   🛡️ GNN显著提升安全性 (减少事故 {safety_improvement:.1f}%)")
    elif safety_improvement > 1:
        print(f"   🛡️ GNN轻微提升安全性 (减少事故 {safety_improvement:.1f}%)")
    
    return improvements

def plot_comparison(gnn_results, no_gnn_results, save_path=None):
    """绘制对比图表"""
    metrics = ['success_rate', 'collision_rate', 'out_of_road_rate', 'avg_route_completion']
    metric_names = ['成功率', '碰撞率', '出路率', '路线完成度']
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.flatten()
    
    for i, (metric, name) in enumerate(zip(metrics, metric_names)):
        gnn_val = gnn_results.get(metric, 0)
        no_gnn_val = no_gnn_results.get(metric, 0)
        
        bars = axes[i].bar(['GNN+Transformer', '纯Transformer'], 
                          [gnn_val, no_gnn_val],
                          color=['#2E86AB', '#A23B72'])
        
        axes[i].set_title(name, fontsize=14, fontweight='bold')
        axes[i].set_ylabel('比例' if metric != 'avg_route_completion' else '完成度')
        
        # 添加数值标签
        for bar, val in zip(bars, [gnn_val, no_gnn_val]):
            height = bar.get_height()
            axes[i].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{val:.1%}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.suptitle('DAGGER消融实验：GNN vs 纯Transformer性能对比', 
                 fontsize=16, fontweight='bold', y=1.02)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"📈 对比图表已保存: {save_path}")
    
    plt.show()

def main():
    parser = argparse.ArgumentParser(description='对比GNN+Transformer vs 纯Transformer结果')
    parser.add_argument('--gnn_results', type=str, help='GNN+Transformer结果文件路径')
    parser.add_argument('--no_gnn_results', type=str, help='纯Transformer结果文件路径')
    parser.add_argument('--save_plot', type=str, help='保存对比图表的路径')
    
    args = parser.parse_args()
    
    # 如果没有提供结果文件，尝试自动查找
    if not args.gnn_results:
        # 查找原始实验结果
        original_dir = Path("../../dagger_gnn_transformer")
        if original_dir.exists():
            result_dirs = list(original_dir.glob("dagger_results_*"))
            if result_dirs:
                latest_dir = max(result_dirs, key=lambda x: x.stat().st_mtime)
                potential_file = latest_dir / "test_results.json"
                if potential_file.exists():
                    args.gnn_results = str(potential_file)
    
    if not args.no_gnn_results:
        # 查找当前消融实验结果
        current_dir = Path(".")
        result_dirs = list(current_dir.glob("dagger_without_gnn_results_*"))
        if result_dirs:
            latest_dir = max(result_dirs, key=lambda x: x.stat().st_mtime)
            potential_file = latest_dir / "test_results.json"
            if potential_file.exists():
                args.no_gnn_results = str(potential_file)
    
    # 加载结果
    gnn_results = load_results(args.gnn_results) if args.gnn_results else None
    no_gnn_results = load_results(args.no_gnn_results) if args.no_gnn_results else None
    
    if not gnn_results:
        print("❌ 未找到GNN+Transformer结果文件")
        print("   请先运行原始实验或手动指定结果文件路径")
        return False
    
    if not no_gnn_results:
        print("❌ 未找到纯Transformer结果文件")
        print("   请先运行消融实验或手动指定结果文件路径")
        return False
    
    print("📁 使用的结果文件:")
    print(f"   GNN+Transformer: {args.gnn_results}")
    print(f"   纯Transformer: {args.no_gnn_results}")
    print()
    
    # 进行对比分析
    improvements = compare_models(gnn_results, no_gnn_results)
    
    # 绘制对比图表
    try:
        plot_comparison(gnn_results, no_gnn_results, args.save_plot)
    except Exception as e:
        print(f"⚠️ 绘图失败: {e}")
        print("   可能需要安装matplotlib: pip install matplotlib")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
