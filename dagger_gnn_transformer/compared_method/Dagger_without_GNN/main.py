#!/usr/bin/env python3
"""
DAGGER主训练脚本 - 消融实验版本
纯Transformer架构（去掉图神经网络），解决分布偏移问题
"""

import os
import sys
import torch
import torch.multiprocessing as mp
import numpy as np
from pathlib import Path
import logging

# 关闭MetaDrive的日志输出
logging.getLogger('metadrive').setLevel(logging.ERROR)
os.environ['METADRIVE_LOG_LEVEL'] = 'ERROR'

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.dagger_trainer import DaggerTrainer

def main():
    """主函数"""
    # 设置多进程启动方式为 'spawn'，以兼容CUDA
    try:
        mp.set_start_method('spawn', force=True)
        print("✅ 多进程启动方式已设置为 'spawn'")
    except RuntimeError:
        print("⚠️ 多进程启动方式已经设置过")
        pass

    print("🚀 DAGGER训练 - 纯Transformer（消融实验：去掉GNN）")
    print("="*60)
    
    # 配置参数 (与原始实验保持一致)
    config = {
        # 模型配置
        'transformer_dim': 256,        # Transformer主干网络维度
        'num_transformer_layers': 4,   # Transformer层数
        'num_heads': 8,                # Transformer注意力头数
        'dropout': 0.1,
        'sequence_dim': 72,            # 纯传感器信息维度 (30+30+12)
        'ego_node_dim': 7,             # 自车节点特征维度
        'action_dim': 2,               # 动作维度 [转向, 油门]

        # 训练配置
        'learning_rate': 1e-5,  # 适中的学习率
        'weight_decay': 1e-4,   # 适中的正则化
        'batch_size': 128,       # 增加批大小
        'training_epochs': 10,   # 减少训练轮数避免过拟合

        # DAGGER配置
        'num_iterations': 150,  # DAGGER迭代次数
        'episodes_per_iteration': 10,  # 每次迭代收集的episodes
        'max_steps_per_episode': 1000,  # 每个episode最大步数
        'buffer_size': 20000,  # 数据缓冲区大小

        # 专家策略配置 - 与原版一致
        'expert_type': 'idm',  # 使用IDM策略作为专家，实现真正的DAGGER训练（与原版一致）

        # Beta调度策略配置（解决后期性能下降问题）
        'beta_schedule': 'conservative',  # 保守策略，缓慢减少专家策略使用
        'min_beta': 0.1,                 # 最小专家策略比例40%（原来是10%）
        'max_beta': 1.0,                 # 最大专家策略比例100%

        # TensorBoard和验证配置
        'use_tensorboard': True,          # 启用TensorBoard记录
        'validation_episodes': 10,        # 验证时运行的episodes数量
        'validation_frequency': 3,        # 每3次迭代验证一次
        
        # 环境配置 (与原始实验保持一致)
        'env_config': {
            "out_of_road_done": True,
            "use_render": False,
            "num_scenarios": 10,
            "traffic_density": 0.1,
            "start_seed": 1,
            "map": "SSSSS",
            # "use_AI_protector": True,
            "accident_prob": 0,
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            }
        },

        # NPC车速控制配置 (与原版main.py保持一致)
        'npc_speed_config': {
            'use_slow_traffic': True,           # 是否启用低速NPC车辆
            # NPCSlowIDMPolicy配置 - 专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
            'npc_normal_speed': 10,             # NPCSlowIDMPolicy NORMAL_SPEED (km/h)
            'npc_max_speed': 10,                # NPCSlowIDMPolicy MAX_SPEED (km/h)
            'npc_creep_speed': 10,              # NPCSlowIDMPolicy CREEP_SPEED (km/h)
            'npc_acc_factor': 0.5,              # NPCSlowIDMPolicy ACC_FACTOR - 降低加速度因子
            # SlowIDMPolicy配置 - 自定义的低速IDM策略，将NPC车辆速度降低到10 km/h
            'slow_idm_normal_speed': 1,        # SlowIDMPolicy NORMAL_SPEED (km/h)
            'slow_idm_max_speed': 1,           # SlowIDMPolicy MAX_SPEED (km/h)
            'slow_idm_creep_speed': 1,         # SlowIDMPolicy CREEP_SPEED (km/h)
            'slow_idm_acc_factor': 0.8,         # SlowIDMPolicy ACC_FACTOR - 降低加速度因子
        },

        # 验证配置
        'validation_config': {

        }
    }
    
    print("配置参数:")
    print(f"  - Transformer维度: {config['transformer_dim']}")
    print(f"  - Transformer层数: {config['num_transformer_layers']}")
    print(f"  - 学习率: {config['learning_rate']}")
    print(f"  - DAGGER迭代: {config['num_iterations']}")
    print(f"  - 每次迭代episodes: {config['episodes_per_iteration']}")
    print(f"  - 专家策略: {config['expert_type']}")
    print(f"  - 交通密度: {config['env_config']['traffic_density']}")
    print(f"  - 启用低速NPC: {config['npc_speed_config']['use_slow_traffic']}")
    print(f"  - NPC正常速度: {config['npc_speed_config']['npc_normal_speed']} km/h")
    print("  - 模型类型: 纯Transformer（无GNN）")
    
    try:
        # 创建DAGGER训练器
        trainer = DaggerTrainer(config)
        
        # 开始并行DAGGER训练
        best_model_path = trainer.run_dagger_parallel(
            num_iterations=config['num_iterations'],
            episodes_per_iteration=config['episodes_per_iteration'],
            num_workers=10  # 使用10个并行进程
        )
        
        print(f"\n📊 训练结果:")
        print(f"最佳模型: {best_model_path}")
        print(f"最佳损失: {trainer.best_loss:.4f}")
        
        # 保存配置文件
        if best_model_path:
            config_path = Path(best_model_path).parent / "config.json"
            with open(config_path, 'w') as f:
                import json
                json.dump(config, f, indent=2)
            print(f"配置已保存: {config_path}")
        
    except Exception as e:
        print(f"\n❌ 训练过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 DAGGER训练成功完成!")
    else:
        print("\n💥 DAGGER训练失败!")
        sys.exit(1)
