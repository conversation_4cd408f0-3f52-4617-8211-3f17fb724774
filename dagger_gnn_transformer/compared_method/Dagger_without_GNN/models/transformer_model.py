"""
纯Transformer模型 - 去掉图神经网络的消融实验版本
直接使用传感器特征和车辆状态特征进行模仿学习
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Tuple, Optional

class TransformerBlock(nn.Module):
    """Transformer块"""
    
    def __init__(self, d_model: int, num_heads: int, ff_dim: int, dropout: float = 0.1):
        super().__init__()
        self.attention = nn.MultiheadAttention(d_model, num_heads, dropout=dropout, batch_first=True)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, ff_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(ff_dim, d_model)
        )
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        attn_output, _ = self.attention(x, x, x, key_padding_mask=mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        return x

class TransformerModel(nn.Module):
    """纯Transformer模型 - 消融实验版本（去掉GNN）"""
    
    def __init__(self, config: Dict):
        super().__init__()
        self.config = config
        
        # 从配置中获取参数
        self.sequence_dim = config.get('sequence_dim', 72)  # 传感器特征维度
        self.ego_state_dim = config.get('ego_node_dim', 7)  # 自车状态维度
        self.transformer_dim = config.get('transformer_dim', 256)
        self.num_heads = config.get('num_heads', 8)
        self.num_transformer_layers = config.get('num_transformer_layers', 4)
        self.dropout = config.get('dropout', 0.1)
        self.action_dim = config.get('action_dim', 2)
        
        # 计算输入特征总维度：传感器特征 + 自车状态
        self.input_dim = self.sequence_dim + self.ego_state_dim
        
        # 输入特征投影到Transformer维度
        self.input_projection = nn.Sequential(
            nn.Linear(self.input_dim, self.transformer_dim),
            nn.LayerNorm(self.transformer_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout)
        )
        
        # Transformer编码器
        self.transformer_layers = nn.ModuleList([
            TransformerBlock(
                d_model=self.transformer_dim,
                num_heads=self.num_heads,
                ff_dim=self.transformer_dim * 4,
                dropout=self.dropout
            ) for _ in range(self.num_transformer_layers)
        ])
        
        # 动作和价值输出头
        self.action_head = nn.Sequential(
            nn.Linear(self.transformer_dim, self.transformer_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.transformer_dim // 2, self.action_dim)
        )
        
        self.value_head = nn.Sequential(
            nn.Linear(self.transformer_dim, self.transformer_dim // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.transformer_dim // 2, 1)
        )

        print("✅ Pure Transformer Model Initialized (Without GNN)")
        print(f"   Input dimension: {self.input_dim}")
        print(f"   Transformer dimension: {self.transformer_dim}")
        print(f"   Number of parameters: {sum(p.numel() for p in self.parameters()):,}")

    def forward(self, batch_data: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        前向传播 - 消融实验版本（去掉GNN）
        
        Args:
            batch_data:
                - sequence_features: [batch_size, seq_dim] 传感器特征
                - ego_features: [batch_size, ego_dim] 自车状态特征
        """
        sequence_features = batch_data['sequence_features']  # [batch_size, 72]
        ego_features = batch_data['ego_features']  # [batch_size, 7]
        
        # 1. 特征拼接：传感器特征 + 自车状态
        # [batch_size, input_dim]
        combined_features = torch.cat([sequence_features, ego_features], dim=-1)
        
        # 2. 投影到Transformer维度
        # [batch_size, transformer_dim]
        projected_features = self.input_projection(combined_features)
        
        # 3. Transformer处理
        # Transformer期望输入 [batch_size, seq_len, d_model], 我们这里seq_len=1
        transformer_input = projected_features.unsqueeze(1)  # [batch_size, 1, transformer_dim]
        
        transformer_output = transformer_input
        for layer in self.transformer_layers:
            transformer_output = layer(transformer_output)
        
        # 提取最终特征
        final_features = transformer_output.squeeze(1)  # [batch_size, transformer_dim]
        
        # 4. 预测动作和价值
        action_pred = self.action_head(final_features)
        value_pred = self.value_head(final_features).squeeze(-1)
        
        # 对动作进行约束
        steering = torch.tanh(action_pred[:, 0]) * 0.8  # 限制在[-0.8, 0.8]
        throttle = torch.tanh(action_pred[:, 1])  # 限制在[-1, 1]
        
        action_pred = torch.stack([steering, throttle], dim=1)
        
        return {
            'action_pred': action_pred,
            'value_pred': value_pred
        }

def create_model(config: Dict = None) -> TransformerModel:
    """创建纯Transformer模型"""
    if config is None:
        # 提供一个用于独立测试的默认配置
        config = {
            'ego_node_dim': 7,
            'sequence_dim': 72,
            'transformer_dim': 256,
            'num_heads': 8,
            'num_transformer_layers': 4,
            'dropout': 0.1,
            'action_dim': 2
        }
    return TransformerModel(config)

# 测试函数
def test_model():
    print("=== Testing Pure Transformer Model (Without GNN) ===")
    
    config = {
        'ego_node_dim': 7,
        'sequence_dim': 72,
        'transformer_dim': 256,
        'num_heads': 8,
        'num_transformer_layers': 2,
        'dropout': 0.1,
        'action_dim': 2
    }
    model = create_model(config)
    model.eval()
    
    # 创建模拟数据
    batch_size = 4
    sequence_features = torch.randn(batch_size, config['sequence_dim'])
    ego_features = torch.randn(batch_size, config['ego_node_dim'])
    
    batch_data = {
        'sequence_features': sequence_features,
        'ego_features': ego_features
    }
    
    with torch.no_grad():
        outputs = model(batch_data)
    
    print("✅ Model test successful!")
    print(f"   Action prediction shape: {outputs['action_pred'].shape}")
    assert outputs['action_pred'].shape == (batch_size, 2)
    print(f"   Value prediction shape: {outputs['value_pred'].shape}")
    assert outputs['value_pred'].shape == (batch_size,)
    
    return True

if __name__ == "__main__":
    test_model()
