#!/usr/bin/env python3
"""
快速模型性能测试脚本
用于快速验证模型的基本性能指标
"""

import os
import sys
import time
import numpy as np
import torch
import logging
from pathlib import Path

# 关闭MetaDrive的日志输出
logging.getLogger('metadrive').setLevel(logging.ERROR)
os.environ['METADRIVE_LOG_LEVEL'] = 'ERROR'

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.dagger_trainer import get_pure_sensor_data
from tools.suppress_logs import quiet_metadrive
from models.transformer_model import TransformerModel

def quick_test_model(model_path, test_episodes=20, render=False):
    """快速测试模型性能"""
    print(f"🚀 快速模型性能测试")
    print(f"   模型路径: {model_path}")
    print(f"   测试episodes: {test_episodes}")
    print(f"   渲染: {render}")
    print("="*50)
    
    # 模型配置
    model_config = {
        'transformer_dim': 256,
        'num_transformer_layers': 4,
        'num_heads': 8,
        'dropout': 0.1,
        'sequence_dim': 72,
        'ego_node_dim': 7,
        'action_dim': 2,
    }
    
    # 设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 使用设备: {device}")
    
    # 加载模型
    model = TransformerModel(model_config).to(device)
    
    if os.path.exists(model_path):
        try:
            checkpoint = torch.load(model_path, map_location=device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ 模型加载成功")
        except Exception as e:
            print(f"⚠️ 模型加载失败: {e}")
            print(f"⚠️ 使用随机初始化模型")
    else:
        print(f"⚠️ 模型文件不存在，使用随机初始化模型")
    
    model.eval()
    
    # 测试环境配置 - 必须与训练时完全一致
    env_config = {
        "out_of_road_done": True,
        "use_render": render,
        "num_scenarios": 10,  # 🔧 修复：与训练时一致
        "traffic_density": 0.1,
        "start_seed": 1,  # 🔧 修复：与训练时一致
        "map": "SSSSS",
        "accident_prob": 0,  # 🔧 修复：与训练时一致（去掉.0）
        "vehicle_config": {
            "lidar": {"num_lasers": 30, "distance": 50},  # 🔧 修复：与训练时一致（30个激光）
            "side_detector": {"num_lasers": 30},
            "lane_line_detector": {"num_lasers": 12}
        }
    }
    
    # 统计变量
    results = {
        'success': 0,
        'collision': 0,
        'out_of_road': 0,
        'timeout': 0,
        'route_completions': [],
        'episode_steps': [],
        'episode_times': [],
        'avg_speeds': [],
        'total_rewards': []
    }
    
    print(f"\n🔄 开始测试...")
    
    with quiet_metadrive():
        from metadrive import MetaDriveEnv
        from tools.dagger_trainer import _patch_traffic_manager_for_slow_npc_worker

        # 🔧 关键修复：应用与训练时相同的NPC速度控制
        npc_speed_config = {
            'use_slow_traffic': True,
            'npc_normal_speed': 10,
            'npc_max_speed': 10,
            'npc_creep_speed': 10,
            'npc_acc_factor': 0.5,
            'slow_idm_normal_speed': 1,
            'slow_idm_max_speed': 1,
            'slow_idm_creep_speed': 1,
            'slow_idm_acc_factor': 0.8,
        }

        if npc_speed_config.get('use_slow_traffic', False):
            _patch_traffic_manager_for_slow_npc_worker(npc_speed_config)

        for episode in range(test_episodes):
            print(f"Episode {episode + 1}/{test_episodes}", end=" ")
            
            env = MetaDriveEnv(env_config)
            
            try:
                start_time = time.time()
                obs, info = env.reset()
                
                speeds = []
                rewards = []
                steps = 0
                
                for step in range(1000):  # 最大1000步
                    # 获取传感器特征
                    sensor_features = get_pure_sensor_data(obs)
                    
                    # 获取自车状态
                    ego = env.agent
                    ego_state = np.array([
                        ego.speed_km_h / 120.0, ego.heading_diff(ego.lane), ego.steering,
                        ego.throttle_brake, ego.dist_to_left_side, ego.dist_to_right_side,
                        np.linalg.norm(ego.velocity)
                    ], dtype=np.float32)
                    
                    # 模型预测
                    with torch.no_grad():
                        batch_input = {
                            'sequence_features': torch.from_numpy(sensor_features).float().unsqueeze(0).to(device),
                            'ego_features': torch.from_numpy(ego_state).float().unsqueeze(0).to(device)
                        }
                        outputs = model(batch_input)
                        action = outputs['action_pred'].cpu().numpy()[0]
                    
                    # 执行动作
                    obs, reward, terminated, truncated, info = env.step(action)
                    
                    speeds.append(ego.speed_km_h)
                    rewards.append(reward)
                    steps += 1
                    
                    if terminated or truncated:
                        break
                
                # 记录结果
                episode_time = time.time() - start_time
                agent = env.agent
                
                # 路线完成度
                try:
                    route_completion = getattr(agent.navigation, 'route_completion', 0.0) * 100
                except (AttributeError, TypeError):
                    route_completion = 0.0
                
                results['route_completions'].append(route_completion)
                results['episode_steps'].append(steps)
                results['episode_times'].append(episode_time)
                results['avg_speeds'].append(np.mean(speeds) if speeds else 0)
                results['total_rewards'].append(np.sum(rewards) if rewards else 0)
                
                # 判断终止原因
                if info.get('arrive_dest', False) or (hasattr(agent, 'arrive_destination') and agent.arrive_destination):
                    results['success'] += 1
                    status = "SUCCESS"
                elif info.get('crash', False) or (hasattr(agent, 'crash_vehicle') and agent.crash_vehicle) or (hasattr(agent, 'crash_object') and agent.crash_object):
                    results['collision'] += 1
                    status = "COLLISION"
                elif info.get('out_of_road', False) or (hasattr(agent, 'out_of_road') and agent.out_of_road):
                    results['out_of_road'] += 1
                    status = "OUT_OF_ROAD"
                else:
                    results['timeout'] += 1
                    status = "TIMEOUT"
                
                print(f"- {status} (完成度: {route_completion:.1f}%, 步数: {steps}, 时间: {episode_time:.1f}s)")
                
            except Exception as e:
                print(f"- ERROR: {e}")
            finally:
                env.close()
    
    # 计算统计结果
    print(f"\n" + "="*50)
    print(f"📊 测试结果统计")
    print(f"="*50)
    
    total = test_episodes
    print(f"\n🎯 基础指标:")
    print(f"   总测试episodes: {total}")
    print(f"   成功率: {results['success']/total*100:.1f}% ({results['success']}/{total})")
    print(f"   碰撞率: {results['collision']/total*100:.1f}% ({results['collision']}/{total})")
    print(f"   出路率: {results['out_of_road']/total*100:.1f}% ({results['out_of_road']}/{total})")
    print(f"   超时率: {results['timeout']/total*100:.1f}% ({results['timeout']}/{total})")
    
    if results['route_completions']:
        print(f"\n🛣️ 路线完成度:")
        print(f"   平均: {np.mean(results['route_completions']):.1f}%")
        print(f"   最高: {np.max(results['route_completions']):.1f}%")
        print(f"   最低: {np.min(results['route_completions']):.1f}%")
        print(f"   标准差: {np.std(results['route_completions']):.1f}%")
    
    if results['episode_steps']:
        print(f"\n⏱️ 步数统计:")
        print(f"   平均步数: {np.mean(results['episode_steps']):.1f}")
        print(f"   最多步数: {np.max(results['episode_steps'])}")
        print(f"   最少步数: {np.min(results['episode_steps'])}")
    
    if results['episode_times']:
        print(f"\n🕐 时间统计:")
        print(f"   平均时间: {np.mean(results['episode_times']):.1f}s")
        print(f"   总测试时间: {np.sum(results['episode_times']):.1f}s")
    
    if results['avg_speeds']:
        print(f"\n🚗 速度统计:")
        print(f"   平均速度: {np.mean(results['avg_speeds']):.1f} km/h")
        print(f"   最高平均速度: {np.max(results['avg_speeds']):.1f} km/h")
        print(f"   最低平均速度: {np.min(results['avg_speeds']):.1f} km/h")
    
    if results['total_rewards']:
        print(f"\n🏆 奖励统计:")
        print(f"   平均总奖励: {np.mean(results['total_rewards']):.2f}")
        print(f"   最高总奖励: {np.max(results['total_rewards']):.2f}")
        print(f"   最低总奖励: {np.min(results['total_rewards']):.2f}")
    
    # 简单评分
    success_rate = results['success']/total*100
    avg_completion = np.mean(results['route_completions']) if results['route_completions'] else 0
    collision_rate = results['collision']/total*100
    
    score = success_rate * 0.5 + min(avg_completion, 100) * 0.3 + max(0, 100-collision_rate) * 0.2
    print(f"\n⭐ 简单评分: {score:.1f}/100")
    
    return results

def main():
    """主函数"""
    # 查找最新的模型文件
    saved_model_dir = Path("saved_model")
    if not saved_model_dir.exists():
        print("❌ 没有找到saved_model目录，请先训练模型")
        return
    
    # 查找最佳模型
    best_models = []
    for model_dir in saved_model_dir.iterdir():
        if model_dir.is_dir():
            completion_model = model_dir / "best_model_by_completion.pth"
            loss_model = model_dir / "best_model_by_loss.pth"
            final_model = model_dir / "final_model.pth"
            
            if completion_model.exists():
                best_models.append(completion_model)
            elif loss_model.exists():
                best_models.append(loss_model)
            elif final_model.exists():
                best_models.append(final_model)
    
    if not best_models:
        print("❌ 没有找到训练好的模型文件")
        return
    
    # 选择最新的模型
    best_models.sort(key=lambda x: x.stat().st_mtime, reverse=True)
    model_path = best_models[0]
    
    print(f"📁 使用模型: {model_path}")
    
    # 运行快速测试
    results = quick_test_model(
        model_path=model_path,
        test_episodes=20,  # 快速测试，只测试20个episodes
        render=False
    )
    
    print(f"\n✅ 快速测试完成！")
    print(f"💡 如需更详细的测试，请运行: python test_model_performance.py")

if __name__ == "__main__":
    main()
