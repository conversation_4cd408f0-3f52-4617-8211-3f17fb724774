#!/usr/bin/env python3
"""
全面的模型性能测试脚本
测试指标：成功率、出路率、碰撞率、路线完成度、步数、时间、车速等
"""

import os
import sys
import time
import numpy as np
import torch
import logging
from pathlib import Path
import json
from datetime import datetime

class NumpyEncoder(json.JSONEncoder):
    """自定义JSON编码器，处理numpy数据类型"""
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyEncoder, self).default(obj)

# 关闭MetaDrive的日志输出
logging.getLogger('metadrive').setLevel(logging.ERROR)
os.environ['METADRIVE_LOG_LEVEL'] = 'ERROR'

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.dagger_trainer import get_pure_sensor_data
from tools.suppress_logs import quiet_metadrive
from models.transformer_model import TransformerModel

class ModelPerformanceTester:
    """模型性能测试器"""
    
    def __init__(self, model_path, config):
        self.model_path = model_path
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 加载模型
        self.model = self._load_model()
        
        print(f"🤖 模型性能测试器初始化完成")
        print(f"   模型路径: {model_path}")
        print(f"   设备: {self.device}")
        
    def _load_model(self):
        """加载训练好的模型"""
        model = TransformerModel(self.config).to(self.device)
        
        if os.path.exists(self.model_path):
            try:
                checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
                model.load_state_dict(checkpoint['model_state_dict'])
                print(f"✅ 模型加载成功: {self.model_path}")
            except Exception as e:
                print(f"⚠️ 模型加载失败: {e}")
                print(f"⚠️ 使用随机初始化模型: {self.model_path}")
        else:
            print(f"⚠️ 模型文件不存在，使用随机初始化模型: {self.model_path}")
            
        model.eval()
        return model
    
    def test_single_episode(self, env_config, episode_id=0, render=False, max_steps=1000):
        """测试单个episode的性能"""
        with quiet_metadrive():
            from metadrive import MetaDriveEnv
            from tools.dagger_trainer import _patch_traffic_manager_for_slow_npc_worker

            # 🔧 关键修复：应用与训练时相同的NPC速度控制
            npc_speed_config = {
                'use_slow_traffic': True,
                'npc_normal_speed': 10,
                'npc_max_speed': 10,
                'npc_creep_speed': 10,
                'npc_acc_factor': 0.5,
                'slow_idm_normal_speed': 1,
                'slow_idm_max_speed': 1,
                'slow_idm_creep_speed': 1,
                'slow_idm_acc_factor': 0.8,
            }

            if npc_speed_config.get('use_slow_traffic', False):
                _patch_traffic_manager_for_slow_npc_worker(npc_speed_config)

            env_config_copy = env_config.copy()
            env_config_copy["use_render"] = render  # 🔧 修复：使用传入的render参数
            env = MetaDriveEnv(env_config_copy)
            
        episode_metrics = {
            'episode_id': episode_id,
            'success': False,
            'collision': False,
            'out_of_road': False,
            'timeout': False,
            'route_completion': 0.0,
            'steps': 0,
            'episode_time': 0.0,
            'avg_speed': 0.0,
            'max_speed': 0.0,
            'min_speed': float('inf'),
            'total_reward': 0.0,
            'avg_reward': 0.0,
            'steering_variance': 0.0,
            'throttle_variance': 0.0,
            'action_smoothness': 0.0,
            'distance_traveled': 0.0,
            'fuel_efficiency': 0.0,
            'safety_violations': 0,
            'lane_changes': 0,
            'close_calls': 0
        }
        
        try:
            start_time = time.time()
            obs, info = env.reset()
            
            speeds = []
            rewards = []
            actions = []
            positions = []
            prev_lane = None
            
            for step in range(max_steps):
                # 获取传感器特征
                sensor_features = get_pure_sensor_data(obs)
                
                # 获取自车状态
                ego = env.agent
                ego_state = np.array([
                    ego.speed_km_h / 120.0, ego.heading_diff(ego.lane), ego.steering,
                    ego.throttle_brake, ego.dist_to_left_side, ego.dist_to_right_side,
                    np.linalg.norm(ego.velocity)
                ], dtype=np.float32)
                
                # 模型预测
                with torch.no_grad():
                    batch_input = {
                        'sequence_features': torch.from_numpy(sensor_features).float().unsqueeze(0).to(self.device),
                        'ego_features': torch.from_numpy(ego_state).float().unsqueeze(0).to(self.device)
                    }
                    outputs = self.model(batch_input)
                    action = outputs['action_pred'].cpu().numpy()[0]
                
                # 执行动作
                obs, reward, terminated, truncated, info = env.step(action)
                
                # 收集数据
                current_speed = ego.speed_km_h
                speeds.append(current_speed)
                rewards.append(reward)
                actions.append(action.copy())
                positions.append(np.array([ego.position[0], ego.position[1]]))
                
                # 检测车道变换
                current_lane = getattr(ego.lane, 'index', None) if hasattr(ego, 'lane') and ego.lane else None
                if prev_lane is not None and current_lane != prev_lane and current_lane is not None:
                    episode_metrics['lane_changes'] += 1
                prev_lane = current_lane
                
                # 检测安全违规（距离边界太近）
                if ego.dist_to_left_side < 1.0 or ego.dist_to_right_side < 1.0:
                    episode_metrics['safety_violations'] += 1
                
                # 检测险情（与其他车辆距离过近）
                min_distance_to_vehicles = float('inf')
                for v_id, v in env.engine.get_objects().items():
                    if v_id != ego.id and hasattr(v, 'position'):
                        distance = np.linalg.norm(ego.position - v.position)
                        min_distance_to_vehicles = min(min_distance_to_vehicles, distance)
                
                if min_distance_to_vehicles < 5.0:  # 5米内算险情
                    episode_metrics['close_calls'] += 1
                
                episode_metrics['steps'] = step + 1
                
                if terminated or truncated:
                    break
            
            # 计算episode结束时间
            episode_metrics['episode_time'] = time.time() - start_time
            
            # 获取最终状态
            agent = env.agent
            
            # 路线完成度
            try:
                episode_metrics['route_completion'] = getattr(agent.navigation, 'route_completion', 0.0) * 100
            except (AttributeError, TypeError):
                episode_metrics['route_completion'] = 0.0
            
            # 判断终止原因
            if info.get('arrive_dest', False) or (hasattr(agent, 'arrive_destination') and agent.arrive_destination):
                episode_metrics['success'] = True
            elif info.get('crash', False) or (hasattr(agent, 'crash_vehicle') and agent.crash_vehicle) or (hasattr(agent, 'crash_object') and agent.crash_object):
                episode_metrics['collision'] = True
            elif info.get('out_of_road', False) or (hasattr(agent, 'out_of_road') and agent.out_of_road):
                episode_metrics['out_of_road'] = True
            else:
                episode_metrics['timeout'] = True
            
            # 计算速度统计
            if speeds:
                episode_metrics['avg_speed'] = np.mean(speeds)
                episode_metrics['max_speed'] = np.max(speeds)
                episode_metrics['min_speed'] = np.min(speeds)
            
            # 计算奖励统计
            if rewards:
                episode_metrics['total_reward'] = np.sum(rewards)
                episode_metrics['avg_reward'] = np.mean(rewards)
            
            # 计算动作统计
            if actions:
                actions_array = np.array(actions)
                episode_metrics['steering_variance'] = np.var(actions_array[:, 0])
                episode_metrics['throttle_variance'] = np.var(actions_array[:, 1])
                
                # 动作平滑度（相邻动作的差异）
                if len(actions) > 1:
                    action_diffs = np.diff(actions_array, axis=0)
                    episode_metrics['action_smoothness'] = np.mean(np.linalg.norm(action_diffs, axis=1))
            
            # 计算行驶距离
            if len(positions) > 1:
                distances = [np.linalg.norm(positions[i+1] - positions[i]) for i in range(len(positions)-1)]
                episode_metrics['distance_traveled'] = np.sum(distances)
                
                # 燃油效率（距离/时间，简化指标）
                if episode_metrics['episode_time'] > 0:
                    episode_metrics['fuel_efficiency'] = episode_metrics['distance_traveled'] / episode_metrics['episode_time']
            
        except Exception as e:
            print(f"❌ Episode {episode_id} 测试失败: {e}")
            episode_metrics['error'] = str(e)
        finally:
            env.close()
        
        return episode_metrics
    
    def test_model_performance(self, test_episodes=100, render=False, save_results=True):
        """测试模型性能"""
        print(f"\n🚀 开始模型性能测试")
        print(f"   测试episodes: {test_episodes}")
        print(f"   渲染: {render}")
        print("="*60)
        
        # 测试环境配置 - 必须与训练时完全一致
        test_env_config = {
            "out_of_road_done": True,
            "use_render": True,
            "num_scenarios": 100,  # 与训练时一致
            "traffic_density": 0.1,  # 与训练时一致
            "start_seed": 10,  # 与训练时一致
            "map": "SS",  # 与训练时一致
            "accident_prob": 0,  # 与训练时一致
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50},  # 🔧 修复：与训练时一致（30个激光）
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            }
        }
        
        all_metrics = []
        
        for episode in range(test_episodes):
            print(f"🔄 测试 Episode {episode + 1}/{test_episodes}")
            
            episode_metrics = self.test_single_episode(
                test_env_config, 
                episode_id=episode,
                render=render,
                max_steps=1000
            )
            
            all_metrics.append(episode_metrics)
            
            # 显示进度
            if (episode + 1) % 10 == 0:
                success_rate = sum(1 for m in all_metrics if m['success']) / len(all_metrics) * 100
                avg_completion = np.mean([m['route_completion'] for m in all_metrics])
                print(f"   进度: {episode + 1}/{test_episodes}, 当前成功率: {success_rate:.1f}%, 平均完成度: {avg_completion:.1f}%")
        
        # 计算总体统计
        overall_stats = self._calculate_overall_statistics(all_metrics)
        
        # 打印结果
        self._print_results(overall_stats)
        
        # 保存结果
        if save_results:
            self._save_results(all_metrics, overall_stats)
        
        return all_metrics, overall_stats

    def _calculate_overall_statistics(self, all_metrics):
        """计算总体统计数据"""
        valid_metrics = [m for m in all_metrics if 'error' not in m]
        total_episodes = len(valid_metrics)

        if total_episodes == 0:
            return {}

        stats = {
            'total_episodes': total_episodes,
            'success_rate': sum(1 for m in valid_metrics if m['success']) / total_episodes * 100,
            'collision_rate': sum(1 for m in valid_metrics if m['collision']) / total_episodes * 100,
            'out_of_road_rate': sum(1 for m in valid_metrics if m['out_of_road']) / total_episodes * 100,
            'timeout_rate': sum(1 for m in valid_metrics if m['timeout']) / total_episodes * 100,

            # 路线完成度统计
            'avg_route_completion': np.mean([m['route_completion'] for m in valid_metrics]),
            'std_route_completion': np.std([m['route_completion'] for m in valid_metrics]),
            'max_route_completion': np.max([m['route_completion'] for m in valid_metrics]),
            'min_route_completion': np.min([m['route_completion'] for m in valid_metrics]),

            # 步数统计
            'avg_steps': np.mean([m['steps'] for m in valid_metrics]),
            'std_steps': np.std([m['steps'] for m in valid_metrics]),
            'max_steps': np.max([m['steps'] for m in valid_metrics]),
            'min_steps': np.min([m['steps'] for m in valid_metrics]),

            # 时间统计
            'avg_episode_time': np.mean([m['episode_time'] for m in valid_metrics]),
            'std_episode_time': np.std([m['episode_time'] for m in valid_metrics]),
            'total_test_time': np.sum([m['episode_time'] for m in valid_metrics]),

            # 速度统计
            'avg_speed': np.mean([m['avg_speed'] for m in valid_metrics]),
            'std_avg_speed': np.std([m['avg_speed'] for m in valid_metrics]),
            'overall_max_speed': np.max([m['max_speed'] for m in valid_metrics]),
            'overall_min_speed': np.min([m['min_speed'] for m in valid_metrics if m['min_speed'] != float('inf')]),

            # 奖励统计
            'avg_total_reward': np.mean([m['total_reward'] for m in valid_metrics]),
            'std_total_reward': np.std([m['total_reward'] for m in valid_metrics]),
            'avg_reward_per_step': np.mean([m['avg_reward'] for m in valid_metrics]),

            # 驾驶行为统计
            'avg_steering_variance': np.mean([m['steering_variance'] for m in valid_metrics]),
            'avg_throttle_variance': np.mean([m['throttle_variance'] for m in valid_metrics]),
            'avg_action_smoothness': np.mean([m['action_smoothness'] for m in valid_metrics]),

            # 行驶统计
            'avg_distance_traveled': np.mean([m['distance_traveled'] for m in valid_metrics]),
            'total_distance_traveled': np.sum([m['distance_traveled'] for m in valid_metrics]),
            'avg_fuel_efficiency': np.mean([m['fuel_efficiency'] for m in valid_metrics]),

            # 安全统计
            'avg_safety_violations': np.mean([m['safety_violations'] for m in valid_metrics]),
            'total_safety_violations': np.sum([m['safety_violations'] for m in valid_metrics]),
            'avg_close_calls': np.mean([m['close_calls'] for m in valid_metrics]),
            'total_close_calls': np.sum([m['close_calls'] for m in valid_metrics]),
            'avg_lane_changes': np.mean([m['lane_changes'] for m in valid_metrics]),
            'total_lane_changes': np.sum([m['lane_changes'] for m in valid_metrics]),
        }

        return stats

    def _print_results(self, stats):
        """打印测试结果"""
        print(f"\n" + "="*60)
        print(f"📊 模型性能测试结果")
        print(f"="*60)

        print(f"\n🎯 基础成功指标:")
        print(f"   总测试episodes: {stats['total_episodes']}")
        print(f"   成功率: {stats['success_rate']:.2f}%")
        print(f"   碰撞率: {stats['collision_rate']:.2f}%")
        print(f"   出路率: {stats['out_of_road_rate']:.2f}%")
        print(f"   超时率: {stats['timeout_rate']:.2f}%")

        print(f"\n🛣️ 路线完成度:")
        print(f"   平均完成度: {stats['avg_route_completion']:.2f}% (±{stats['std_route_completion']:.2f}%)")
        print(f"   最高完成度: {stats['max_route_completion']:.2f}%")
        print(f"   最低完成度: {stats['min_route_completion']:.2f}%")

        print(f"\n⏱️ 时间与步数:")
        print(f"   平均步数: {stats['avg_steps']:.1f} (±{stats['std_steps']:.1f})")
        print(f"   步数范围: {stats['min_steps']} - {stats['max_steps']}")
        print(f"   平均episode时间: {stats['avg_episode_time']:.2f}s (±{stats['std_episode_time']:.2f}s)")
        print(f"   总测试时间: {stats['total_test_time']:.2f}s ({stats['total_test_time']/60:.1f}分钟)")

        print(f"\n🚗 速度表现:")
        print(f"   平均速度: {stats['avg_speed']:.2f} km/h (±{stats['std_avg_speed']:.2f})")
        print(f"   速度范围: {stats['overall_min_speed']:.2f} - {stats['overall_max_speed']:.2f} km/h")

        print(f"\n🏆 奖励表现:")
        print(f"   平均总奖励: {stats['avg_total_reward']:.2f} (±{stats['std_total_reward']:.2f})")
        print(f"   平均步奖励: {stats['avg_reward_per_step']:.4f}")

        print(f"\n🎮 驾驶行为:")
        print(f"   转向方差: {stats['avg_steering_variance']:.6f}")
        print(f"   油门方差: {stats['avg_throttle_variance']:.6f}")
        print(f"   动作平滑度: {stats['avg_action_smoothness']:.6f}")

        print(f"\n📏 行驶统计:")
        print(f"   平均行驶距离: {stats['avg_distance_traveled']:.2f}m")
        print(f"   总行驶距离: {stats['total_distance_traveled']:.2f}m")
        print(f"   平均燃油效率: {stats['avg_fuel_efficiency']:.2f} m/s")

        print(f"\n🛡️ 安全表现:")
        print(f"   平均安全违规: {stats['avg_safety_violations']:.2f}次/episode")
        print(f"   总安全违规: {stats['total_safety_violations']:.0f}次")
        print(f"   平均险情: {stats['avg_close_calls']:.2f}次/episode")
        print(f"   总险情: {stats['total_close_calls']:.0f}次")
        print(f"   平均车道变换: {stats['avg_lane_changes']:.2f}次/episode")
        print(f"   总车道变换: {stats['total_lane_changes']:.0f}次")

        # 综合评分
        overall_score = self._calculate_overall_score(stats)
        print(f"\n⭐ 综合评分: {overall_score:.2f}/100")

    def _calculate_overall_score(self, stats):
        """计算综合评分（0-100分）"""
        # 成功率权重40%
        success_score = stats['success_rate'] * 0.4

        # 路线完成度权重30%
        completion_score = min(stats['avg_route_completion'], 100) * 0.3

        # 安全性权重20%（碰撞率越低越好）
        safety_score = max(0, 100 - stats['collision_rate']) * 0.2

        # 效率权重10%（基于平均速度和燃油效率）
        efficiency_score = min(stats['avg_speed'] / 60 * 100, 100) * 0.1

        return success_score + completion_score + safety_score + efficiency_score

    def _save_results(self, all_metrics, overall_stats):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = Path("test_results")
        results_dir.mkdir(exist_ok=True)

        # 保存详细结果
        detailed_file = results_dir / f"detailed_results_{timestamp}.json"
        with open(detailed_file, 'w', encoding='utf-8') as f:
            json.dump({
                'test_info': {
                    'model_path': str(self.model_path),
                    'timestamp': timestamp,
                    'device': str(self.device)
                },
                'overall_statistics': overall_stats,
                'episode_details': all_metrics
            }, f, indent=2, ensure_ascii=False, cls=NumpyEncoder)

        # 保存简化统计
        summary_file = results_dir / f"summary_{timestamp}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump({
                'model_path': str(self.model_path),
                'timestamp': timestamp,
                'overall_score': self._calculate_overall_score(overall_stats),
                'key_metrics': {
                    'success_rate': overall_stats['success_rate'],
                    'collision_rate': overall_stats['collision_rate'],
                    'out_of_road_rate': overall_stats['out_of_road_rate'],
                    'avg_route_completion': overall_stats['avg_route_completion'],
                    'avg_speed': overall_stats['avg_speed'],
                    'avg_reward_per_step': overall_stats['avg_reward_per_step']
                }
            }, f, indent=2, ensure_ascii=False, cls=NumpyEncoder)

        print(f"\n💾 结果已保存:")
        print(f"   详细结果: {detailed_file}")
        print(f"   简化统计: {summary_file}")


def main():
    """主函数"""
    print("🚀 模型性能测试工具")
    print("="*60)

    # 模型配置（与训练时保持一致）
    model_config = {
        'transformer_dim': 256,
        'num_transformer_layers': 4,
        'num_heads': 8,
        'dropout': 0.1,
        'sequence_dim': 72,
        'ego_node_dim': 7,
        'action_dim': 2,
    }

    # 查找最新的模型文件
    saved_model_dir = Path("saved_model")
    if not saved_model_dir.exists():
        print("❌ 没有找到saved_model目录，请先训练模型")
        return

    # 查找最佳模型
    best_models = []
    for model_dir in saved_model_dir.iterdir():
        if model_dir.is_dir():
            # 优先选择基于路线完成度的最佳模型
            completion_model = model_dir / "best_model_by_completion.pth"
            loss_model = model_dir / "best_model_by_loss.pth"
            final_model = model_dir / "final_model.pth"

            if completion_model.exists():
                best_models.append((completion_model, "completion"))
            elif loss_model.exists():
                best_models.append((loss_model, "loss"))
            elif final_model.exists():
                best_models.append((final_model, "final"))

    if not best_models:
        print("❌ 没有找到训练好的模型文件")
        return

    # 选择最新的模型
    best_models.sort(key=lambda x: x[0].stat().st_mtime, reverse=True)
    model_path, model_type = best_models[0]

    print(f"📁 找到模型: {model_path}")
    print(f"📊 模型类型: {model_type}")

    # 创建测试器
    tester = ModelPerformanceTester(model_path, model_config)

    # 运行测试
    print(f"\n🎯 开始性能测试...")

    # 可以调整测试参数
    test_episodes = 100  # 测试episode数量
    render = False       # 是否显示渲染

    try:
        all_metrics, overall_stats = tester.test_model_performance(
            test_episodes=test_episodes,
            render=render,
            save_results=True
        )

        print(f"\n✅ 测试完成！")

    except KeyboardInterrupt:
        print(f"\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def test_specific_model():
    """测试指定模型的性能"""
    import argparse

    parser = argparse.ArgumentParser(description='测试指定模型的性能')
    parser.add_argument('--model_path', type=str, required=True, help='模型文件路径')
    parser.add_argument('--episodes', type=int, default=100, help='测试episode数量')
    parser.add_argument('--render', action='store_true', help='是否显示渲染',default=False)

    args = parser.parse_args()

    # 模型配置
    model_config = {
        'transformer_dim': 256,
        'num_transformer_layers': 4,
        'num_heads': 8,
        'dropout': 0.1,
        'sequence_dim': 72,
        'ego_node_dim': 7,
        'action_dim': 2,
    }

    # 创建测试器
    model_path = 'saved_model/dagger_without_gnn_20250713_124046/best_model_by_completion.pth'
    # model_path = "/home/<USER>/rl/RL-IL/dagger_gnn_transformer/saved_model/dagger_without_gnn_20250713_123818/best_model_by_completion.pth"
    tester = ModelPerformanceTester(model_path, model_config)

    # 运行测试
    all_metrics, overall_stats = tester.test_model_performance(
        test_episodes=args.episodes,
        render=args.render,
        save_results=True
    )

    return all_metrics, overall_stats


if __name__ == "__main__":
    main()
