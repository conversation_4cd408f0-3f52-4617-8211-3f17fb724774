#!/usr/bin/env python3
"""
DAGGER训练器 - 纯Transformer架构 (消融实验版本)
去掉图神经网络，使用DAGGER算法解决分布偏移问题
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import json
import time
from pathlib import Path
from datetime import datetime
from collections import deque
import random
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import threading
import queue
from torch.utils.tensorboard import SummaryWriter

# 导入日志抑制工具
from tools.suppress_logs import setup_quiet_mode, quiet_metadrive

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.transformer_model import TransformerModel

def normalize_expert_action(action):
    """
    将专家动作归一化到模型输出的相同范围

    Args:
        action: [steering, throttle]

    Returns:
        normalized_action: [steering, throttle] 归一化后的动作
    """
    steering, throttle = action[0], action[1]

    # 对转向进行归一化：限制在[-0.8, 0.8]范围内（与模型输出一致）
    steering_normalized = np.clip(steering, -0.8, 0.8)

    # 对油门进行归一化：限制在[-1, 1]范围内（与模型输出一致）
    throttle_normalized = np.clip(throttle, -1.0, 1.0)

    return np.array([steering_normalized, throttle_normalized], dtype=np.float32)

# 动态创建NPC车速策略类的全局变量
_NPCSlowIDMPolicy = None
_SlowIDMPolicy = None

def create_npc_speed_policies(npc_speed_config):
    """根据配置动态创建NPC车速策略类"""
    global _NPCSlowIDMPolicy, _SlowIDMPolicy

    from metadrive.policy.idm_policy import IDMPolicy

    # 创建专门用于NPC车辆的低速IDM策略
    class NPCSlowIDMPolicy(IDMPolicy):
        """
        专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
        """
        NORMAL_SPEED = npc_speed_config.get('npc_normal_speed', 10)  # km/h
        MAX_SPEED = npc_speed_config.get('npc_max_speed', 10)  # km/h
        CREEP_SPEED = npc_speed_config.get('npc_creep_speed', 10)  # km/h
        ACC_FACTOR = npc_speed_config.get('npc_acc_factor', 0.5)  # 降低加速度因子

        def __init__(self, control_object, random_seed):
            super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
            self.target_speed = self.NORMAL_SPEED

    # 创建自定义的低速IDM策略
    class SlowIDMPolicy(IDMPolicy):
        """
        自定义的低速IDM策略，将NPC车辆速度降低到10 km/h
        """
        NORMAL_SPEED = npc_speed_config.get('slow_idm_normal_speed', 15)  # km/h
        MAX_SPEED = npc_speed_config.get('slow_idm_max_speed', 15)  # km/h
        CREEP_SPEED = npc_speed_config.get('slow_idm_creep_speed', 10)  # km/h
        ACC_FACTOR = npc_speed_config.get('slow_idm_acc_factor', 0.8)  # 降低加速度因子

        def __init__(self, control_object, random_seed):
            super(SlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
            # 确保目标速度设置为低速
            self.target_speed = self.NORMAL_SPEED

    _NPCSlowIDMPolicy = NPCSlowIDMPolicy
    _SlowIDMPolicy = SlowIDMPolicy

    return NPCSlowIDMPolicy, SlowIDMPolicy

def _patch_traffic_manager_for_slow_npc_worker(npc_speed_config):
    """
    在工作进程中应用NPC车速控制猴子补丁
    """
    from metadrive.policy.idm_policy import IDMPolicy

    # 动态创建NPC策略类
    NPCSlowIDMPolicy, _ = create_npc_speed_policies(npc_speed_config)

    import metadrive.manager.traffic_manager as tm

    # 保存原始的add_policy方法
    original_add_policy = tm.PGTrafficManager.add_policy

    def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
        # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
        if policy_class == IDMPolicy:
            policy_class = NPCSlowIDMPolicy
        return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

    # 应用patch
    tm.PGTrafficManager.add_policy = patched_add_policy

def get_pure_sensor_data(obs: np.ndarray) -> np.ndarray:
    """从原始88维观测中提取纯72维传感器数据."""
    if not isinstance(obs, np.ndarray):
        obs = np.array(obs).flatten()

    if obs.shape[0] != 88:
        return np.zeros(72, dtype=np.float32)

    # This is the corrected slicing based on the structure of LidarStateObservation
    # The 88-dim vector is composed of: state_obs (58) + lidar (30)
    # The state_obs (58) is composed of: side_detector (30) + ego_state (6) + lane_detector (12) + navi (10)
    side_detector_obs = obs[0:30]
    lane_line_detector_obs = obs[36:48]
    lidar_obs = obs[58:88]

    # Concatenate in the order: [main_lidar, side_detector, lane_detector]
    pure_sensors = np.concatenate([lidar_obs, side_detector_obs, lane_line_detector_obs])
    return pure_sensors.astype(np.float32)

class ExpertPolicy:
    """专家策略"""
    def __init__(self, policy_type="IDM"):
        self.policy_type = policy_type
        
    def get_action(self, env):
        """获取专家动作"""
        if hasattr(env, 'agent') and hasattr(env.agent, 'expert_action'):
            return env.agent.expert_action
        return [0.0, 0.0]

def collect_episode_data_worker(args):
    """工作进程：收集单个episode的数据"""
    worker_id, model_config, env_config, beta, episode_id = args

    with quiet_metadrive():
        from metadrive import MetaDriveEnv
        from metadrive.policy.expert_policy import ExpertPolicy as MetaDriveExpertPolicy
        from metadrive.policy.idm_policy import IDMPolicy

        # 在创建环境之前应用NPC车速控制猴子补丁
        npc_speed_config = model_config.get('npc_speed_config', {})
        if npc_speed_config.get('use_slow_traffic', False):
            _patch_traffic_manager_for_slow_npc_worker(npc_speed_config)

        # 根据配置选择专家策略（与原版一致）
        expert_type = model_config.get('expert_type', 'expert')
        if expert_type == 'idm':
            policy_class = IDMPolicy
        else:
            policy_class = MetaDriveExpertPolicy

        # 设置专家策略到环境配置中
        env_config_copy = env_config.copy()
        env_config_copy["agent_policy"] = policy_class

        env = MetaDriveEnv(env_config_copy)
        expert_policy = ExpertPolicy(expert_type)

        episode_data = []

        try:
            # 使用固定种子，因为只有一个场景
            # seed = env_config.get('start_seed', 4)
            obs, info = env.reset()
            max_steps = model_config.get('max_steps_per_episode', 1000)

            # 🔧 关键修复：为每个episode重新创建IDM专家策略（与原版一致）
            # 因为env.reset()后agent对象会改变，IDM策略需要绑定到新的agent
            idm_policy = None
            if expert_type == 'idm':
                idm_policy = IDMPolicy(env.agent, random_seed=episode_id)

            for step in range(max_steps):
                sensor_features = get_pure_sensor_data(obs)

                ego = env.agent
                ego_state = np.array([
                    ego.speed_km_h / 120.0, ego.heading_diff(ego.lane), ego.steering,
                    ego.throttle_brake, ego.dist_to_left_side, ego.dist_to_right_side,
                    np.linalg.norm(ego.velocity)
                ], dtype=np.float32)

                # 获取IDM专家动作（与原版完全一致）
                if expert_type == 'idm' and idm_policy is not None:
                    # 直接从IDM策略获取动作
                    expert_action = idm_policy.act()
                    # 执行专家动作
                    obs, reward, terminated, truncated, info = env.step(expert_action)
                else:
                    # 使用环境内置的专家策略
                    action = [0.0, 0.0]  # 占位动作
                    obs, reward, terminated, truncated, info = env.step(action)
                    expert_action = info.get('action', action)

                # 确保专家动作是有效的
                if expert_action is None or len(expert_action) != 2:
                    expert_action = [0.0, 0.0]

                # 🔧 关键修复：对专家动作进行归一化（与原版一致）
                expert_action_normalized = normalize_expert_action(expert_action)

                # 打印调试信息（前几步）
                # if step < 3:
                #     print(f"    Worker {worker_id} Step {step}: expert_action = {expert_action} -> {expert_action_normalized}")

                # 采集边界距离用于安全损失计算
                dist_left = getattr(ego, 'dist_to_left_side', 0.0)
                dist_right = getattr(ego, 'dist_to_right_side', 0.0)

                step_data = {
                    'sensor_features': sensor_features,
                    'ego_features': ego_state,
                    'action_target': expert_action_normalized,  # 使用归一化后的动作
                    'dist_to_boundaries': np.array([dist_left, dist_right], dtype=np.float32)
                }
                episode_data.append(step_data)
                
                if terminated or truncated:
                    break
                    
        except Exception as e:
            print(f"Worker {worker_id} episode {episode_id} failed: {e}")
            return []
        finally:
            env.close()
            
        return episode_data

class DaggerTrainer:
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = TransformerModel(config).to(self.device)
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=config['learning_rate'], weight_decay=config['weight_decay'])
        self.loss_fn = nn.MSELoss()
        self.expert_policy = ExpertPolicy(config['expert_type'])
        self.data_buffer = deque(maxlen=config['buffer_size'])
        self.iteration_losses = []
        self.best_loss = float('inf')
        self.best_route_completion = 0.0
        self.validation_results = []

        # Beta调度策略配置（与原版一致）
        self.beta_schedule = config.get('beta_schedule', 'conservative')
        self.min_beta = config.get('min_beta', 0.1)
        self.max_beta = config.get('max_beta', 1.0)

        # NPC车速控制配置
        self.npc_speed_config = config.get('npc_speed_config', {})
        if self.npc_speed_config.get('use_slow_traffic', False):
            # 根据配置动态创建NPC车速策略类
            self.NPCSlowIDMPolicy, self.SlowIDMPolicy = create_npc_speed_policies(self.npc_speed_config)
            self._patch_traffic_manager_for_slow_npc()
            print("✅ 已将NPC车辆速度降低到10 km/h，自车保持正常速度")

        print(f"✅ DaggerTrainer Initialized (Without GNN)")
        print(f"🖥️ Device: {self.device}")
        print(f"🧠 Model Parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        print(f"👨‍🏫 Expert Policy: {config['expert_type']}")
        print(f"📊 Beta Schedule: {self.beta_schedule} (min: {self.min_beta}, max: {self.max_beta})")

    def _patch_traffic_manager_for_slow_npc(self):
        """
        通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy
        这样只有NPC车辆会使用低速，自车保持正常速度
        """
        from metadrive.policy.idm_policy import IDMPolicy
        import metadrive.manager.traffic_manager as tm

        # 保存原始的add_policy方法
        original_add_policy = tm.PGTrafficManager.add_policy

        # 获取动态创建的NPCSlowIDMPolicy类
        NPCSlowIDMPolicy = self.NPCSlowIDMPolicy

        def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
            # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
            if policy_class == IDMPolicy:
                policy_class = NPCSlowIDMPolicy
            return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

        # 应用patch
        tm.PGTrafficManager.add_policy = patched_add_policy

    def calculate_beta(self, iteration, total_iterations):
        """计算beta值（与原版完全一致）"""
        progress = iteration / max(total_iterations - 1, 1)
        if self.beta_schedule == 'linear':
            beta = self.max_beta - (self.max_beta - self.min_beta) * progress
        else:  # conservative
            beta = self.max_beta * (1 - progress) ** 0.5
        return max(self.min_beta, beta)

    def train_on_data(self, data_list, epochs=10):
        """在收集的数据上训练模型"""
        if not data_list:
            return float('inf')
            
        self.model.train()
        total_loss = 0.0
        num_batches = 0
        
        for epoch in range(epochs):
            random.shuffle(data_list)
            
            for i in range(0, len(data_list), self.config['batch_size']):
                batch_data = data_list[i:i + self.config['batch_size']]
                if len(batch_data) < 2:
                    continue
                    
                # 准备批次数据
                sensor_features = torch.stack([torch.from_numpy(d['sensor_features']) for d in batch_data]).float().to(self.device)
                ego_features = torch.stack([torch.from_numpy(d['ego_features']) for d in batch_data]).float().to(self.device)
                action_targets = torch.stack([torch.from_numpy(d['action_target']) for d in batch_data]).float().to(self.device)
                
                batch_input = {
                    'sequence_features': sensor_features,
                    'ego_features': ego_features
                }
                
                # 前向传播
                outputs = self.model(batch_input)
                action_pred = outputs['action_pred']
                
                # 计算损失
                action_loss = self.loss_fn(action_pred, action_targets)
                
                # 安全损失（可选）
                safety_loss = 0.0
                if 'dist_to_boundaries' in batch_data[0]:
                    boundaries = torch.stack([torch.from_numpy(d['dist_to_boundaries']) for d in batch_data]).float().to(self.device)
                    min_dist = torch.min(boundaries, dim=1)[0]
                    safety_loss = torch.mean(torch.clamp(0.5 - min_dist, min=0.0))
                
                total_loss_batch = action_loss + 0.1 * safety_loss
                
                # 反向传播
                self.optimizer.zero_grad()
                total_loss_batch.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()
                
                total_loss += total_loss_batch.item()
                num_batches += 1
        
        avg_loss = total_loss / max(num_batches, 1)
        return avg_loss

    def collect_data_parallel(self, num_episodes, beta, num_workers=4):
        """并行收集数据"""
        print(f"  🔄 Collecting {num_episodes} episodes with {num_workers} workers (beta={beta:.2f})")

        # 准备参数 - 简化版本，只使用专家策略收集数据
        args_list = [
            (i % num_workers, self.config, self.config['env_config'], beta, i)
            for i in range(num_episodes)
        ]
        
        all_data = []
        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            futures = [executor.submit(collect_episode_data_worker, args) for args in args_list]
            
            for future in as_completed(futures):
                try:
                    episode_data = future.result()
                    all_data.extend(episode_data)
                except Exception as e:
                    print(f"    ⚠️ Episode collection failed: {e}")
        
        return all_data

    def validate_model(self, num_episodes=10, render=False):
        """验证模型性能，返回路线完成度等指标"""

        # 设置验证环境配置
        val_config = self.config['env_config'].copy()
        val_config['use_render'] = render

        # 验证统计
        total_episodes = 0
        successful_episodes = 0
        total_steps = 0
        total_rewards = 0.0
        collision_count = 0
        out_of_road_count = 0
        timeout_count = 0
        route_completion_rates = []

        with quiet_metadrive():
            from metadrive import MetaDriveEnv
            env = MetaDriveEnv(val_config)

            try:
                for episode in range(num_episodes):
                    obs, info = env.reset()
                    episode_reward = 0.0
                    episode_steps = 0
                    max_steps = 1000  # 验证时使用更长的步数

                    for step in range(max_steps):
                        # 获取传感器特征
                        sensor_features = get_pure_sensor_data(obs)

                        # 获取自车状态
                        ego = env.agent
                        ego_state = np.array([
                            ego.speed_km_h / 120.0, ego.heading_diff(ego.lane), ego.steering,
                            ego.throttle_brake, ego.dist_to_left_side, ego.dist_to_right_side,
                            np.linalg.norm(ego.velocity)
                        ], dtype=np.float32)

                        # 模型预测
                        with torch.no_grad():
                            batch_data = {
                                'sequence_features': torch.from_numpy(sensor_features).float().unsqueeze(0).to(self.device),
                                'ego_features': torch.from_numpy(ego_state).float().unsqueeze(0).to(self.device)
                            }

                            outputs = self.model(batch_data)
                            action = outputs['action_pred'].cpu().numpy()[0]

                        # 执行动作
                        obs, reward, terminated, truncated, info = env.step(action)
                        episode_reward += reward
                        episode_steps += 1

                        if terminated or truncated:
                            break

                    # 统计结果
                    total_episodes += 1
                    total_steps += episode_steps
                    total_rewards += episode_reward

                    # 获取终止原因和路线完成度
                    agent = env.agent

                    # 获取当前路线完成度（与原版一致的获取方式）
                    try:
                        completion = getattr(agent.navigation, 'route_completion', 0.0) * 100  # 转换为百分比
                    except (AttributeError, TypeError):
                        completion = 0.0

                    # 检查终止原因（参考info字典，更准确）
                    if info.get('arrive_dest', False) or (hasattr(agent, 'arrive_destination') and agent.arrive_destination):
                        successful_episodes += 1
                        route_completion_rates.append(completion)  # 使用实际完成度
                        print(f"    Episode {episode + 1}: SUCCESS (completion: {completion:.1f}%)")
                    elif info.get('crash', False) or (hasattr(agent, 'crash_vehicle') and agent.crash_vehicle) or (hasattr(agent, 'crash_object') and agent.crash_object):
                        collision_count += 1
                        route_completion_rates.append(completion)
                        print(f"    Episode {episode + 1}: COLLISION (completion: {completion:.1f}%)")
                    elif info.get('out_of_road', False) or (hasattr(agent, 'out_of_road') and agent.out_of_road):
                        out_of_road_count += 1
                        route_completion_rates.append(completion)
                        print(f"    Episode {episode + 1}: OUT_OF_ROAD (completion: {completion:.1f}%)")
                    else:
                        # 如果没有明确的终止原因，根据路线完成度判断
                        if completion > 95.0:  # 接近完成，认为是成功
                            successful_episodes += 1
                            route_completion_rates.append(completion)
                            print(f"    Episode {episode + 1}: SUCCESS (completion: {completion:.1f}%)")
                        else:
                            # 其他情况认为是出路（因为没有明确的成功或碰撞）
                            out_of_road_count += 1
                            route_completion_rates.append(completion)
                            print(f"    Episode {episode + 1}: OUT_OF_ROAD (completion: {completion:.1f}%)")

            finally:
                env.close()

        # 计算统计结果
        success_rate = successful_episodes / total_episodes if total_episodes > 0 else 0.0
        collision_rate = collision_count / total_episodes if total_episodes > 0 else 0.0
        out_of_road_rate = out_of_road_count / total_episodes if total_episodes > 0 else 0.0
        avg_reward = total_rewards / total_episodes if total_episodes > 0 else 0.0
        avg_steps = total_steps / total_episodes if total_episodes > 0 else 0.0
        avg_route_completion = np.mean(route_completion_rates) if route_completion_rates else 0.0

        validation_results = {
            'success_rate': success_rate,
            'collision_rate': collision_rate,
            'out_of_road_rate': out_of_road_rate,
            'avg_reward': avg_reward,
            'avg_steps': avg_steps,
            'avg_route_completion': avg_route_completion,
            'total_episodes': total_episodes
        }

        print(f"    📊 Validation Results:")
        print(f"       Success Rate: {success_rate:.1%}")
        print(f"       Collision Rate: {collision_rate:.1%}")
        print(f"       Out of Road Rate: {out_of_road_rate:.1%}")
        print(f"       Avg Route Completion: {avg_route_completion:.1f}%")
        print(f"       Avg Reward: {avg_reward:.2f}")
        print(f"       Avg Steps: {avg_steps:.1f}")

        return validation_results

    def save_model(self, save_path, iteration=None, loss=None, route_completion=None, model_type="best"):
        """保存模型"""
        save_path = Path(save_path)
        save_path.parent.mkdir(parents=True, exist_ok=True)

        save_data = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'config': self.config,
            'iteration': iteration,
            'loss': loss,
            'route_completion': route_completion,
            'model_type': model_type
        }

        torch.save(save_data, save_path)
        if route_completion is not None:
            print(f"  💾 Model saved: {save_path} (Route Completion: {route_completion:.1%})")
        else:
            print(f"  💾 Model saved: {save_path}")

    def run_dagger_parallel(self, num_iterations=10, episodes_per_iteration=50, num_workers=4):
        """运行并行DAGGER训练"""
        print(f"\n🚀 Starting DAGGER Training (Without GNN)")
        print(f"   Iterations: {num_iterations}")
        print(f"   Episodes per iteration: {episodes_per_iteration}")
        print(f"   Workers: {num_workers}")
        
        # 创建保存目录
        save_dir = Path("saved_model")
        save_dir.mkdir(exist_ok=True)

        # 创建时间戳子目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        timestamp_dir = save_dir / f"dagger_without_gnn_{timestamp}"
        timestamp_dir.mkdir(exist_ok=True)
        
        # 初始化TensorBoard
        writer = SummaryWriter(timestamp_dir / "tensorboard")
        
        best_model_path = None
        
        for iteration in range(num_iterations):
            print(f"\n📊 Iteration {iteration + 1}/{num_iterations}")

            # 计算beta值（与原版一致的专家策略混合比例）
            beta = self.calculate_beta(iteration, num_iterations)

            # 收集数据
            iteration_data = self.collect_data_parallel(episodes_per_iteration, beta, num_workers)

            print(f"  📦 Collected {len(iteration_data)} samples this iteration.")
            self.data_buffer.extend(iteration_data)
            print(f"  💾 Buffer size: {len(self.data_buffer)}")

            if len(self.data_buffer) >= self.config['batch_size']:
                print(f"  🧠 Training model...")
                avg_loss = self.train_on_data(list(self.data_buffer), self.config['training_epochs'])
                print(f"    Loss: {avg_loss:.4f}")
                self.iteration_losses.append(avg_loss)

                # 记录到TensorBoard
                writer.add_scalar('Training/Loss', avg_loss, iteration)
                writer.add_scalar('Training/Beta', beta, iteration)

                # 每3回合验证一次
                if (iteration + 1) % 3 == 0:
                    # 下次验证时打开render（从第6回合开始）
                    render_validation = (iteration + 1) >= 6
                    validation_results = self.validate_model(
                        self.config.get('validation_episodes', 10),
                        render=False
                    )
                    self.validation_results.append(validation_results)

                    # 记录验证结果到TensorBoard
                    writer.add_scalar('Validation/Success_Rate', validation_results['success_rate'], iteration)
                    writer.add_scalar('Validation/Route_Completion', validation_results['avg_route_completion'], iteration)
                    writer.add_scalar('Validation/Collision_Rate', validation_results['collision_rate'], iteration)
                    writer.add_scalar('Validation/Avg_Reward', validation_results['avg_reward'], iteration)

                    # 根据路线完成度保存最佳模型
                    current_route_completion = validation_results['avg_route_completion']
                    if current_route_completion > self.best_route_completion:
                        self.best_route_completion = current_route_completion
                        best_model_path = timestamp_dir / "best_model_by_completion.pth"
                        self.save_model(best_model_path, iteration, avg_loss, current_route_completion, "best_by_completion")
                        print(f"    🏆 New best route completion: {current_route_completion:.1f}%")

                # 保存检查点模型
                if (iteration + 1) % 10 == 0 or iteration == num_iterations - 1:
                    self.save_model(timestamp_dir / "checkpoint.pth", iteration, avg_loss, None, "checkpoint")

                # 保存最佳损失模型
                if avg_loss < self.best_loss:
                    self.best_loss = avg_loss
                    best_loss_model_path = timestamp_dir / "best_model_by_loss.pth"
                    self.save_model(best_loss_model_path, iteration, avg_loss, None, "best_by_loss")
            else:
                print("  ⚠️ Not enough data to train, skipping.")
        
        writer.close()
        print(f"\n🎉 DAGGER Training Completed!")
        print(f"   Best loss: {self.best_loss:.4f}")
        print(f"   Best route completion: {self.best_route_completion:.1f}%")
        print(f"   Total validations: {len(self.validation_results)}")
        print(f"   Results saved to: {timestamp_dir}")

        # 返回最佳路线完成度模型路径，如果没有则返回最佳损失模型路径
        if self.best_route_completion > 0:
            return timestamp_dir / "best_model_by_completion.pth"
        else:
            return timestamp_dir / "best_model_by_loss.pth"
