#!/usr/bin/env python3
"""
日志抑制工具
"""

import os
import sys
import logging
from contextlib import contextmanager

def setup_quiet_mode():
    """设置安静模式，抑制MetaDrive日志"""
    
    # 设置环境变量
    os.environ['METADRIVE_LOG_LEVEL'] = 'CRITICAL'
    os.environ['PANDA3D_LOG_LEVEL'] = 'error'
    os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
    
    # 设置Python日志级别
    logging.getLogger('metadrive').setLevel(logging.CRITICAL)
    logging.getLogger('metadrive.engine').setLevel(logging.CRITICAL)
    logging.getLogger('metadrive.envs').setLevel(logging.CRITICAL)
    logging.getLogger('metadrive.component').setLevel(logging.CRITICAL)
    logging.getLogger('panda3d').setLevel(logging.CRITICAL)
    
    # 抑制警告
    import warnings
    warnings.filterwarnings("ignore")
    
    # 重定向stderr（MetaDrive的INFO日志通过stderr输出）
    import sys
    from io import StringIO
    
    class NullWriter:
        def write(self, txt):
            pass
        def flush(self):
            pass
    
    # 保存原始stderr
    original_stderr = sys.stderr
    
    # 创建一个过滤器，只允许我们的输出通过
    class FilteredWriter:
        def __init__(self, original):
            self.original = original
            
        def write(self, txt):
            # 过滤掉MetaDrive的INFO日志
            if '[INFO]' not in txt and 'MetaDrive' not in txt and 'Assets version' not in txt:
                self.original.write(txt)
                
        def flush(self):
            self.original.flush()
    
    # 应用过滤器
    sys.stderr = FilteredWriter(original_stderr)
    
    return original_stderr

@contextmanager
def quiet_metadrive():
    """上下文管理器，临时抑制MetaDrive日志"""
    original_stderr = setup_quiet_mode()
    try:
        yield
    finally:
        sys.stderr = original_stderr

# 在模块导入时就设置安静模式
setup_quiet_mode()
