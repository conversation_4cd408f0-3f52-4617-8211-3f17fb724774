#!/usr/bin/env python3
"""
增强对比实验配置 - 设计更能凸显GNN优势的实验场景
"""

# 1. 高复杂度场景配置 - 多车交互密集
HIGH_COMPLEXITY_CONFIG = {
    'env_config': {
        "out_of_road_done": True,
        "use_render": False,
        "num_scenarios": 100,  # 增加场景多样性
        "traffic_density": 0.3,  # 大幅增加交通密度
        "start_seed": 4,
        "map": "X",  # 使用更复杂的X型交叉路口
        "accident_prob": 0.05,  # 增加意外情况
        "vehicle_config": {
            "lidar": {"num_lasers": 30, "distance": 50},
            "side_detector": {"num_lasers": 30},
            "lane_line_detector": {"num_lasers": 12}
        }
    },
    'description': '高交通密度X型路口 - 多车交互场景'
}

# 2. 动态障碍物场景配置
DYNAMIC_OBSTACLES_CONFIG = {
    'env_config': {
        "out_of_road_done": True,
        "use_render": False,
        "num_scenarios": 50,
        "traffic_density": 0.25,
        "start_seed": 4,
        "map": "C",  # 环形路
        "accident_prob": 0.1,
        "random_traffic": True,  # 随机交通模式
        "vehicle_config": {
            "lidar": {"num_lasers": 30, "distance": 50},
            "side_detector": {"num_lasers": 30},
            "lane_line_detector": {"num_lasers": 12}
        }
    },
    'description': '环形路动态障碍物场景'
}

# 3. 限制传感器信息配置 - 突出图结构重要性
LIMITED_SENSOR_CONFIG = {
    'env_config': {
        "out_of_road_done": True,
        "use_render": False,
        "num_scenarios": 1,
        "traffic_density": 0.2,
        "start_seed": 4,
        "map": "CXO",
        "accident_prob": 0.0,
        "vehicle_config": {
            "lidar": {"num_lasers": 15, "distance": 30},  # 减少激光雷达
            "side_detector": {"num_lasers": 15},  # 减少侧向检测
            "lane_line_detector": {"num_lasers": 6}  # 减少车道线检测
        }
    },
    'model_config': {
        'sequence_dim': 36,  # 对应减少的传感器维度 (15+15+6)
    },
    'description': '限制传感器信息 - 突出图结构价值'
}

# 4. 多车协作场景配置
MULTI_VEHICLE_CONFIG = {
    'env_config': {
        "out_of_road_done": True,
        "use_render": False,
        "num_scenarios": 1,
        "traffic_density": 0.4,  # 极高密度
        "start_seed": 4,
        "map": "CXO",
        "accident_prob": 0.0,
        "vehicle_config": {
            "lidar": {"num_lasers": 30, "distance": 60},  # 增加感知范围
            "side_detector": {"num_lasers": 30},
            "lane_line_detector": {"num_lasers": 12}
        }
    },
    'description': '极高密度多车协作场景'
}

# 5. 长期规划场景配置
LONG_TERM_PLANNING_CONFIG = {
    'env_config': {
        "out_of_road_done": True,
        "use_render": False,
        "num_scenarios": 1,
        "traffic_density": 0.15,
        "start_seed": 4,
        "map": "CXO",
        "accident_prob": 0.0,
        "horizon": 2000,  # 增加episode长度
        "vehicle_config": {
            "lidar": {"num_lasers": 30, "distance": 50},
            "side_detector": {"num_lasers": 30},
            "lane_line_detector": {"num_lasers": 12}
        }
    },
    'training_config': {
        'max_steps_per_episode': 2000,
    },
    'description': '长期规划场景 - 测试持续决策能力'
}

def get_comparison_configs():
    """获取所有对比配置"""
    return {
        'high_complexity': HIGH_COMPLEXITY_CONFIG,
        'dynamic_obstacles': DYNAMIC_OBSTACLES_CONFIG,
        'limited_sensor': LIMITED_SENSOR_CONFIG,
        'multi_vehicle': MULTI_VEHICLE_CONFIG,
        'long_term_planning': LONG_TERM_PLANNING_CONFIG
    }

def create_enhanced_experiment_plan():
    """创建增强对比实验计划"""
    
    plan = {
        'phase1': {
            'name': '基础对比验证',
            'configs': ['high_complexity', 'multi_vehicle'],
            'focus': '验证在高复杂度场景下GNN的优势',
            'expected_difference': '10-20%性能差异'
        },
        
        'phase2': {
            'name': '传感器限制实验',
            'configs': ['limited_sensor'],
            'focus': '在传感器信息受限时突出图结构价值',
            'expected_difference': '20-30%性能差异'
        },
        
        'phase3': {
            'name': '动态场景测试',
            'configs': ['dynamic_obstacles'],
            'focus': '测试动态环境下的适应能力',
            'expected_difference': '15-25%性能差异'
        },
        
        'phase4': {
            'name': '长期规划能力',
            'configs': ['long_term_planning'],
            'focus': '测试长期决策和规划能力',
            'expected_difference': '5-15%性能差异'
        }
    }
    
    return plan

# 6. 特殊评估指标
ENHANCED_METRICS = {
    'multi_vehicle_interaction': {
        'description': '多车交互质量评估',
        'metrics': [
            'average_distance_to_nearby_vehicles',
            'lane_change_success_rate',
            'overtaking_efficiency',
            'collision_avoidance_margin'
        ]
    },
    
    'spatial_awareness': {
        'description': '空间感知能力评估',
        'metrics': [
            'spatial_prediction_accuracy',
            'trajectory_smoothness',
            'path_optimality',
            'obstacle_avoidance_quality'
        ]
    },
    
    'decision_consistency': {
        'description': '决策一致性评估',
        'metrics': [
            'action_variance',
            'decision_stability',
            'long_term_goal_adherence'
        ]
    }
}

def print_experiment_recommendations():
    """打印实验建议"""
    print("🎯 增强GNN优势的实验建议:")
    print("="*50)
    
    configs = get_comparison_configs()
    plan = create_enhanced_experiment_plan()
    
    for phase_name, phase_info in plan.items():
        print(f"\n📊 {phase_info['name']}:")
        print(f"   重点: {phase_info['focus']}")
        print(f"   预期差异: {phase_info['expected_difference']}")
        print(f"   配置: {', '.join(phase_info['configs'])}")
    
    print(f"\n🔧 关键修改建议:")
    print(f"   1. 增加交通密度到0.3-0.4")
    print(f"   2. 使用更复杂的地图(X型路口)")
    print(f"   3. 限制传感器信息突出图结构价值")
    print(f"   4. 增加episode长度测试长期规划")
    print(f"   5. 添加专门的多车交互评估指标")

if __name__ == "__main__":
    print_experiment_recommendations()
