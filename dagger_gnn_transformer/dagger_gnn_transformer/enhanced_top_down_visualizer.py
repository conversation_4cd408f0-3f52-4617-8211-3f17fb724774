#!/usr/bin/env python3
"""
增强版Top-Down俯视图可视化工具
提供高质量、论文级别的车辆状态箭头可视化
"""

import os
import sys
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyArrowPatch, Circle
from matplotlib.collections import LineCollection
import seaborn as sns
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import time
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.dagger_trainer import ExpertPolicy
from models.graph_transformer_model import GraphTransformerModel
from metadrive import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy
from torch_geometric.data import Data, Batch

# 设置matplotlib中文字体和高质量渲染
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['savefig.bbox'] = 'tight'
plt.rcParams['savefig.pad_inches'] = 0.1

class EnhancedTopDownVisualizer:
    """增强版Top-Down可视化器，提供高质量箭头绘制"""
    
    def __init__(self, output_dir="enhanced_visualization", style="paper"):
        """
        初始化增强版可视化器
        
        Args:
            output_dir: 输出目录
            style: 可视化风格 ('paper', 'presentation', 'technical')
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.style = style
        
        # 设置不同风格的颜色方案
        self.color_schemes = {
            'paper': {
                'background': '#FFFFFF',
                'road': '#E8E8E8',
                'lane_line': '#CCCCCC',
                'ego_vehicle': '#2E86AB',
                'npc_vehicle': '#A23B72',
                'trajectory': '#F18F01',
                'arrow_throttle': '#C73E1D',
                'arrow_brake': '#0077BE',
                'arrow_steering': '#7209B7',
                'text': '#000000'
            },
            'presentation': {
                'background': '#F8F9FA',
                'road': '#DEE2E6',
                'lane_line': '#ADB5BD',
                'ego_vehicle': '#007BFF',
                'npc_vehicle': '#DC3545',
                'trajectory': '#FFC107',
                'arrow_throttle': '#28A745',
                'arrow_brake': '#DC3545',
                'arrow_steering': '#6F42C1',
                'text': '#212529'
            },
            'technical': {
                'background': '#1E1E1E',
                'road': '#404040',
                'lane_line': '#606060',
                'ego_vehicle': '#00D4AA',
                'npc_vehicle': '#FF6B6B',
                'trajectory': '#FFE66D',
                'arrow_throttle': '#4ECDC4',
                'arrow_brake': '#FF6B6B',
                'arrow_steering': '#A8E6CF',
                'text': '#FFFFFF'
            }
        }
        
        self.colors = self.color_schemes[style]
        
    def create_high_quality_arrow(self, ax, start_pos, direction, magnitude, arrow_type='throttle', 
                                scale=1.0, alpha=0.8):
        """
        创建高质量的箭头
        
        Args:
            ax: matplotlib轴对象
            start_pos: 起始位置 (x, y)
            direction: 方向角度（弧度）
            magnitude: 大小 (0-1)
            arrow_type: 箭头类型 ('throttle', 'brake', 'steering')
            scale: 缩放因子
            alpha: 透明度
        """
        x, y = start_pos
        
        # 根据类型选择颜色和样式
        if arrow_type == 'throttle':
            color = self.colors['arrow_throttle']
            width = 0.8 * scale
            head_width = 1.5 * scale
        elif arrow_type == 'brake':
            color = self.colors['arrow_brake']
            width = 0.8 * scale
            head_width = 1.5 * scale
        elif arrow_type == 'steering':
            color = self.colors['arrow_steering']
            width = 0.6 * scale
            head_width = 1.2 * scale
        else:
            color = self.colors['text']
            width = 0.5 * scale
            head_width = 1.0 * scale
            
        # 计算箭头长度
        arrow_length = magnitude * 3.0 * scale
        
        # 计算箭头终点
        end_x = x + arrow_length * np.cos(direction)
        end_y = y + arrow_length * np.sin(direction)
        
        # 创建高质量箭头
        arrow = FancyArrowPatch(
            (x, y), (end_x, end_y),
            arrowstyle='-|>',
            mutation_scale=head_width * 10,
            linewidth=width * 2,
            color=color,
            alpha=alpha,
            zorder=10,
            # 添加阴影效果
            path_effects=[
                plt.matplotlib.patheffects.withStroke(linewidth=width*3, foreground='black', alpha=0.3)
            ] if self.style == 'presentation' else None
        )
        
        ax.add_patch(arrow)
        
        return arrow
        
    def create_vehicle_representation(self, ax, pos, heading, vehicle_type='ego', 
                                    throttle=0.0, steering=0.0, scale=1.0):
        """
        创建高质量的车辆表示
        
        Args:
            ax: matplotlib轴对象
            pos: 车辆位置 (x, y)
            heading: 车辆朝向（弧度）
            vehicle_type: 车辆类型 ('ego', 'npc')
            throttle: 油门值 (-1到1)
            steering: 转向值 (-1到1)
            scale: 缩放因子
        """
        x, y = pos
        
        # 车辆尺寸
        length = 4.0 * scale
        width = 2.0 * scale
        
        # 选择颜色
        if vehicle_type == 'ego':
            color = self.colors['ego_vehicle']
            edge_color = 'black'
            linewidth = 2
        else:
            color = self.colors['npc_vehicle']
            edge_color = 'darkred'
            linewidth = 1
            
        # 创建车辆矩形
        vehicle_rect = patches.Rectangle(
            (-length/2, -width/2), length, width,
            angle=np.degrees(heading),
            facecolor=color,
            edgecolor=edge_color,
            linewidth=linewidth,
            alpha=0.8,
            zorder=5
        )
        
        # 应用变换
        t = plt.matplotlib.transforms.Affine2D().rotate_around(x, y, heading) + ax.transData
        vehicle_rect.set_transform(t)
        ax.add_patch(vehicle_rect)
        
        # 为自车添加状态箭头
        if vehicle_type == 'ego':
            # 油门/刹车箭头（前方）
            if abs(throttle) > 0.1:
                arrow_type = 'throttle' if throttle > 0 else 'brake'
                throttle_pos = (
                    x + (length/2 + 1.0) * np.cos(heading) * scale,
                    y + (length/2 + 1.0) * np.sin(heading) * scale
                )
                self.create_high_quality_arrow(
                    ax, throttle_pos, heading, abs(throttle), 
                    arrow_type=arrow_type, scale=scale
                )
                
            # 转向箭头（侧面）
            if abs(steering) > 0.1:
                steering_angle = heading + np.pi/2 * np.sign(steering)
                steering_pos = (
                    x + width/2 * np.cos(steering_angle) * scale,
                    y + width/2 * np.sin(steering_angle) * scale
                )
                self.create_high_quality_arrow(
                    ax, steering_pos, steering_angle, abs(steering),
                    arrow_type='steering', scale=scale
                )
        
        return vehicle_rect
        
    def create_enhanced_visualization(self, env, model_action, expert_action=None, 
                                   trajectory_history=None, save_path=None):
        """
        创建增强版可视化图像
        
        Args:
            env: MetaDrive环境
            model_action: 模型动作 [steering, throttle]
            expert_action: 专家动作（可选）
            trajectory_history: 轨迹历史
            save_path: 保存路径
        """
        # 创建高分辨率图像
        fig, ax = plt.subplots(1, 1, figsize=(16, 10))
        ax.set_facecolor(self.colors['background'])
        
        # 获取车辆信息
        ego = env.agent
        ego_pos = np.array([ego.position[0], ego.position[1]])
        ego_heading = ego.heading_theta
        
        # 设置视图范围（以自车为中心）
        view_range = 50
        ax.set_xlim(ego_pos[0] - view_range, ego_pos[0] + view_range)
        ax.set_ylim(ego_pos[1] - view_range, ego_pos[1] + view_range)
        
        # 绘制道路（简化版）
        road_width = 8.0
        road_rect = patches.Rectangle(
            (ego_pos[0] - view_range, ego_pos[1] - road_width/2),
            2 * view_range, road_width,
            facecolor=self.colors['road'],
            edgecolor=self.colors['lane_line'],
            linewidth=2,
            alpha=0.7,
            zorder=1
        )
        ax.add_patch(road_rect)
        
        # 绘制车道线
        for lane_offset in [-road_width/4, 0, road_width/4]:
            ax.axhline(y=ego_pos[1] + lane_offset, 
                      xmin=(ego_pos[0] - view_range + view_range)/100, 
                      xmax=(ego_pos[0] + view_range + view_range)/100,
                      color=self.colors['lane_line'], 
                      linewidth=1, linestyle='--', alpha=0.8, zorder=2)
        
        # 绘制轨迹历史
        if trajectory_history is not None and len(trajectory_history) > 1:
            traj_array = np.array(trajectory_history)
            ax.plot(traj_array[:, 0], traj_array[:, 1], 
                   color=self.colors['trajectory'], linewidth=3, 
                   alpha=0.7, zorder=3, label='Trajectory')
        
        # 绘制NPC车辆
        for v_id, vehicle in env.engine.get_objects().items():
            if v_id != ego.id and hasattr(vehicle, 'position'):
                distance = np.linalg.norm(ego_pos - np.array([vehicle.position[0], vehicle.position[1]]))
                if distance < view_range:
                    self.create_vehicle_representation(
                        ax, [vehicle.position[0], vehicle.position[1]], 
                        vehicle.heading_theta, 'npc', scale=0.8
                    )
        
        # 绘制自车（带状态箭头）
        steering, throttle = model_action[0], model_action[1]
        self.create_vehicle_representation(
            ax, ego_pos, ego_heading, 'ego', 
            throttle=throttle, steering=steering, scale=1.0
        )
        
        # 添加信息面板
        self._add_info_panel(ax, ego, model_action, expert_action)
        
        # 设置图像属性
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3, color=self.colors['text'])
        ax.set_xlabel('X Position (m)', fontsize=12, color=self.colors['text'])
        ax.set_ylabel('Y Position (m)', fontsize=12, color=self.colors['text'])
        ax.set_title('Enhanced Top-Down Vehicle Visualization', 
                    fontsize=16, fontweight='bold', color=self.colors['text'])
        
        # 设置刻度颜色
        ax.tick_params(colors=self.colors['text'])
        
        # 保存图像
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor=self.colors['background'], 
                       edgecolor='none', format='png')
            print(f"📸 高质量可视化图像已保存: {save_path}")
        
        return fig, ax
        
    def _add_info_panel(self, ax, ego, model_action, expert_action=None):
        """添加信息面板"""
        info_text = []
        info_text.append(f"Speed: {ego.speed_km_h:.1f} km/h")
        info_text.append(f"Steering: {model_action[0]:.3f}")
        info_text.append(f"Throttle: {model_action[1]:.3f}")
        
        if expert_action is not None:
            info_text.append("--- Expert ---")
            info_text.append(f"E_Steering: {expert_action[0]:.3f}")
            info_text.append(f"E_Throttle: {expert_action[1]:.3f}")
        
        # 在图像右上角添加信息
        info_str = '\n'.join(info_text)
        ax.text(0.98, 0.98, info_str, transform=ax.transAxes,
               fontsize=10, verticalalignment='top', horizontalalignment='right',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.8),
               color=self.colors['text'])

class NPCSlowIDMPolicy(IDMPolicy):
    """专门用于NPC车辆的低速IDM策略"""
    NORMAL_SPEED = 10  # km/h
    MAX_SPEED = 10  # km/h
    CREEP_SPEED = 10  # km/h
    ACC_FACTOR = 0.5

    def __init__(self, control_object, random_seed):
        super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
        self.target_speed = self.NORMAL_SPEED

def patch_traffic_manager_for_slow_npc():
    """通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy"""
    import metadrive.manager.traffic_manager as tm
    
    original_add_policy = tm.PGTrafficManager.add_policy
    
    def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
        if policy_class == IDMPolicy:
            policy_class = NPCSlowIDMPolicy
        return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)
    
    tm.PGTrafficManager.add_policy = patched_add_policy

def get_pure_sensor_data(obs: np.ndarray) -> np.ndarray:
    """从原始88维观测中提取纯72维传感器数据 - 与test_model.py完全一致"""
    if not isinstance(obs, np.ndarray):
        obs = np.array(obs).flatten()

    if obs.shape[0] != 88:
        return np.zeros(72, dtype=np.float32)

    side_detector_obs = obs[0:30]
    lane_line_detector_obs = obs[36:48]
    lidar_obs = obs[58:88]

    pure_sensors = np.concatenate([lidar_obs, side_detector_obs, lane_line_detector_obs])
    return pure_sensors.astype(np.float32)


class EnhancedModelTester:
    """增强版模型测试器，集成高质量可视化"""

    def __init__(self, model_path, config, visualizer_style='paper'):
        """
        初始化增强版模型测试器

        Args:
            model_path: 模型路径
            config: 配置字典
            visualizer_style: 可视化风格
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.config = config

        # 应用低速NPC补丁
        patch_traffic_manager_for_slow_npc()

        # 加载模型
        self.model = GraphTransformerModel(config).to(self.device)
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()

        # 初始化专家策略
        self.expert_policy = ExpertPolicy(config['expert_type'])

        # 初始化可视化器
        self.visualizer = EnhancedTopDownVisualizer(
            output_dir="enhanced_visualization",
            style=visualizer_style
        )

        print("✅ 增强版模型测试器初始化完成")
        print(f"📂 模型路径: {model_path}")
        print(f"🖥️ 设备: {self.device}")
        print(f"🎨 可视化风格: {visualizer_style}")

    def _get_model_input(self, obs, env):
        """从环境状态创建模型输入"""
        sensor_features = get_pure_sensor_data(obs)

        ego = env.agent
        ego_state = np.array([
            ego.speed_km_h / 120.0, ego.heading_diff(ego.lane), ego.steering,
            ego.throttle_brake, ego.dist_to_left_side, ego.dist_to_right_side,
            np.linalg.norm(ego.velocity)
        ], dtype=np.float32)

        node_features = [ego_state]
        max_dist = self.config.get("max_npc_distance", 50)
        for v_id, v in env.engine.get_objects().items():
            if v_id != ego.id and hasattr(v, 'position'):
                rel_pos = ego.position - v.position
                distance = np.linalg.norm(rel_pos)
                if distance > max_dist:
                    continue
                rel_vel = ego.velocity - v.velocity
                npc_state = np.array([
                    np.linalg.norm(v.velocity), v.heading_diff(v.lane), rel_pos[0],
                    rel_pos[1], rel_vel[0], rel_vel[1], np.linalg.norm(rel_pos)
                ], dtype=np.float32)
                node_features.append(npc_state)

        node_features = np.array(node_features)
        num_nodes = len(node_features)
        edge_index = []
        for i in range(num_nodes):
            for j in range(i + 1, num_nodes):
                edge_index.extend([[i, j], [j, i]])

        if not edge_index and num_nodes > 0:
            edge_index = [[0, 0]]

        graph_data = Data(
            x=torch.from_numpy(node_features).float(),
            edge_index=torch.from_numpy(np.array(edge_index).T).long()
        )

        # 获取边界距离
        dist_left = env.agent.dist_to_left_side
        dist_right = env.agent.dist_to_right_side
        dist_to_boundaries = np.array([dist_left, dist_right], dtype=np.float32)

        return graph_data, sensor_features, dist_to_boundaries

    def test_with_enhanced_visualization(self, num_episodes=1, save_interval=10):
        """
        运行测试并生成增强版可视化

        Args:
            num_episodes: 测试episode数量
            save_interval: 保存图像的步数间隔
        """
        print(f"\n🧪 开始增强版可视化测试 ({num_episodes} episodes)")

        for episode_id in range(num_episodes):
            print(f"\n📊 Episode {episode_id + 1}/{num_episodes}")

            # 配置环境
            env_config = self.config['env_config'].copy()
            env_config['use_render'] = False  # 我们使用自定义可视化

            env = MetaDriveEnv(env_config)
            self.expert_policy.setup(env)

            # 重置环境
            start_seed = env_config.get('start_seed', 100)
            num_scenarios = env_config.get('num_scenarios', 1)
            seed = start_seed + (episode_id % num_scenarios)
            obs, info = env.reset(seed=seed)

            trajectory_history = []
            step = 0
            max_steps = 1000

            while step < max_steps:
                # 记录轨迹
                if hasattr(env.agent, 'position'):
                    trajectory_history.append([env.agent.position[0], env.agent.position[1]])

                # 获取模型输入
                graph_data, sensor_features, dist_to_boundaries = self._get_model_input(obs, env)

                # 模型预测
                with torch.no_grad():
                    batch = {
                        'graph_batch': Batch.from_data_list([graph_data]).to(self.device),
                        'sequence_features': torch.from_numpy(sensor_features).float().unsqueeze(0).to(self.device),
                        'dist_to_boundaries': torch.from_numpy(dist_to_boundaries).float().unsqueeze(0).to(self.device)
                    }
                    model_action = self.model(batch)['action_pred'].cpu().numpy()[0]

                # 获取专家动作（用于对比）
                expert_action = self.expert_policy.act(obs)

                # 执行动作
                obs, reward, terminated, truncated, info = env.step(model_action)

                # 定期保存可视化图像
                if step % save_interval == 0 or terminated or truncated:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    save_path = self.visualizer.output_dir / f"enhanced_ep{episode_id+1}_step{step}_{timestamp}.png"

                    self.visualizer.create_enhanced_visualization(
                        env, model_action, expert_action,
                        trajectory_history, save_path
                    )

                step += 1

                # 检查终止条件
                if terminated or truncated or info.get('arrive_dest', False):
                    print(f"✅ Episode {episode_id+1} 完成 ({step} steps)")
                    break
                if info.get('out_of_road', False):
                    print(f"❌ Episode {episode_id+1} 出路 ({step} steps)")
                    break
                if info.get('crash', False):
                    print(f"💥 Episode {episode_id+1} 碰撞 ({step} steps)")
                    break

            env.close()

        print(f"\n🎉 增强版可视化测试完成！")
        print(f"📸 图像保存在: {self.visualizer.output_dir}")


def create_enhanced_visualization_demo(
    model_path="/home/<USER>/rl/RL-IL/dagger_gnn_transformer/dagger_model/best_model.pth",
    style='paper',
    num_episodes=1
):
    """
    创建增强版可视化演示

    Args:
        model_path: 模型路径
        style: 可视化风格 ('paper', 'presentation', 'technical')
        num_episodes: 测试episode数量
    """
    config = {
        'hidden_dim': 128, 'transformer_dim': 256, 'num_layers': 2,
        'num_transformer_layers': 4, 'num_heads': 8, 'max_vehicles': 10,
        'dropout': 0.1, 'sequence_dim': 72, 'ego_node_dim': 7, 'npc_node_dim': 7,
        'action_dim': 2, 'max_npc_distance': 30, 'expert_type': 'expert',
        'env_config': {
            "out_of_road_done": True,
            "use_render": False,
            "num_scenarios": 1,
            "traffic_density": 0.1,
            "start_seed": 1,
            "map": "SSSSS",
            "accident_prob": 0.0,
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            }
        }
    }

    print("=== 增强版Top-Down可视化演示 ===")
    print(f"📊 参数设置:")
    print(f"  - 可视化风格: {style}")
    print(f"  - Episode数量: {num_episodes}")
    print(f"  - 模型路径: {model_path}")

    tester = EnhancedModelTester(model_path, config, visualizer_style=style)
    tester.test_with_enhanced_visualization(num_episodes=num_episodes, save_interval=5)

    return True


def main():
    """主函数 - 生成增强版Top-Down可视化"""
    print("🎯 增强版DAGGER模型Top-Down可视化")
    print("=" * 50)

    # 提供三种风格选择
    styles = ['paper', 'presentation', 'technical']

    for style in styles:
        print(f"\n🎨 生成 {style} 风格可视化...")
        create_enhanced_visualization_demo(
            style=style,
            num_episodes=1
        )

    return True


if __name__ == "__main__":
    if main():
        print("\n🎉 增强版可视化生成完成！")
    else:
        print("\n💥 增强版可视化生成失败！")
        sys.exit(1)
