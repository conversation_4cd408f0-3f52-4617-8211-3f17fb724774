#!/usr/bin/env python3
"""
超车场景专用可视化工具
专门展示超车变道场景中的模型vs专家轨迹对比
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyArrowPatch, Circle, Polygon
import matplotlib.patheffects as path_effects
from pathlib import Path
from datetime import datetime
import seaborn as sns

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trajectory_comparison_visualizer import TrajectoryComparisonVisualizer

# 设置高质量绘图
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman', 'DejaVu Serif']
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

class OvertakingScenarioVisualizer(TrajectoryComparisonVisualizer):
    """超车场景专用可视化器"""
    
    def __init__(self, model_path, config, output_dir="overtaking_visualization"):
        """初始化超车场景可视化器"""
        super().__init__(model_path, config, output_dir)
        
        # 超车场景专用颜色方案
        self.colors.update({
            'overtaking_zone': '#FFE6E6',  # 浅红色 - 超车区域
            'lane_change_arrow': '#FF4500',  # 橙红色 - 变道箭头
            'speed_indicator': '#FF6B6B',  # 红色 - 速度指示（改为红色避免绿色）
            'danger_zone': '#FF6B6B',  # 红色 - 危险区域
        })
        
        print("✅ 超车场景可视化器初始化完成")
    
    def create_overtaking_scenario_config(self):
        config = self.config.copy()

        
        return config
    
    def collect_overtaking_trajectories(self, episode_steps=300, fixed_seed=0):
        """
        收集包含超车行为的轨迹数据 - 使用固定seed与test_model.py一致

        Args:
            episode_steps: 运行步数（增加到300以获得更长轨迹）
            fixed_seed: 固定的seed偏移量（与test_model.py的episode_id对应）
        """
        print(f"\n🎯 收集超车场景数据 (固定seed={fixed_seed})")

        # 收集轨迹 - 使用固定seed确保可重现
        print(f"📊 使用固定seed收集轨迹...")
        model_data = self._collect_single_trajectory(episode_steps, fixed_seed, use_model=True)
        expert_data = self._collect_single_trajectory(episode_steps, fixed_seed, use_model=False)

        # 评估超车行为
        overtaking_score = self._evaluate_overtaking_behavior(model_data, expert_data)
        print(f"✅ 超车场景评分: {overtaking_score:.2f}")

        return model_data, expert_data
    
    def _evaluate_overtaking_behavior(self, model_data, expert_data):
        """评估轨迹中的超车行为"""
        score = 0
        
        # 1. 检查横向位移（变道行为）
        model_positions = np.array(model_data['ego_positions'])
        expert_positions = np.array(expert_data['ego_positions'])
        
        if len(model_positions) > 10 and len(expert_positions) > 10:
            # 计算横向变化
            model_lateral_change = np.max(model_positions[:, 1]) - np.min(model_positions[:, 1])
            expert_lateral_change = np.max(expert_positions[:, 1]) - np.min(expert_positions[:, 1])
            
            # 横向变化越大，越可能是变道/超车
            score += (model_lateral_change + expert_lateral_change) * 0.5
        
        # 2. 检查NPC车辆密度
        avg_npc_count = np.mean([len(npc_step) for npc_step in model_data['npc_data']])
        score += avg_npc_count * 2  # NPC越多，超车场景越有趣
        
        # 3. 检查轨迹差异
        min_len = min(len(model_positions), len(expert_positions))
        if min_len > 5:
            trajectory_diff = np.mean(np.linalg.norm(
                model_positions[:min_len] - expert_positions[:min_len], axis=1))
            score += trajectory_diff  # 轨迹差异越大越有趣
        
        # 4. 检查速度变化
        model_speeds = np.array(model_data['ego_speeds'])
        expert_speeds = np.array(expert_data['ego_speeds'])
        
        model_speed_var = np.var(model_speeds) if len(model_speeds) > 1 else 0
        expert_speed_var = np.var(expert_speeds) if len(expert_speeds) > 1 else 0
        
        score += (model_speed_var + expert_speed_var) * 0.1  # 速度变化表示复杂驾驶行为
        
        return score
    
    def create_overtaking_visualization(self, model_data, expert_data, save_path=None, full_trajectory=False):
        """
        创建专门的超车场景可视化

        Args:
            model_data: 模型轨迹数据
            expert_data: 专家轨迹数据
            save_path: 保存路径
            full_trajectory: 是否绘制整段行驶轨迹
        """

        # 获取轨迹数据
        model_positions = np.array(model_data['ego_positions'])
        expert_positions = np.array(expert_data['ego_positions'])

        # 首先计算实际的坐标范围来确定合适的图像尺寸
        if full_trajectory:
            # 绘制整段轨迹
            start_step = 0
            end_step = min(len(model_positions), len(expert_positions)) - 1
            print(f"🔍 绘制整段轨迹: 步骤 {start_step} - {end_step}")

            # 使用全部轨迹
            context_start = 0
            context_end_model = len(model_positions)
            context_end_expert = len(expert_positions)

            # 为全轨迹模式设置overtaking_range
            overtaking_range = (start_step, end_step)
        else:
            # 根据x距离区间截取（可以修改这里的参数）
            overtaking_range = self._find_overtaking_range(model_data, expert_data,
                                                         start_x=120, end_x=150)  # 换道 120 150
            start_step, end_step = overtaking_range

            print(f"🔍 超车区间: 步骤 {start_step} - {end_step}")

            # 扩展视图范围以显示更多上下文
            context_start = max(0, start_step - 20)
            context_end_model = min(len(model_positions), end_step + 20)
            context_end_expert = min(len(expert_positions), end_step + 20)

        model_context = model_positions[context_start:context_end_model]
        expert_context = expert_positions[context_start:context_end_expert]

        print(f"📊 轨迹长度: 模型={len(model_context)}, 专家={len(expert_context)}")

        # 设置视图范围
        if len(model_context) > 0 and len(expert_context) > 0:
            all_positions = np.vstack([model_context, expert_context])
        elif len(model_context) > 0:
            all_positions = model_context
        elif len(expert_context) > 0:
            all_positions = expert_context
        else:
            print("⚠️ 警告: 没有有效的轨迹数据")
            return None, None

        # 基于车道线计算y范围，确保车道线不被截掉
        # 减少x方向的边距，避免右侧留白
        x_min, x_max = all_positions[:, 0].min() - 2, all_positions[:, 0].max() + 2

        # 计算车道线的y范围（考虑车道往下移动2米的偏移）
        y_mean = np.mean(all_positions[:, 1])
        lane_width = 3.5
        lane_offset = -2.0  # 车道往下移动的偏移

        # 最上和最下的车道线位置
        top_lane_line = y_mean + lane_width * 1.5 + lane_offset
        bottom_lane_line = y_mean - lane_width * 1.5 + lane_offset

        # y范围：车道线往外再延1米
        y_min, y_max = bottom_lane_line - 1, top_lane_line + 1

        # 根据实际坐标范围动态计算图像尺寸
        x_range = x_max - x_min
        y_range = y_max - y_min

        # 计算长宽比，确保图像尺寸与实际内容匹配
        aspect_ratio = x_range / y_range

        # 限制极端长宽比，避免图像过于细长或过于扁平
        max_aspect_ratio = 6.0  # 最大长宽比6:1
        min_aspect_ratio = 2.0  # 最小长宽比2:1

        if aspect_ratio > max_aspect_ratio:
            aspect_ratio = max_aspect_ratio
            print(f"⚠️ 长宽比过大，限制为 {max_aspect_ratio}:1")
        elif aspect_ratio < min_aspect_ratio:
            aspect_ratio = min_aspect_ratio
            print(f"⚠️ 长宽比过小，限制为 {min_aspect_ratio}:1")

        # 设置合理的基础尺寸
        base_height = 6  # 基础高度6英寸
        fig_width = base_height * aspect_ratio

        # 确保尺寸在合理范围内
        max_width = 16
        min_width = 8
        max_height = 8
        min_height = 4

        # 首先检查宽度限制
        if fig_width > max_width:
            fig_width = max_width
            base_height = max_width / aspect_ratio
        elif fig_width < min_width:
            fig_width = min_width
            base_height = min_width / aspect_ratio

        # 然后检查高度限制
        if base_height > max_height:
            base_height = max_height
            fig_width = base_height * aspect_ratio
            # 再次检查宽度是否超限
            if fig_width > max_width:
                fig_width = max_width
                base_height = max_width / aspect_ratio
        elif base_height < min_height:
            base_height = min_height
            fig_width = base_height * aspect_ratio
            # 再次检查宽度是否超限
            if fig_width > max_width:
                fig_width = max_width
                base_height = max_width / aspect_ratio

        print(f"📐 动态图像尺寸计算:")
        print(f"  - 坐标范围: x={x_range:.1f}m, y={y_range:.1f}m")
        print(f"  - 长宽比: {aspect_ratio:.2f}")
        print(f"  - 图像尺寸: {fig_width:.1f} x {base_height:.1f} 英寸")

        # 创建动态尺寸的图像
        fig, ax = plt.subplots(1, 1, figsize=(fig_width, base_height))
        ax.set_facecolor(self.colors['background'])

        # 精确设置坐标范围，避免额外留白
        ax.set_xlim(x_min, x_max)
        ax.set_ylim(y_min, y_max)

        # 设置紧凑的边距，去掉右侧留白
        fig.subplots_adjust(left=0.08, right=0.99, top=0.92, bottom=0.12)
        
        # 绘制拟真的马路背景
        self._draw_realistic_road_environment(ax, model_data, expert_data,
                                            [x_min, x_max], [y_min, y_max])
        
        # 不再标记超车区域，保持简洁
        
        # 绘制NPC车辆和轨迹 - 只显示超车区域的NPC
        self._draw_enhanced_npc_visualization(ax, model_data['npc_data'],
                                            context_start, context_end_model,
                                            start_step, end_step)

        # 绘制主要轨迹（带详细标注和偏移避免重叠）
        if len(model_context) > 0:
            model_actions = model_data['actions'][context_start:context_end_model]
            model_speeds = model_data['ego_speeds'][context_start:context_end_model]
            print(f"🔵 绘制模型轨迹: {len(model_context)} 个点")

            # 模型轨迹向右偏移0.4米避免重叠
            model_offset = np.array([0.4, 0])
            model_context_offset = model_context + model_offset
            self._draw_detailed_trajectory(ax, model_context_offset, model_actions, model_speeds,
                                         self.colors['model_trajectory'], 'Model', 'model')

        if len(expert_context) > 0:
            expert_actions = expert_data['actions'][context_start:context_end_expert]
            expert_speeds = expert_data['ego_speeds'][context_start:context_end_expert]
            print(f"🔴 绘制专家轨迹: {len(expert_context)} 个点")

            # 专家轨迹向左偏移0.4米避免重叠
            expert_offset = np.array([-0.4, 0])
            expert_context_offset = expert_context + expert_offset
            self._draw_detailed_trajectory(ax, expert_context_offset, expert_actions, expert_speeds,
                                         self.colors['expert_trajectory'], 'Expert', 'expert')
        
        # 不显示任何分析标签，保持图像简洁
        
        # 设置图像属性
        ax.set_aspect('equal')
        ax.grid(False)  # 取消网格
        ax.set_xlabel('X Position (m)', fontsize=16, fontweight='bold')
        ax.set_ylabel('Y Position (m)', fontsize=16, fontweight='bold')

        # 获取场景信息
        start_x, end_x = 120, 150  # 从代码中获取的x范围
        seed_info = 2  # 从代码中获取的seed
        npc_count = len([npc for npc_step in model_data['npc_data'][start_step:end_step+1] for npc in npc_step])

        # ax.set_title(f'Overtaking Scenario Analysis | Seed: {seed_info} | X Range: {start_x}-{end_x}m\n' +
        #             f'Model vs Expert Trajectory Comparison | NPCs: {len(set([npc["id"] for npc_step in model_data["npc_data"][start_step:end_step+1] for npc in npc_step]))} vehicles',
        #             fontsize=16, fontweight='bold', pad=20)
        
        ax.set_title('Comparative Analysis of Models vs. Experts in Lane Change Trajectories',
                    fontsize=16, fontweight='bold', pad=20)
        
        # 不添加图例，保持画面简洁

        # 保存图像
        if save_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_path = Path(save_path).with_suffix('')

            # 使用最紧凑的保存设置，完全去掉边距
            plt.savefig(f"{base_path}_overtaking_{timestamp}.pdf", format='pdf',
                       bbox_inches='tight', dpi=300, pad_inches=0.01)
            plt.savefig(f"{base_path}_overtaking_{timestamp}.png", format='png',
                       bbox_inches='tight', dpi=600, pad_inches=0.01)
            # plt.savefig(f"{base_path}_overtaking_{timestamp}.svg", format='svg',
            #            bbox_inches='tight', dpi=300, pad_inches=0.05)

            print(f"📸 超车场景可视化已保存:")
            print(f"  - PDF: {base_path}_overtaking_{timestamp}.pdf")
            print(f"  - PNG: {base_path}_overtaking_{timestamp}.png")
            # print(f"  - SVG: {base_path}_overtaking_{timestamp}.svg")
        
        return fig, ax
    
    def _find_overtaking_range(self, model_data, expert_data, start_x=20, end_x=100):
        """根据x距离来截取图像"""
        model_positions = np.array(model_data['ego_positions'])
        expert_positions = np.array(expert_data['ego_positions'])

        # 找到对应x距离的步数索引
        def find_x_indices(positions, start_x, end_x):
            start_idx = 0
            end_idx = len(positions) - 1

            for i, pos in enumerate(positions):
                if pos[0] >= start_x and start_idx == 0:
                    start_idx = i
                if pos[0] >= end_x:
                    end_idx = i
                    break

            return start_idx, end_idx

        model_start, model_end = find_x_indices(model_positions, start_x, end_x)
        expert_start, expert_end = find_x_indices(expert_positions, start_x, end_x)

        # 使用较大的范围确保两条轨迹都包含
        actual_start = min(model_start, expert_start)
        actual_end = max(model_end, expert_end)

        print(f"🔍 x距离区间截取:")
        print(f"  请求x范围: {start_x} -> {end_x}米")
        print(f"  模型步数: {model_start} -> {model_end}")
        print(f"  专家步数: {expert_start} -> {expert_end}")
        print(f"  实际区间: {actual_start} -> {actual_end}")

        return [actual_start, actual_end]

    
    def _draw_realistic_road_environment(self, ax, model_data, expert_data, x_range, y_range):
        """绘制拟真的马路背景"""
        # 从轨迹数据中获取车道信息
        all_positions = np.vstack([
            model_data['ego_positions'],
            expert_data['ego_positions']
        ])

        # 计算道路范围
        x_min, x_max = x_range
        y_min, y_max = y_range

        # 扩展道路范围以显示更真实的环境
        road_width = max(20, y_max - y_min + 8)  # 至少20米宽的道路
        road_center_y = (y_max + y_min) / 2
        road_y_min = road_center_y - road_width/2
        road_y_max = road_center_y + road_width/2

        # 绘制道路背景 - 使用更浅的颜色，减少额外边距
        road = patches.Rectangle(
            (x_min - 1, road_y_min),
            x_max - x_min + 2, road_width,
            facecolor='#6B6B6B',  # 浅灰色路面
            edgecolor='none',
            alpha=0.8,
            zorder=1
        )
        ax.add_patch(road)

        # 绘制路肩
        shoulder_width = 2
        # 左路肩
        left_shoulder = patches.Rectangle(
            (x_min - 1, road_y_min - shoulder_width),
            x_max - x_min + 2, shoulder_width,
            facecolor='#8A8A8A',  # 更浅的路肩色
            edgecolor='none',
            alpha=0.7,
            zorder=1
        )
        ax.add_patch(left_shoulder)

        # 右路肩
        right_shoulder = patches.Rectangle(
            (x_min - 1, road_y_max),
            x_max - x_min + 2, shoulder_width,
            facecolor='#8A8A8A',  # 更浅的路肩色
            edgecolor='none',
            alpha=0.7,
            zorder=1
        )
        ax.add_patch(right_shoulder)

        # 绘制拟真的车道线
        # 分析y坐标分布来推断车道结构
        y_positions = all_positions[:, 1]
        y_mean = np.mean(y_positions)
        y_std = np.std(y_positions)

        # 标准车道宽度为3.5米
        lane_width = 3.5

        # 创建3车道系统（左、中、右），整体往下移动2米
        lane_offset = -1  # 往下移动2米
        lane_centers = [
            y_mean - lane_width + lane_offset,  # 左车道
            y_mean + lane_offset,               # 中车道
            y_mean + lane_width + lane_offset   # 右车道
        ]

        # 绘制车道分隔线
        lane_lines = [
            y_mean - lane_width * 1.5 + lane_offset,  # 最左边界
            y_mean - lane_width * 0.5 + lane_offset,  # 左-中分隔线
            y_mean + lane_width * 0.5 + lane_offset,  # 中-右分隔线
            y_mean + lane_width * 1.5 + lane_offset   # 最右边界
        ]

        for i, y_pos in enumerate(lane_lines):
            if i == 0 or i == len(lane_lines) - 1:
                # 道路边界线 - 实线，减少额外边距
                self._draw_road_line(ax, x_min - 1, x_max + 1, y_pos,
                                   'solid', 'white', 3, 1.0)
            else:
                # 车道分隔线 - 虚线，减少额外边距
                self._draw_road_line(ax, x_min - 1, x_max + 1, y_pos,
                                   'dashed', 'white', 2, 0.9)

        # 不绘制中央分隔带，保持简洁

    def _draw_road_line(self, ax, x_start, x_end, y_pos, line_style, color, line_width, alpha):
        """绘制道路线条"""
        if line_style == 'dashed':
            # 绘制虚线
            dash_length = 6
            gap_length = 4
            x_positions = np.arange(x_start, x_end, dash_length + gap_length)
            for x_pos in x_positions:
                line = patches.Rectangle(
                    (x_pos, y_pos - line_width/20),
                    dash_length, line_width/10,
                    facecolor=color,
                    edgecolor='none',
                    alpha=alpha,
                    zorder=3
                )
                ax.add_patch(line)
        else:
            # 绘制实线
            ax.axhline(y=y_pos, xmin=0, xmax=1,
                      color=color, linewidth=line_width,
                      alpha=alpha, zorder=3)

    def _mark_overtaking_zone(self, ax, model_positions, expert_positions):
        """标记超车区域"""
        if len(model_positions) == 0 or len(expert_positions) == 0:
            return

        # 计算超车区域边界
        all_positions = np.vstack([model_positions, expert_positions])
        x_min, x_max = all_positions[:, 0].min() - 2, all_positions[:, 0].max() + 2
        y_min, y_max = all_positions[:, 1].min() - 3, all_positions[:, 1].max() + 3

        # 绘制超车区域背景 - 使用浅色
        overtaking_zone = patches.Rectangle(
            (x_min, y_min), x_max - x_min, y_max - y_min,
            facecolor='lightsteelblue',  # 浅蓝色
            alpha=0.2,  # 更淡的透明度
            zorder=1.5,
            linewidth=2,
            edgecolor=self.colors['lane_change_arrow'],
            linestyle='--'
        )
        ax.add_patch(overtaking_zone)

        # 添加超车区域标签
        ax.text(x_min + (x_max - x_min) * 0.1, y_max - 1,
               'OVERTAKING\nZONE', fontsize=12, fontweight='bold',
               color=self.colors['lane_change_arrow'],
               bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

    def _draw_enhanced_npc_visualization(self, ax, npc_data, start_step, end_step, overtaking_start, overtaking_end):
        """绘制增强的NPC可视化 - 只显示超车区域的NPC"""
        print(f"🚗 绘制超车区域NPC车辆: 步骤 {overtaking_start} - {overtaking_end}")

        # 收集超车区域内的NPC轨迹
        npc_trajectories = {}

        # 只收集超车区域内的NPC数据
        for step in range(overtaking_start, min(overtaking_end + 1, len(npc_data))):
            if step < len(npc_data):
                for npc in npc_data[step]:
                    npc_id = npc['id']
                    if npc_id not in npc_trajectories:
                        npc_trajectories[npc_id] = []
                    npc_trajectories[npc_id].append({
                        'step': step,
                        'position': npc['position'],
                        'heading': npc['heading'],
                        'speed': npc['speed']
                    })

        print(f"📊 超车区域发现 {len(npc_trajectories)} 个NPC车辆")

        # 绘制NPC轨迹和车辆 - 只显示超车区域的NPC
        npc_count = 0
        for npc_id, trajectory in npc_trajectories.items():
            if len(trajectory) < 5:  # 降低最小轨迹要求
                continue

            positions = np.array([point['position'] for point in trajectory])

            print(f"  超车区域NPC {npc_id}: {len(trajectory)} 个轨迹点")

            # 绘制NPC轨迹线 - 淡色线条，避免重叠时看不清
            npc_colors = ['lightcoral', 'lightblue', 'lightgreen', 'plum', 'wheat', 'lightgray']
            npc_color = npc_colors[npc_count % len(npc_colors)]
            ax.plot(positions[:, 0], positions[:, 1],
                   color=npc_color, linewidth=2, alpha=0.6, linestyle='-',
                   zorder=6, label='NPC Vehicle' if npc_count == 0 else "")

            # 绘制起点和终点NPC车辆
            start_pos = trajectory[0]['position']
            start_heading = trajectory[0]['heading']
            end_pos = trajectory[-1]['position']
            end_heading = trajectory[-1]['heading']

            # 绘制起点车辆（半透明）
            self._draw_vehicle(ax, start_pos, start_heading, 'npc',
                             scale=0.8, alpha=0.5)

            # 绘制终点车辆（不透明）
            self._draw_vehicle(ax, end_pos, end_heading, 'npc',
                             scale=0.9, alpha=0.9)

            # 在NPC车辆内部添加编号
            npc_count += 1
            ax.text(end_pos[0], end_pos[1], f'NPC{npc_count}',
                   fontsize=9, ha='center', va='center',
                   color='white', fontweight='bold',
                   zorder=12)



    def _draw_detailed_trajectory(self, ax, positions, actions, speeds, color, label, traj_type):
        """绘制详细的轨迹信息"""
        if len(positions) < 2:
            return

        # 根据轨迹类型选择不同的线型
        if traj_type == 'model':
            linestyle = '-'  # 实线
            linewidth = 5
            alpha = 0.9
        else:  # expert
            linestyle = '--'  # 虚线
            linewidth = 4
            alpha = 0.8

        # 绘制主轨迹线 - 使用更深的颜色
        darker_color = color
        if traj_type == 'model':
            darker_color = '#0066CC'  # 更深的蓝色
        elif traj_type == 'expert':
            darker_color = '#CC0000'  # 更深的红色

        ax.plot(positions[:, 0], positions[:, 1],
               color=darker_color, linewidth=linewidth, alpha=alpha, linestyle=linestyle,
               zorder=5, label=f'{label} Trajectory')

        # 添加速度颜色编码的轨迹段
        for i in range(len(positions) - 1):
            speed = speeds[i] if i < len(speeds) else 0
            # 根据速度调整颜色深度
            speed_alpha = min(1.0, max(0.3, speed / 60))

            segment_color = color if speed > 20 else self.colors['danger_zone']

            ax.plot([positions[i, 0], positions[i+1, 0]],
                   [positions[i, 1], positions[i+1, 1]],
                   color=segment_color, linewidth=3, alpha=speed_alpha, zorder=4)

        # 不添加动作箭头，保持图像简洁
        # self._add_action_arrows(ax, positions, actions, color, traj_type)

        # 不绘制起点和终点标记，只绘制车辆

        # 在终点绘制车辆，并在车内添加标签
        if len(positions) > 1:
            last_pos = positions[-1]
            direction = positions[-1] - positions[-2]
            heading = np.arctan2(direction[1], direction[0])

            self._draw_vehicle(ax, last_pos, heading, 'ego', color=color, scale=1.2)

            # 在车内添加标签 - 使用更小的字体
            vehicle_label = 'Model' if traj_type == 'model' else 'Expert'
            ax.text(last_pos[0], last_pos[1], vehicle_label,
                   fontsize=7, ha='center', va='center',
                   color='white', fontweight='bold',
                   zorder=12)

    def _add_action_arrows(self, ax, positions, actions, color, traj_type):
        """添加动作箭头 - 修复方向问题"""
        if len(positions) < 2 or len(actions) == 0:
            return

        # 选择几个关键点添加动作箭头，减少数量避免混乱
        arrow_indices = np.linspace(0, len(positions)-2, min(4, len(positions)-1), dtype=int)

        for i in arrow_indices:
            if i < len(actions) and i < len(positions) - 1:
                pos = positions[i]
                action = actions[i]
                steering, throttle = action[0], action[1]

                # 计算真实的车辆朝向（基于轨迹方向）
                direction = positions[i+1] - positions[i]
                if np.linalg.norm(direction) > 0.1:  # 确保有足够的移动
                    heading = np.arctan2(direction[1], direction[0])

                    # 只显示显著的油门/刹车动作，简化可视化
                    if abs(throttle) > 0.15:
                        throttle_color = ('green' if throttle > 0 else 'red')
                        arrow_length = abs(throttle) * 3

                        # 箭头沿着轨迹方向
                        start_pos = pos
                        end_pos = pos + np.array([arrow_length * np.cos(heading),
                                                arrow_length * np.sin(heading)])

                        arrow = FancyArrowPatch(
                            start_pos, end_pos,
                            arrowstyle='-|>',
                            mutation_scale=15,
                            linewidth=3,
                            color=throttle_color,
                            alpha=0.8,
                            zorder=8
                        )
                        ax.add_patch(arrow)

                        # 不显示动作标签，保持简洁

    def _draw_vehicle(self, ax, position, heading, vehicle_type='ego', scale=1.0,
                     color=None, alpha=0.9):
        """绘制车辆（增强版）"""
        x, y = position

        # 车辆尺寸
        length = 4.5 * scale
        width = 1.8 * scale

        # 选择颜色和线型
        if color is None:
            if vehicle_type == 'ego':
                color = self.colors['ego_vehicle']
                edge_color = 'black'
                linewidth = 2
                linestyle = '-'  # 实线
            else:
                color = 'red'  # 使用红色让NPC更明显
                edge_color = 'darkred'
                linewidth = 3  # 更粗的边框
                linestyle = '--'  # 虚线
        else:
            edge_color = 'black'
            linewidth = 2
            linestyle = '-' if vehicle_type == 'ego' else '--'

        # 创建车辆矩形
        vehicle = patches.Rectangle(
            (-length/2, -width/2), length, width,
            facecolor=color,
            edgecolor=edge_color,
            linewidth=linewidth,
            linestyle=linestyle,
            alpha=alpha,
            zorder=9  # 提高层级，确保在轨迹线之上
        )

        # 应用旋转和平移变换
        t = plt.matplotlib.transforms.Affine2D().rotate(heading).translate(x, y) + ax.transData
        vehicle.set_transform(t)
        ax.add_patch(vehicle)

        # 不添加方向指示，保持车辆简洁



    def _add_overtaking_analysis(self, ax, model_data, expert_data, overtaking_range):
        """添加超车行为分析"""
        start_step, end_step = overtaking_range

        # 计算关键指标
        model_positions = np.array(model_data['ego_positions'][start_step:end_step+1])
        expert_positions = np.array(expert_data['ego_positions'][start_step:end_step+1])

        if len(model_positions) == 0 or len(expert_positions) == 0:
            return

        # 找到最大横向差异点
        min_len = min(len(model_positions), len(expert_positions))
        lateral_diffs = np.abs(model_positions[:min_len, 1] - expert_positions[:min_len, 1])
        max_lateral_idx = np.argmax(lateral_diffs)

        if lateral_diffs[max_lateral_idx] > 0.5:  # 显著的横向差异
            model_point = model_positions[max_lateral_idx]
            expert_point = expert_positions[max_lateral_idx]

            # 绘制差异连线
            ax.plot([model_point[0], expert_point[0]],
                   [model_point[1], expert_point[1]],
                   'k--', linewidth=3, alpha=0.8, zorder=6)

            # 添加差异标注
            mid_point = (model_point + expert_point) / 2
            ax.annotate(f'Max Lateral Difference\n{lateral_diffs[max_lateral_idx]:.1f}m',
                       xy=mid_point, xytext=(mid_point[0] + 8, mid_point[1] + 3),
                       fontsize=11, ha='center', fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.9),
                       arrowprops=dict(arrowstyle='->', color='black', lw=2),
                       zorder=10)

        # 标注变道方向
        if len(model_positions) > 1:
            start_y = model_positions[0, 1]
            end_y = model_positions[-1, 1]
            lateral_change = end_y - start_y

            if abs(lateral_change) > 1.0:
                direction = "LEFT" if lateral_change > 0 else "RIGHT"
                ax.text(model_positions[-1, 0] + 5, model_positions[-1, 1],
                       f'Model Lane Change\n{direction} ({abs(lateral_change):.1f}m)',
                       fontsize=10, fontweight='bold',
                       color=self.colors['model_trajectory'],
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

    def _add_overtaking_legend(self, ax):
        """添加超车场景专用图例"""
        # 简化图例，只保留最重要的元素
        legend_elements = [
            plt.Line2D([0], [0], color=self.colors['model_trajectory'], lw=4, label='Model'),
            plt.Line2D([0], [0], color=self.colors['expert_trajectory'], lw=4, label='Expert'),
            plt.Line2D([0], [0], color=self.colors['npc_trajectory'], lw=2, linestyle='-.', label='NPC'),
            patches.Rectangle((0, 0), 1, 1, facecolor=self.colors['overtaking_zone'],
                            alpha=0.3, label='Overtaking Zone'),
            plt.Line2D([0], [0], color=self.colors['arrow_steering'], lw=2,
                      marker='>', markersize=6, label='Actions')
        ]

        # 将图例放在图外，避免遮挡
        ax.legend(handles=legend_elements, loc='center left',
                 fontsize=10, framealpha=0.95, fancybox=True, shadow=True,
                 bbox_to_anchor=(1.02, 0.5))

    def _add_overtaking_statistics(self, ax, model_data, expert_data, overtaking_range):
        """添加超车统计信息"""
        start_step, end_step = overtaking_range

        # 计算详细统计
        model_positions = np.array(model_data['ego_positions'][start_step:end_step+1])
        expert_positions = np.array(expert_data['ego_positions'][start_step:end_step+1])
        model_speeds = model_data['ego_speeds'][start_step:end_step+1]
        expert_speeds = expert_data['ego_speeds'][start_step:end_step+1]
        model_actions = model_data['actions'][start_step:end_step+1]
        expert_actions = expert_data['actions'][start_step:end_step+1]

        # 轨迹统计
        model_distance = np.sum(np.linalg.norm(np.diff(model_positions, axis=0), axis=1)) if len(model_positions) > 1 else 0
        expert_distance = np.sum(np.linalg.norm(np.diff(expert_positions, axis=0), axis=1)) if len(expert_positions) > 1 else 0

        # 横向变化
        model_lateral_change = (model_positions[-1, 1] - model_positions[0, 1]) if len(model_positions) > 0 else 0
        expert_lateral_change = (expert_positions[-1, 1] - expert_positions[0, 1]) if len(expert_positions) > 0 else 0

        # 动作统计
        model_steering_var = np.var([a[0] for a in model_actions]) if model_actions else 0
        expert_steering_var = np.var([a[0] for a in expert_actions]) if expert_actions else 0

        # NPC交互
        avg_npc_count = np.mean([len(npc_step) for npc_step in model_data['npc_data'][start_step:end_step+1]])

        # 创建简化的统计信息文本
        stats_text = f"""Analysis (Steps {start_step}-{end_step}):
Distance: M{model_distance:.0f}m E{expert_distance:.0f}m
Lateral: M{model_lateral_change:+.1f}m E{expert_lateral_change:+.1f}m
Speed: M{np.mean(model_speeds):.0f} E{np.mean(expert_speeds):.0f} km/h
Lane Change: M{'Y' if abs(model_lateral_change) > 1.0 else 'N'} E{'Y' if abs(expert_lateral_change) > 1.0 else 'N'}
NPCs: {avg_npc_count:.1f}"""

        # 将统计信息放在图外，避免遮挡
        ax.text(1.02, 0.02, stats_text, transform=ax.transAxes,
               fontsize=9, verticalalignment='bottom', horizontalalignment='left',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.95,
                        edgecolor='gray', linewidth=1),
               color=self.colors['text'])


def create_overtaking_scenario_demo(
    model_path="/home/<USER>/rl/RL-IL/dagger_gnn_transformer/dagger_model/best_model.pth",
    # model_path="/home/<USER>/rl/RL-IL/dagger_model/20250713_003136/checkpoint.pth",
    output_dir="overtaking_visualization",
    fixed_seed=0,
    full_trajectory=False
):
    """
    创建超车场景演示

    Args:
        model_path: 模型路径
        output_dir: 输出目录
        fixed_seed: 固定seed（与test_model.py的episode_id对应）
        full_trajectory: 是否绘制整段行驶轨迹（True）还是只绘制超车区域（False）
    """
    
    config = {
        'hidden_dim': 128, 'transformer_dim': 256, 'num_layers': 2,
        'num_transformer_layers': 4, 'num_heads': 8, 'max_vehicles': 10,
        'dropout': 0.1, 'sequence_dim': 72, 'ego_node_dim': 7, 'npc_node_dim': 7,
        'action_dim': 2, 'max_npc_distance': 30, 'expert_type': 'idm',
        'env_config': {
            "out_of_road_done": False,
            "use_render": True,
            "num_scenarios": 10,
            "traffic_density": 0.1,  # 高交通密度
            "start_seed": 55, # 换道 2  # 超车 16 (30-150)
            "map": "SSSSS",  # 长直道
            "accident_prob": 0.0,
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            }
        }
    }

    print("=== 超车场景可视化演示 ===")
    print(f"📊 参数设置:")
    print(f"  - 模型路径: {model_path}")
    print(f"  - 输出目录: {output_dir}")
    print(f"  - 固定seed: {fixed_seed}")
    print(f"  - 全轨迹模式: {full_trajectory}")
    print(f"  - 交通密度: {config['env_config']['traffic_density']}")

    visualizer = OvertakingScenarioVisualizer(model_path, config, output_dir)

    # 收集超车场景数据 - 使用固定seed确保可重现
    model_data, expert_data = visualizer.collect_overtaking_trajectories(
        episode_steps=1500, fixed_seed=fixed_seed
    )

    # 创建超车场景可视化
    save_path = visualizer.output_dir / "overtaking_scenario"
    fig, ax = visualizer.create_overtaking_visualization(
        model_data, expert_data, save_path=save_path, full_trajectory=full_trajectory
    )

    print(f"\n🎉 超车场景可视化完成！")
    print(f"📁 文件保存在: {output_dir}")

    return model_data, expert_data


def main():
    """主函数"""
    print("🎯 超车场景专用可视化工具")
    print("=" * 50)

    # 生成超车场景可视化 - 使用固定seed确保与test_model.py一致
    # 可以修改full_trajectory=True来绘制整段轨迹
    create_overtaking_scenario_demo(
        fixed_seed=0,  # 使用固定seed，对应test_model.py的episode_id=0
        full_trajectory=True  # False=只显示超车区域 True=显示整段轨迹
    )

    return True


if __name__ == "__main__":
    if main():
        print("\n🎉 超车场景可视化生成成功！")
    else:
        print("\n💥 超车场景可视化生成失败！")
        sys.exit(1)
