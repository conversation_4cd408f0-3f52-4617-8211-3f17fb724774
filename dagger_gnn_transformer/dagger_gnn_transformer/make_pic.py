#!/usr/bin/env python3
"""
测试DAGGER训练的模型并生成可视化图片 (Refactored)
"""

import os
import sys
import torch
import numpy as np
import cv2
import time
from pathlib import Path
from torch_geometric.data import Data, Batch
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.dagger_trainer import ExpertPolicy
from models.graph_transformer_model import GraphTransformerModel
from metadrive import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy
from metadrive.component.sensors.rgb_camera import RGBCamera

# 创建专门用于NPC车辆的低速IDM策略
class NPCSlowIDMPolicy(IDMPolicy):
    """
    专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
    """
    NORMAL_SPEED = 10  # km/h
    MAX_SPEED = 10  # km/h
    CREEP_SPEED = 10  # km/h
    ACC_FACTOR = 0.5  # 降低加速度因子

    def __init__(self, control_object, random_seed):
        super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
        self.target_speed = self.NORMAL_SPEED

def patch_traffic_manager_for_slow_npc():
    """
    通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy
    这样只有NPC车辆会使用低速，自车保持正常速度
    """
    import metadrive.manager.traffic_manager as tm

    # 保存原始的add_policy方法
    original_add_policy = tm.PGTrafficManager.add_policy

    def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
        # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
        if policy_class == IDMPolicy:
            policy_class = NPCSlowIDMPolicy
        return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

    # 应用patch
    tm.PGTrafficManager.add_policy = patched_add_policy

def get_pure_sensor_data(obs: np.ndarray) -> np.ndarray:
    """从观测中提取纯传感器数据."""
    if not isinstance(obs, np.ndarray):
        obs = np.array(obs).flatten()

    # 处理不同维度的观测
    if obs.shape[0] == 88:
        # 原始88维观测格式
        side_detector_obs = obs[0:30]
        lane_line_detector_obs = obs[36:48]
        lidar_obs = obs[58:88]
        pure_sensors = np.concatenate([lidar_obs, side_detector_obs, lane_line_detector_obs])
    elif obs.shape[0] == 58:
        # 图像观测模式下的58维状态格式
        # 根据分析，58维状态的前42维包含了side_detector和lane_line_detector数据
        # 格式：[side_detector(30维), lane_line_detector(12维), 其他状态(16维)]

        # 提取传感器数据
        side_detector_obs = obs[0:30]  # 与88维格式完全一致
        lane_line_detector_obs = obs[30:42]  # 与88维格式完全一致

        # 对于lidar数据，我们没有直接对应，但可以用剩余的16维数据来近似
        # 或者使用一个合理的默认值（比如最大距离）
        remaining_data = obs[42:58]  # 16维剩余数据

        # 构造30维lidar数据
        lidar_obs = np.ones(30, dtype=np.float32)  # 默认最大距离

        # 用剩余数据的一些信息来调整lidar数据
        if len(remaining_data) >= 16:
            # 将16维数据扩展到30维，重复使用一些值
            for i in range(30):
                lidar_obs[i] = remaining_data[i % 16]

        pure_sensors = np.concatenate([lidar_obs, side_detector_obs, lane_line_detector_obs])
    else:
        # 其他维度，返回零向量
        print(f"WARN: 未知的观测维度: {obs.shape[0]}, 使用零向量")
        pure_sensors = np.zeros(72, dtype=np.float32)

    return pure_sensors.astype(np.float32)

class ImageCollector:
    """图像收集器，用于收集和组合渲染图片"""

    def __init__(self, save_interval=1.0, max_images=12, output_dir="visualization_output"):
        """
        初始化图像收集器

        Args:
            save_interval: 保存图片的时间间隔（秒）
            max_images: 最大保存图片数量
            output_dir: 输出目录
        """
        self.save_interval = save_interval
        self.max_images = max_images
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        self.collected_images = []
        self.last_save_time = 0
        self.episode_start_time = 0
        self.last_image = None  # 保存最后一张图片

    def start_episode(self):
        """开始新的episode"""
        self.collected_images = []
        self.last_save_time = 0
        self.episode_start_time = time.time()
        self.last_image = None

    def finalize_episode(self):
        """结束episode，确保最后一帧被包含"""
        if self.last_image is not None and len(self.collected_images) < self.max_images:
            # 检查最后一张图片是否已经在收集列表中
            last_img_data, last_step_info = self.last_image

            # 如果收集列表为空或最后一张图片不同，则添加最后一帧
            if (len(self.collected_images) == 0 or
                not np.array_equal(self.collected_images[-1], last_img_data)):

                img_with_info = self._add_info_to_image(last_img_data, last_step_info)
                self.collected_images.append(img_with_info)
                print(f"📸 已添加最后一帧 (总共{len(self.collected_images)}张图片)")

    def should_save_image(self, current_time):
        """判断是否应该保存当前图片"""
        if len(self.collected_images) >= self.max_images:
            return False
        return (current_time - self.episode_start_time - self.last_save_time) >= self.save_interval

    def add_image(self, image_data, step_info=None, force_save=False):
        """添加图片到收集器"""
        current_time = time.time()

        # 转换图像格式
        if isinstance(image_data, np.ndarray):
            if image_data.dtype != np.uint8:
                # 如果是[0,1]范围，转换为[0,255]
                if image_data.max() <= 1.0:
                    image_data = (image_data * 255).astype(np.uint8)
                else:
                    image_data = image_data.astype(np.uint8)

            # MetaDrive的_get_window_image()返回的是RGB格式，不需要转换
            if len(image_data.shape) == 3 and image_data.shape[2] == 3:
                # 保持RGB格式，不做颜色通道转换
                pass

            # 始终保存最后一张图片
            self.last_image = (image_data.copy(), step_info)

        # 根据时间间隔或强制保存决定是否添加到收集列表
        if force_save or self.should_save_image(current_time):
            if len(self.collected_images) < self.max_images:
                # 添加时间戳和步数信息
                img_with_info = self._add_info_to_image(image_data, step_info)
                self.collected_images.append(img_with_info)
                self.last_save_time = current_time - self.episode_start_time

    def _add_info_to_image(self, image, step_info):
        """在图片上添加信息文本 - 使用OpenCV获得更好的文字质量"""
        # 使用OpenCV渲染文字，质量更好，无颗粒感
        img_cv = image.copy()

        # OpenCV字体配置
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1.5  # 字体缩放
        thickness = 4     # 字体粗细
        color = (255, 255, 255)  # 白色

        # 添加黑色描边效果，提高可读性
        outline_color = (0, 0, 0)  # 黑色描边
        outline_thickness = 4

        # 准备文字内容
        texts = []
        texts.append(f"Time: {self.last_save_time:.1f}s")

        if step_info:
            texts.append(f"Step: {step_info.get('step', 0)}")
            if 'speed' in step_info:
                texts.append(f"Speed: {step_info['speed']:.1f} km/h")

        # 渲染文字
        y_offset = 45
        for i, text in enumerate(texts):
            y_pos = y_offset + i * 45

            # # 先画描边（黑色）
            # cv2.putText(img_cv, text, (15, y_pos), font, font_scale,
            #            outline_color, outline_thickness, cv2.LINE_AA)

            # 再画主文字（白色）
            cv2.putText(img_cv, text, (15, y_pos), font, font_scale,
                       color, thickness, cv2.LINE_AA)

        return img_cv

    def create_combined_image(self, episode_id, stats=None):
        """创建适合论文的组合图片"""
        if not self.collected_images:
            print("⚠️ 没有收集到图片")
            return None

        num_images = len(self.collected_images)
        if num_images == 0:
            return None

        # 优化布局：根据图片数量选择最佳布局
        if num_images <= 4:
            cols, rows = 2, 2
        elif num_images <= 6:
            cols, rows = 3, 2
        elif num_images <= 9:
            cols, rows = 3, 3
        elif num_images <= 12:
            cols, rows = 4, 3
        elif num_images <= 16:
            cols, rows = 4, 4
        elif num_images <= 20:
            cols, rows = 5, 4
        elif num_images <= 25:
            cols, rows = 5, 5
        else:
            cols, rows = 6, 5  # 最大30张图片

        # 使用所有收集到的图片，但确保最后一帧一定包含
        display_images = self.collected_images

        # 获取单张图片尺寸并调整
        original_height, original_width = display_images[0].shape[:2]

        # 缩放图片以适合论文布局 - 提高分辨率
        if cols <= 3:
            target_width = 400  # 3列或更少时，每张图更大
        elif cols <= 4:
            target_width = 300  # 4列时，中等尺寸
        else:
            target_width = 240  # 5列或更多时，稍小但仍然清晰

        target_height = int(original_height * target_width / original_width)

        # 图片间距和边距 - 根据图片尺寸和字体大小调整
        margin = 30
        spacing = 15
        title_height = 80  # 减小标题区域高度，让标题和图片距离更近

        # 计算总尺寸
        total_width = cols * target_width + (cols - 1) * spacing + 2 * margin
        total_height = rows * target_height + (rows - 1) * spacing + 2 * margin + title_height

        # 创建白色背景
        combined_image = np.ones((total_height, total_width, 3), dtype=np.uint8) * 255

        # 放置图片
        border_width = 2  # 边框宽度
        border_color = (128, 128, 128)  # 灰色边框

        for i, img in enumerate(display_images):
            if i >= cols * rows:
                break

            row = i // cols
            col = i % cols

            # 缩放图片
            img_resized = cv2.resize(img, (target_width, target_height))

            # 计算位置
            x_start = margin + col * (target_width + spacing)
            y_start = title_height + margin + row * (target_height + spacing)
            x_end = x_start + target_width
            y_end = y_start + target_height

            # 放置图片
            combined_image[y_start:y_end, x_start:x_end] = img_resized

            # 添加边框
            # 上边框
            combined_image[y_start:y_start+border_width, x_start:x_end] = border_color
            # 下边框
            combined_image[y_end-border_width:y_end, x_start:x_end] = border_color
            # 左边框
            combined_image[y_start:y_end, x_start:x_start+border_width] = border_color
            # 右边框
            combined_image[y_start:y_end, x_end-border_width:x_end] = border_color

        # 转换为PIL图像以添加标题
        combined_pil = Image.fromarray(combined_image)
        draw = ImageDraw.Draw(combined_pil)

        # 加载高质量字体 - 避免颗粒感
        title_font = None
        subtitle_font = None

        # 尝试不同的高质量字体
        font_configs = [
            ("/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf", 32, 20),  # Liberation字体
            ("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 30, 18),          # DejaVu，稍小
            ("/System/Library/Fonts/Helvetica.ttc", 32, 20),                          # macOS
            ("/Windows/Fonts/arial.ttf", 32, 20),                                     # Windows
        ]

        for font_path, title_size, subtitle_size in font_configs:
            try:
                title_font = ImageFont.truetype(font_path, title_size)
                subtitle_font = ImageFont.truetype(font_path.replace("-Bold", ""), subtitle_size)
                break
            except (OSError, IOError):
                continue

        # 如果所有字体都失败，使用默认字体
        if title_font is None:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()

        # 添加标题 - 调整位置让标题更靠近图片
        title = "The performance of the DAGGER-GraphFormer agent in a validation scenario"
        title_bbox = draw.textbbox((0, 0), title, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (total_width - title_width) // 2
        draw.text((title_x, 15), title, fill=(0, 0, 0), font=title_font)  # 从20改为15，减小距离

        # 添加统计信息 - 调整位置
        # if stats:
        #     success_text = "✓ Success" if stats.get('success', False) else "✗ Failed"
        #     stats_text = f"{success_text} | Steps: {stats.get('steps', 0)} | Route Completion: {stats.get('route_completion', 0):.1f}%"
        #     stats_bbox = draw.textbbox((0, 0), stats_text, font=subtitle_font)
        #     stats_width = stats_bbox[2] - stats_bbox[0]
        #     stats_x = (total_width - stats_width) // 2
        #     draw.text((stats_x, 85), stats_text, fill=(100, 100, 100), font=subtitle_font)

        # 保存图片
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"dagger_visualization_ep{episode_id+1}_{timestamp}.png"
        filepath = self.output_dir / filename

        # 高质量保存
        combined_pil.save(filepath, format='PNG', dpi=(1500, 1500))
        print(f"💾 论文可视化图片已保存: {filepath}")

        return np.array(combined_pil)

class ModelTester:
    """模型测试器 (Refactored)"""

    def __init__(self, model_path, config, use_slow_npc=True, enable_visualization=False,
                 vis_save_interval=1.0, vis_max_images=9):
        """
        初始化模型测试器

        Args:
            model_path: 模型路径
            config: 配置字典
            use_slow_npc: 是否使用低速NPC
            enable_visualization: 是否启用可视化
            vis_save_interval: 可视化图片保存间隔（秒）
            vis_max_images: 最大保存图片数量
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.config = config
        self.use_slow_npc = use_slow_npc
        self.enable_visualization = enable_visualization

        # 如果启用低速NPC，应用猴子补丁
        if use_slow_npc:
            patch_traffic_manager_for_slow_npc()
            print("✅ 已将NPC车辆速度降低到10 km/h，自车保持正常速度")

        self.model = GraphTransformerModel(config).to(self.device)
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()

        self.expert_policy = ExpertPolicy(config['expert_type'])

        # 初始化图像收集器
        if enable_visualization:
            self.image_collector = ImageCollector(
                save_interval=vis_save_interval,
                max_images=vis_max_images
            )
            print(f"📸 图像可视化功能已启用 (间隔: {vis_save_interval}s, 最大图片数: {vis_max_images})")

        print("✅ ModelTester Initialized (Refactored)")
        print(f"📂 Model Path: {model_path}")
        print(f"🖥️ Device: {self.device}")
        print(f"📸 Visualization: {enable_visualization}")

    def _get_model_input(self, obs, env):
        """从环境状态创建模型输入."""
        sensor_features = get_pure_sensor_data(obs)

        ego = env.agent
        ego_state = np.array([
            ego.speed_km_h / 120.0, ego.heading_diff(ego.lane), ego.steering,
            ego.throttle_brake, ego.dist_to_left_side, ego.dist_to_right_side,
            np.linalg.norm(ego.velocity)
        ], dtype=np.float32)

        node_features = [ego_state]
        max_dist = self.config.get("max_npc_distance", 50)
        for v_id, v in env.engine.get_objects().items():
            if v_id != ego.id and hasattr(v, 'position'):
                rel_pos = ego.position - v.position
                distance = np.linalg.norm(rel_pos)
                if distance > max_dist:
                    continue
                rel_vel = ego.velocity - v.velocity
                npc_state = np.array([
                    np.linalg.norm(v.velocity), v.heading_diff(v.lane), rel_pos[0],
                    rel_pos[1], rel_vel[0], rel_vel[1], np.linalg.norm(rel_pos)
                ], dtype=np.float32)
                node_features.append(npc_state)

        node_features = np.array(node_features)
        num_nodes = len(node_features)
        edge_index = []
        for i in range(num_nodes):
            for j in range(i + 1, num_nodes):
                edge_index.extend([[i, j], [j, i]])
        
        if not edge_index and num_nodes > 0:
            edge_index = [[0, 0]]

        graph_data = Data(
            x=torch.from_numpy(node_features).float(),
            edge_index=torch.from_numpy(np.array(edge_index).T).long()
        )

        # 获取边界距离
        dist_left = env.agent.dist_to_left_side
        dist_right = env.agent.dist_to_right_side
        dist_to_boundaries = np.array([dist_left, dist_right], dtype=np.float32)

        return graph_data, sensor_features, dist_to_boundaries

    def test_episode(self, episode_id, max_steps=1000, render=None):
        env_config = self.config['env_config'].copy()

        # 如果启用可视化，强制开启渲染但保持原始观测格式
        if self.enable_visualization:
            env_config['use_render'] = True
            env_config['window_size'] = (1200, 900)  # 提高分辨率到1200x900
            # 去掉MetaDrive的logo和界面元素
            env_config['show_logo'] = False
            env_config['show_fps'] = False
            env_config['show_interface'] = False
            env_config['show_coordinates'] = False
        else:
            # 统一render配置：如果没有指定，使用配置文件中的设置
            if render is not None:
                env_config['use_render'] = render

        env = MetaDriveEnv(env_config)
        self.expert_policy.setup(env)

        # 计算seed：每个episode使用不同的seed，在有效范围内
        start_seed = env_config.get('start_seed', 100)
        num_scenarios = env_config.get('num_scenarios', 1)
        # 每个episode使用不同的seed，如果超出范围则循环使用
        seed = start_seed + (episode_id % num_scenarios)

        obs, info = env.reset(seed=seed)

        # 初始化图像收集器
        if self.enable_visualization:
            self.image_collector.start_episode()

        stats = {
            'steps': 0,
            'success': False,
            'out_of_road': False,
            'collision': False,
            'route_completion': 0.0,  # 新增：路线完成度
            'reward': 0.0,  # 新增：总奖励
            'distance': 0.0  # 新增：行驶距离
        }

        # 获取初始位置用于计算距离
        if hasattr(env.agent, 'position'):
            initial_position = np.array([env.agent.position[0], env.agent.position[1]])
        else:
            initial_position = np.array([0, 0])

        for step in range(max_steps):
            if obs is None:
                print(f"WARN: Obs is None at step {step}, skipping.")
                break

            # 现在主环境始终使用88维观测格式
            state_obs = obs

            graph_data, sensor_features, dist_to_boundaries = self._get_model_input(state_obs, env)

            with torch.no_grad():
                batch = {
                    'graph_batch': Batch.from_data_list([graph_data]).to(self.device),
                    'sequence_features': torch.from_numpy(sensor_features).float().unsqueeze(0).to(self.device),
                    'dist_to_boundaries': torch.from_numpy(dist_to_boundaries).float().unsqueeze(0).to(self.device) # 新增
                }
                model_action = self.model(batch)['action_pred'].cpu().numpy()[0]

            obs, reward, terminated, truncated, info = env.step(model_action)

            # 收集图像（如果启用可视化）
            if self.enable_visualization:
                try:
                    # 使用MetaDrive的_get_window_image方法获取真实渲染图像
                    current_image = None

                    if hasattr(env, 'engine') and hasattr(env.engine, '_get_window_image'):
                        try:
                            current_image = env.engine._get_window_image()
                            # MetaDrive返回的是RGB格式，确保颜色正确
                            if current_image is not None and len(current_image.shape) == 3:
                                # 保持RGB格式，不做任何颜色转换
                                pass
                        except Exception as e:
                            if step == 0:
                                print(f"WARN: _get_window_image失败: {e}")

                    # 如果获取失败，创建占位图像
                    if current_image is None:
                        current_image = np.ones((300, 400, 3), dtype=np.uint8) * 128  # 灰色占位图
                        if step == 0:
                            print("WARN: 使用占位图像")

                    # 确保图像格式正确
                    if current_image is not None:
                        # 准备步骤信息
                        step_info = {
                            'step': step,
                            'speed': getattr(env.agent, 'speed_km_h', 0)
                        }

                        # 添加图像到收集器（确保是RGB格式）
                        self.image_collector.add_image(current_image, step_info)

                except Exception as e:
                    # 如果截图失败，不影响主要功能
                    if step == 0:  # 只在第一步打印警告
                        print(f"WARN: 图像收集失败: {e}")

            stats['steps'] = step + 1
            stats['reward'] += reward

            # 更新距离
            if hasattr(env.agent, 'position'):
                current_position = np.array([env.agent.position[0], env.agent.position[1]])
                stats['distance'] = np.linalg.norm(current_position - initial_position)

            if info.get('arrive_dest', False):
                stats['success'] = True
                print(f"✅ Episode {episode_id+1}: Success! ({stats['steps']} steps)")
                break
            if info.get('out_of_road', False):
                stats['out_of_road'] = True
                print(f"❌ Episode {episode_id+1}: Out of road. ({stats['steps']} steps)")
                break
            if info.get('crash', False):
                stats['collision'] = True
                print(f"💥 Episode {episode_id+1}: Collision! ({stats['steps']} steps)")
                break
            if terminated or truncated:
                print(f"🔄 Episode {episode_id+1}: Terminated. ({stats['steps']} steps)")
                break

        # 计算路线完成度
        try:
            stats['route_completion'] = getattr(env.agent.navigation, 'route_completion', 0.0) * 100
        except (AttributeError, TypeError):
            stats['route_completion'] = 0.0

        # 生成组合图片（如果启用可视化）
        if self.enable_visualization:
            # 确保最后一帧被包含
            self.image_collector.finalize_episode()
            self.image_collector.create_combined_image(episode_id, stats)

        env.close()
        return stats

    def run_test(self, num_episodes=50, render=None):
        """
        运行模型测试

        Args:
            num_episodes: 测试的episode数量
            render: 是否渲染，None表示使用配置文件中的设置
        """
        if self.enable_visualization:
            render_status = True  # 可视化模式强制开启渲染
            print(f"\n🧪 Starting Model Test with Visualization ({num_episodes} episodes)")
            print(f"📸 图片将保存到: {self.image_collector.output_dir}")
        else:
            render_status = render if render is not None else self.config['env_config'].get('use_render', False)
            print(f"\n🧪 Starting Model Test ({num_episodes} episodes, render={render_status})")

        results = [self.test_episode(i, render=render) for i in range(num_episodes)]
        self.print_summary(results)
        return results

    def print_summary(self, results):
        total = len(results)
        if total == 0:
            return

        success = sum(r['success'] for r in results)
        out_of_road = sum(r['out_of_road'] for r in results)
        collision = sum(r['collision'] for r in results)
        avg_steps = np.mean([r['steps'] for r in results])

        # 新增统计信息
        avg_route_completion = np.mean([r['route_completion'] for r in results])
        avg_reward = np.mean([r['reward'] for r in results])
        avg_distance = np.mean([r['distance'] for r in results])

        # 计算超时率（既不成功也不出路也不碰撞的情况）
        timeout = total - success - out_of_road - collision
        timeout_rate = timeout / total

        print("\n" + "="*60)
        print("🏁 Model Test Summary")
        print("="*60)
        print(f"📊 Total Episodes: {total}")
        print(f"🎯 Success Rate: {success/total:.1%}")
        print(f"🛣️ Out of Road Rate: {out_of_road/total:.1%}")
        print(f"💥 Collision Rate: {collision/total:.1%}")
        print(f"⏰ Timeout Rate: {timeout_rate:.1%}")
        print(f"👣 Average Steps: {avg_steps:.1f}")
        print(f"🗺️ Average Route Completion: {avg_route_completion:.1f}%")
        print(f"🏆 Average Reward: {avg_reward:.2f}")
        print(f"📏 Average Distance: {avg_distance:.1f}m")



def create_paper_visualization(
    model_path="/home/<USER>/rl/RL-IL/dagger_gnn_transformer/dagger_model/best_model.pth",
    num_episodes=3,
    vis_save_interval=2.0,
    vis_max_images=9,
    output_dir="paper_visualization"
):
    """
    创建适合论文的可视化图片

    Args:
        model_path: 模型路径
        num_episodes: 测试的episode数量
        vis_save_interval: 图片保存间隔（秒）
        vis_max_images: 每个episode最大图片数量
        output_dir: 输出目录
    """
    config = {
        'hidden_dim': 128, 'transformer_dim': 256, 'num_layers': 2,
        'num_transformer_layers': 4, 'num_heads': 8, 'max_vehicles': 10,
        'dropout': 0.1, 'sequence_dim': 72, 'ego_node_dim': 7, 'npc_node_dim': 7,
        'action_dim': 2, 'max_npc_distance': 30, 'expert_type': 'expert',
        'env_config': {
            "out_of_road_done": False,
            "use_render": False,
            "num_scenarios": 1,
            "traffic_density": 0.1,
            "start_seed": 15,  # 580
            "map":"SSSSSyORXOyYCCSCSOOOXyy", #SSSOXyY   yORXOyYCCSCSOOOXyy
            "accident_prob": 0.0,
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            }
        }
    }

    print("=== 生成论文可视化图片 ===")
    print(f"📊 参数设置:")
    print(f"  - Episode数量: {num_episodes}")
    print(f"  - 图片间隔: {vis_save_interval}秒")
    print(f"  - 每episode最大图片数: {vis_max_images}")
    print(f"  - 输出目录: {output_dir}")

    tester = ModelTester(
        model_path, config,
        use_slow_npc=True,
        enable_visualization=True,
        vis_save_interval=vis_save_interval,
        vis_max_images=vis_max_images
    )

    # 修改输出目录
    tester.image_collector.output_dir = Path(output_dir)
    tester.image_collector.output_dir.mkdir(exist_ok=True)

    # 运行测试
    results = tester.run_test(num_episodes=num_episodes, render=None)

    # 统计结果
    success_rate = sum(r['success'] for r in results) / len(results)
    avg_completion = np.mean([r['route_completion'] for r in results])

    print(f"\n📊 生成结果:")
    print(f"成功率: {success_rate:.1%}")
    print(f"路线完成度: {avg_completion:.1f}%")
    print(f"📸 可视化图片已保存到: {output_dir}")

    return results

def main():
    """主函数 - 可以选择不同的测试模式"""

    # 选择测试模式
    mode = "paper_visualization"  # 可选: "paper_visualization", "debug_test"

    if mode == "paper_visualization":
        # 生成论文用的高质量可视化图片
        create_paper_visualization(
            num_episodes=1,           # 生成3个episode
            vis_save_interval=1,    # 每1.5秒保存一张图片
            vis_max_images=12,         # 每个episode最多9张图片（3x3布局）
            output_dir="paper_visualization"
        )
        return True

    # 原来的调试测试代码
    model_path =  "/home/<USER>/rl/RL-IL/dagger_gnn_transformer/dagger_model/best_model.pth"


    # 统一配置：与main.py中的训练配置保持一致
    config = {
    }

    if not Path(model_path).exists():
        print(f"❌ Error: Model path not found: {model_path}")
        return False

    try:
        # 测试混合模式：88维观测 + 图像可视化
        print("=== 测试混合模式：88维观测 + 图像可视化 ===")

        # 可配置的可视化参数
        vis_save_interval = 2.0  # 每2秒保存一张图片
        vis_max_images = 9       # 最多9张图片（3x3布局）

        tester = ModelTester(
            model_path, config,
            use_slow_npc=True,
            enable_visualization=True,
            vis_save_interval=vis_save_interval,
            vis_max_images=vis_max_images
        )

        # 运行测试，生成可视化图片
        results = tester.run_test(num_episodes=1, render=None)

        # 检查性能
        success_rate = sum(r['success'] for r in results) / len(results)
        avg_completion = np.mean([r['route_completion'] for r in results])

        print(f"\n📊 混合模式结果:")
        print(f"成功率: {success_rate:.1%}")
        print(f"路线完成度: {avg_completion:.1f}%")
        print(f"📸 可视化图片已保存到: {tester.image_collector.output_dir}")

        return True
    except Exception as e:
        print(f"\n❌ An error occurred during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if main():
        print("\n🎉 Model test finished successfully!")
    else:
        print("\n💥 Model test failed!")
        sys.exit(1)
