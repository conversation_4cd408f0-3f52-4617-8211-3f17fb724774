#!/usr/bin/env python3
"""
测试DAGGER训练的模型并生成Top-Down俯视图可视化 (Top-Down View)
"""

import os
import sys
import torch
import numpy as np
import cv2
import time
from pathlib import Path
from torch_geometric.data import Data, Batch
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
from datetime import datetime

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.dagger_trainer import ExpertPolicy
from models.graph_transformer_model import GraphTransformerModel
from metadrive import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy
from metadrive.component.sensors.rgb_camera import RGBCamera

# 创建专门用于NPC车辆的低速IDM策略
class NPCSlowIDMPolicy(IDMPolicy):
    """
    专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
    """
    NORMAL_SPEED = 10  # km/h
    MAX_SPEED = 10  # km/h
    CREEP_SPEED = 10  # km/h
    ACC_FACTOR = 0.5  # 降低加速度因子

    def __init__(self, control_object, random_seed):
        super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
        self.target_speed = self.NORMAL_SPEED

def patch_traffic_manager_for_slow_npc():
    """
    通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy
    这样只有NPC车辆会使用低速，自车保持正常速度
    """
    import metadrive.manager.traffic_manager as tm

    # 保存原始的add_policy方法
    original_add_policy = tm.PGTrafficManager.add_policy

    def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
        # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
        if policy_class == IDMPolicy:
            policy_class = NPCSlowIDMPolicy
        return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

    # 应用patch
    tm.PGTrafficManager.add_policy = patched_add_policy

def get_pure_sensor_data(obs: np.ndarray) -> np.ndarray:
    """从观测中提取纯传感器数据."""
    if not isinstance(obs, np.ndarray):
        obs = np.array(obs).flatten()

    # 处理不同维度的观测
    if obs.shape[0] == 88:
        # 原始88维观测格式
        side_detector_obs = obs[0:30]
        lane_line_detector_obs = obs[36:48]
        lidar_obs = obs[58:88]
        pure_sensors = np.concatenate([lidar_obs, side_detector_obs, lane_line_detector_obs])
    elif obs.shape[0] == 58:
        # 图像观测模式下的58维状态格式
        # 根据分析，58维状态的前42维包含了side_detector和lane_line_detector数据
        # 格式：[side_detector(30维), lane_line_detector(12维), 其他状态(16维)]

        # 提取传感器数据
        side_detector_obs = obs[0:30]  # 与88维格式完全一致
        lane_line_detector_obs = obs[30:42]  # 与88维格式完全一致

        # 对于lidar数据，我们没有直接对应，但可以用剩余的16维数据来近似
        # 或者使用一个合理的默认值（比如最大距离）
        remaining_data = obs[42:58]  # 16维剩余数据

        # 构造30维lidar数据
        lidar_obs = np.ones(30, dtype=np.float32)  # 默认最大距离

        # 用剩余数据的一些信息来调整lidar数据
        if len(remaining_data) >= 16:
            # 将16维数据扩展到30维，重复使用一些值
            for i in range(30):
                lidar_obs[i] = remaining_data[i % 16]

        pure_sensors = np.concatenate([lidar_obs, side_detector_obs, lane_line_detector_obs])
    else:
        # 其他维度，返回零向量
        print(f"WARN: 未知的观测维度: {obs.shape[0]}, 使用零向量")
        pure_sensors = np.zeros(72, dtype=np.float32)

    return pure_sensors.astype(np.float32)

class TopDownImageCollector:
    """Top-Down俯视图收集器，只保存最终的俯视图"""

    def __init__(self, output_dir="top_down_visualization"):
        """
        初始化Top-Down图像收集器

        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        self.final_image = None  # 只保存最终的俯视图
        self.episode_start_time = 0

    def start_episode(self):
        """开始新的episode"""
        self.final_image = None
        self.episode_start_time = time.time()

    def set_final_image(self, image_data, stats=None):
        """设置最终的俯视图"""
        if isinstance(image_data, np.ndarray):
            if image_data.dtype != np.uint8:
                if image_data.max() <= 1.0:
                    image_data = (image_data * 255).astype(np.uint8)
                else:
                    image_data = image_data.astype(np.uint8)

            # 添加统计信息到图片上
            self.final_image = self._add_stats_to_image(image_data, stats)
            print("📸 已保存最终俯视图")

    def _add_stats_to_image(self, image, stats):
        """不添加任何文字，直接返回原图"""
        return image.copy()

    def save_final_image(self, episode_id):
        """保存最终的俯视图"""
        if self.final_image is None:
            print("⚠️ 没有俯视图可保存")
            return None

        # 保存图片
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"top_down_ep{episode_id+1}_{timestamp}.png"
        filepath = self.output_dir / filename

        # 转换为PIL图像并保存
        img_pil = Image.fromarray(self.final_image)
        img_pil.save(filepath, format='PNG', dpi=(300, 300))
        print(f"💾 俯视图已保存: {filepath}")

        return filepath

    # 删除create_combined_image方法，因为我们只需要保存单张俯视图

class ModelTester:
    """模型测试器 (Refactored)"""

    def __init__(self, model_path, config, use_slow_npc=True, enable_visualization=False,
                 vis_save_interval=1.0, vis_max_images=9):
        """
        初始化模型测试器

        Args:
            model_path: 模型路径
            config: 配置字典
            use_slow_npc: 是否使用低速NPC
            enable_visualization: 是否启用可视化
            vis_save_interval: 可视化图片保存间隔（秒）
            vis_max_images: 最大保存图片数量
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.config = config
        self.use_slow_npc = use_slow_npc
        self.enable_visualization = enable_visualization

        # 如果启用低速NPC，应用猴子补丁
        if use_slow_npc:
            patch_traffic_manager_for_slow_npc()
            print("✅ 已将NPC车辆速度降低到10 km/h，自车保持正常速度")

        self.model = GraphTransformerModel(config).to(self.device)
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()

        self.expert_policy = ExpertPolicy(config['expert_type'])

        # 初始化Top-Down图像收集器
        if enable_visualization:
            self.image_collector = TopDownImageCollector()
            print("📸 Top-Down俯视图可视化功能已启用")

        print("✅ ModelTester Initialized (Refactored)")
        print(f"📂 Model Path: {model_path}")
        print(f"🖥️ Device: {self.device}")
        print(f"📸 Visualization: {enable_visualization}")

    def _get_model_input(self, obs, env):
        """从环境状态创建模型输入."""
        sensor_features = get_pure_sensor_data(obs)

        ego = env.agent
        ego_state = np.array([
            ego.speed_km_h / 120.0, ego.heading_diff(ego.lane), ego.steering,
            ego.throttle_brake, ego.dist_to_left_side, ego.dist_to_right_side,
            np.linalg.norm(ego.velocity)
        ], dtype=np.float32)

        node_features = [ego_state]
        max_dist = self.config.get("max_npc_distance", 50)
        for v_id, v in env.engine.get_objects().items():
            if v_id != ego.id and hasattr(v, 'position'):
                rel_pos = ego.position - v.position
                distance = np.linalg.norm(rel_pos)
                if distance > max_dist:
                    continue
                rel_vel = ego.velocity - v.velocity
                npc_state = np.array([
                    np.linalg.norm(v.velocity), v.heading_diff(v.lane), rel_pos[0],
                    rel_pos[1], rel_vel[0], rel_vel[1], np.linalg.norm(rel_pos)
                ], dtype=np.float32)
                node_features.append(npc_state)

        node_features = np.array(node_features)
        num_nodes = len(node_features)
        edge_index = []
        for i in range(num_nodes):
            for j in range(i + 1, num_nodes):
                edge_index.extend([[i, j], [j, i]])
        
        if not edge_index and num_nodes > 0:
            edge_index = [[0, 0]]

        graph_data = Data(
            x=torch.from_numpy(node_features).float(),
            edge_index=torch.from_numpy(np.array(edge_index).T).long()
        )

        # 获取边界距离
        dist_left = env.agent.dist_to_left_side
        dist_right = env.agent.dist_to_right_side
        dist_to_boundaries = np.array([dist_left, dist_right], dtype=np.float32)

        return graph_data, sensor_features, dist_to_boundaries

    def test_episode(self, episode_id, max_steps=1000, render=None):
        env_config = self.config['env_config'].copy()

        # 如果启用可视化，配置环境（使用有效的MetaDrive配置参数）
        if self.enable_visualization:
            env_config['use_render'] = True
            # 移除界面元素
            env_config['show_logo'] = False
            env_config['show_fps'] = False
            env_config['show_interface'] = False
            env_config['show_coordinates'] = False
            # 启用策略标记（这个参数存在）
            env_config['show_policy_mark'] = True
            # 启用episode记录（可能有助于轨迹显示）
            env_config['record_episode'] = True
            # 设置top-down相机位置
            env_config['top_down_camera_initial_x'] = 0
            env_config['top_down_camera_initial_y'] = 0
            env_config['top_down_camera_initial_z'] = 80
        else:
            # 统一render配置：如果没有指定，使用配置文件中的设置
            if render is not None:
                env_config['use_render'] = render

        env = MetaDriveEnv(env_config)
        self.expert_policy.setup(env)

        # 计算seed：每个episode使用不同的seed，在有效范围内
        start_seed = env_config.get('start_seed', 100)
        num_scenarios = env_config.get('num_scenarios', 1)
        # 每个episode使用不同的seed，如果超出范围则循环使用
        seed = start_seed + (episode_id % num_scenarios)

        obs, info = env.reset(seed=seed)

        # 初始化图像收集器
        if self.enable_visualization:
            self.image_collector.start_episode()

        stats = {
            'steps': 0,
            'success': False,
            'out_of_road': False,
            'collision': False,
            'route_completion': 0.0,  # 新增：路线完成度
            'reward': 0.0,  # 新增：总奖励
            'distance': 0.0  # 新增：行驶距离
        }

        # 获取初始位置用于计算距离
        if hasattr(env.agent, 'position'):
            initial_position = np.array([env.agent.position[0], env.agent.position[1]])
        else:
            initial_position = np.array([0, 0])

        for step in range(max_steps):
            if obs is None:
                print(f"WARN: Obs is None at step {step}, skipping.")
                break

            # 现在主环境始终使用88维观测格式
            state_obs = obs

            graph_data, sensor_features, dist_to_boundaries = self._get_model_input(state_obs, env)

            with torch.no_grad():
                batch = {
                    'graph_batch': Batch.from_data_list([graph_data]).to(self.device),
                    'sequence_features': torch.from_numpy(sensor_features).float().unsqueeze(0).to(self.device),
                    'dist_to_boundaries': torch.from_numpy(dist_to_boundaries).float().unsqueeze(0).to(self.device) # 新增
                }
                model_action = self.model(batch)['action_pred'].cpu().numpy()[0]

            obs, reward, terminated, truncated, info = env.step(model_action)

            # 在每一步都调用render来积累轨迹历史
            if self.enable_visualization:
                try:
                    # 每一步都渲染，积累轨迹历史
                    current_image = env.render(
                        mode="top_down",
                        window=False,  # 不显示窗口，只返回图像
                        screen_size=(1200, 400),  # 长条形尺寸，适合直线道路
                        film_size=(3000, 1000),   # 高分辨率长条形
                        num_stack=1000,           # 增加历史帧数
                        history_smooth=1,         # 减少平滑，显示更多轨迹点
                        draw_target_vehicle_trajectory=True,  # 启用目标车辆轨迹
                        draw_state_arrow=True     # 启用状态箭头
                    )

                    # 在episode结束时保存最终图像
                    if (terminated or truncated or
                        info.get('arrive_dest', False) or
                        info.get('out_of_road', False) or
                        info.get('crash', False)):
                        if current_image is not None:
                            self.image_collector.set_final_image(current_image, stats)

                except Exception as e:
                    print(f"WARN: 获取俯视图失败: {e}")

            stats['steps'] = step + 1
            stats['reward'] += reward

            # 更新距离
            if hasattr(env.agent, 'position'):
                current_position = np.array([env.agent.position[0], env.agent.position[1]])
                stats['distance'] = np.linalg.norm(current_position - initial_position)

            if info.get('arrive_dest', False):
                stats['success'] = True
                print(f"✅ Episode {episode_id+1}: Success! ({stats['steps']} steps)")
                break
            if info.get('out_of_road', False):
                stats['out_of_road'] = True
                print(f"❌ Episode {episode_id+1}: Out of road. ({stats['steps']} steps)")
                break
            if info.get('crash', False):
                stats['collision'] = True
                print(f"💥 Episode {episode_id+1}: Collision! ({stats['steps']} steps)")
                break
            if terminated or truncated:
                print(f"🔄 Episode {episode_id+1}: Terminated. ({stats['steps']} steps)")
                break

        # 计算路线完成度
        try:
            stats['route_completion'] = getattr(env.agent.navigation, 'route_completion', 0.0) * 100
        except (AttributeError, TypeError):
            stats['route_completion'] = 0.0

        # 保存最终的俯视图（如果启用可视化）
        if self.enable_visualization:
            # 保存俯视图
            self.image_collector.save_final_image(episode_id)

        env.close()
        return stats

    def run_test(self, num_episodes=50, render=None):
        """
        运行模型测试

        Args:
            num_episodes: 测试的episode数量
            render: 是否渲染，None表示使用配置文件中的设置
        """
        if self.enable_visualization:
            render_status = True  # 可视化模式强制开启渲染
            print(f"\n🧪 Starting Model Test with Visualization ({num_episodes} episodes)")
            print(f"📸 图片将保存到: {self.image_collector.output_dir}")
        else:
            render_status = render if render is not None else self.config['env_config'].get('use_render', False)
            print(f"\n🧪 Starting Model Test ({num_episodes} episodes, render={render_status})")

        results = [self.test_episode(i, render=render) for i in range(num_episodes)]
        self.print_summary(results)
        return results

    def print_summary(self, results):
        total = len(results)
        if total == 0:
            return

        success = sum(r['success'] for r in results)
        out_of_road = sum(r['out_of_road'] for r in results)
        collision = sum(r['collision'] for r in results)
        avg_steps = np.mean([r['steps'] for r in results])

        # 新增统计信息
        avg_route_completion = np.mean([r['route_completion'] for r in results])
        avg_reward = np.mean([r['reward'] for r in results])
        avg_distance = np.mean([r['distance'] for r in results])

        # 计算超时率（既不成功也不出路也不碰撞的情况）
        timeout = total - success - out_of_road - collision
        timeout_rate = timeout / total

        print("\n" + "="*60)
        print("🏁 Model Test Summary")
        print("="*60)
        print(f"📊 Total Episodes: {total}")
        print(f"🎯 Success Rate: {success/total:.1%}")
        print(f"🛣️ Out of Road Rate: {out_of_road/total:.1%}")
        print(f"💥 Collision Rate: {collision/total:.1%}")
        print(f"⏰ Timeout Rate: {timeout_rate:.1%}")
        print(f"👣 Average Steps: {avg_steps:.1f}")
        print(f"🗺️ Average Route Completion: {avg_route_completion:.1f}%")
        print(f"🏆 Average Reward: {avg_reward:.2f}")
        print(f"📏 Average Distance: {avg_distance:.1f}m")



def create_top_down_visualization(
    model_path="/home/<USER>/rl/RL-IL/dagger_gnn_transformer/dagger_model/best_model.pth",
    num_episodes=1,
    output_dir="top_down_visualization"
):
    """
    创建Top-Down俯视图可视化

    Args:
        model_path: 模型路径
        num_episodes: 测试的episode数量
        output_dir: 输出目录
    """
    config = {
        'hidden_dim': 128, 'transformer_dim': 256, 'num_layers': 2,
        'num_transformer_layers': 4, 'num_heads': 8, 'max_vehicles': 10,
        'dropout': 0.1, 'sequence_dim': 72, 'ego_node_dim': 7, 'npc_node_dim': 7,
        'action_dim': 2, 'max_npc_distance': 30, 'expert_type': 'expert',
        'env_config': {
            "out_of_road_done": True,
            "use_render": False,
            "num_scenarios": 10,
            "traffic_density": 0.1,
            "start_seed": 10,
            "map": "SSSSS",
            "accident_prob": 0.0,
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            }
        }
    }

    print("=== 生成Top-Down俯视图 ===")
    print(f"📊 参数设置:")
    print(f"  - Episode数量: {num_episodes}")
    print(f"  - 输出目录: {output_dir}")

    tester = ModelTester(
        model_path, config,
        use_slow_npc=True,
        enable_visualization=True
    )

    # 修改输出目录
    tester.image_collector.output_dir = Path(output_dir)
    tester.image_collector.output_dir.mkdir(exist_ok=True)

    # 运行测试
    results = tester.run_test(num_episodes=num_episodes, render=None)

    # 统计结果
    success_rate = sum(r['success'] for r in results) / len(results)
    avg_completion = np.mean([r['route_completion'] for r in results])

    print(f"\n📊 生成结果:")
    print(f"成功率: {success_rate:.1%}")
    print(f"路线完成度: {avg_completion:.1f}%")
    print(f"📸 Top-Down俯视图已保存到: {output_dir}")

    return results

def main():
    """主函数 - 生成Top-Down俯视图"""

    print("🎯 生成DAGGER模型Top-Down俯视图")
    print("=" * 50)

    # 生成单张俯视图
    create_top_down_visualization(
        num_episodes=1,
        output_dir="top_down_visualization"
    )
    return True

if __name__ == "__main__":
    if main():
        print("\n🎉 Model test finished successfully!")
    else:
        print("\n💥 Model test failed!")
        sys.exit(1)
