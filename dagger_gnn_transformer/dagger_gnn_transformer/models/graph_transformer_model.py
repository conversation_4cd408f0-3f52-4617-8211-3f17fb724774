"""
图神经网络+Transformer混合模型
结合图结构信息和序列建模能力进行模仿学习
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GCNConv, GATConv, GraphNorm, global_mean_pool
from torch_geometric.data import Data, Batch
import numpy as np
from typing import Dict, List, Tuple, Optional

class GraphNeuralNetwork(nn.Module):
    """图神经网络模块 - 处理车辆间的空间关系"""
    
    def __init__(self, node_dim: int, hidden_dim: int, output_dim: int,
                 num_layers: int = 2, gnn_type: str = "GCN", dropout: float = 0.1):
        super().__init__()
        self.gnn_type = gnn_type
        self.num_layers = num_layers
        self.dropout = dropout
        
        self.node_embedding = nn.Linear(node_dim, hidden_dim)
        
        self.gnn_layers = nn.ModuleList()
        self.norms = nn.ModuleList()
        
        for i in range(num_layers):
            input_dim = hidden_dim
            if gnn_type == "GCN":
                self.gnn_layers.append(GCNConv(input_dim, hidden_dim))
            elif gnn_type == "GAT":
                self.gnn_layers.append(GATConv(input_dim, hidden_dim, heads=4, concat=False))
            else:
                raise ValueError(f"Unsupported GNN type: {gnn_type}")
            
            self.norms.append(GraphNorm(hidden_dim))
        
        self.output_proj = nn.Linear(hidden_dim, output_dim)
        self.dropout_layer = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor,
                batch: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Args:
            x: Node features [num_nodes, node_dim]
            edge_index: Edge index [2, num_edges]
            batch: Batch assignment vector [num_nodes]
        """
        x = self.node_embedding(x)
        x = F.relu(x)
        
        for i, (gnn_layer, norm) in enumerate(zip(self.gnn_layers, self.norms)):
            residual = x
            x = gnn_layer(x, edge_index)
            x = norm(x, batch)
            x = F.relu(x)
            x = self.dropout_layer(x)
            if residual.shape[-1] == x.shape[-1]:
                x = x + residual
        
        x = self.output_proj(x)
        return x

class TransformerBlock(nn.Module):
    """Transformer块"""
    
    def __init__(self, d_model: int, num_heads: int, ff_dim: int, dropout: float = 0.1):
        super().__init__()
        self.attention = nn.MultiheadAttention(d_model, num_heads, dropout=dropout, batch_first=True)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        self.feed_forward = nn.Sequential(
            nn.Linear(d_model, ff_dim),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(ff_dim, d_model)
        )
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        attn_output, _ = self.attention(x, x, x, key_padding_mask=mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        return x

class GraphTransformerModel(nn.Module):
    """图神经网络+Transformer混合模型 (Refactored)"""
    
    def __init__(self, config: Dict):
        super().__init__()
        self.config = config
        
        # 从配置中获取参数
        self.node_dim = config.get('ego_node_dim', 7)
        self.sequence_dim = config.get('sequence_dim', 72)
        self.hidden_dim = config.get('hidden_dim', 128)
        self.transformer_dim = config.get('transformer_dim', 256)
        self.num_heads = config.get('num_heads', 8)
        self.num_transformer_layers = config.get('num_transformer_layers', 4)
        self.num_gnn_layers = config.get('num_layers', 2)
        self.gnn_type = config.get('gnn_type', 'GCN')
        self.dropout = config.get('dropout', 0.1)
        self.action_dim = config.get('action_dim', 2)

        # GNN模块
        self.gnn = GraphNeuralNetwork(
            node_dim=self.node_dim,
            hidden_dim=self.hidden_dim,
            output_dim=self.hidden_dim,
            num_layers=self.num_gnn_layers,
            gnn_type=self.gnn_type,
            dropout=self.dropout
        )
        
        # 图特征聚合到Transformer维度的一半
        self.graph_aggregator = nn.Linear(self.hidden_dim, self.transformer_dim // 2)
        
        # 序列特征投影到Transformer维度的一半
        self.sequence_proj = nn.Linear(self.sequence_dim, self.transformer_dim // 2)
        
        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(self.transformer_dim, self.transformer_dim),
            nn.LayerNorm(self.transformer_dim),
            nn.ReLU(),
            nn.Dropout(self.dropout)
        )
        
        # Transformer编码器
        self.transformer_layers = nn.ModuleList([
            TransformerBlock(
                d_model=self.transformer_dim,
                num_heads=self.num_heads,
                ff_dim=self.transformer_dim * 4,
                dropout=self.dropout
            ) for _ in range(self.num_transformer_layers)
        ])
        
        # 动作和价值输出头
        self.action_head = nn.Sequential(
            nn.Linear(self.transformer_dim, self.transformer_dim // 2),
            nn.ReLU(),
            nn.Linear(self.transformer_dim // 2, self.action_dim)
        )
        
        self.value_head = nn.Sequential(
            nn.Linear(self.transformer_dim, self.transformer_dim // 2),
            nn.ReLU(),
            nn.Linear(self.transformer_dim // 2, 1)
        )

        # print("✅ Refactored Graph+Transformer Model Initialized")

    def forward(self, batch_data: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        前向传播 (Refactored)
        
        Args:
            batch_data:
                - graph_batch: torch_geometric.data.Batch object
                - sequence_features: [batch_size, seq_dim]
        """
        graph_batch = batch_data['graph_batch']
        sequence_features = batch_data['sequence_features']
        
        # 1. GNN处理图数据
        # x: [total_nodes, node_dim], edge_index: [2, total_edges], batch: [total_nodes]
        x, edge_index, batch = graph_batch.x, graph_batch.edge_index, graph_batch.batch
        node_embeddings = self.gnn(x, edge_index, batch)
        
        # 2. 全局图特征聚合 (Mean Pooling)
        # [batch_size, hidden_dim]
        graph_features = global_mean_pool(node_embeddings, batch)
        
        # 3. 投影图特征
        # [batch_size, transformer_dim // 2]
        graph_features_proj = self.graph_aggregator(graph_features)
        
        # 4. 投影序列特征
        # [batch_size, transformer_dim // 2]
        seq_features_proj = self.sequence_proj(sequence_features)
        
        # 5. 融合特征
        # [batch_size, transformer_dim]
        combined_features = torch.cat([graph_features_proj, seq_features_proj], dim=-1)
        fused_features = self.feature_fusion(combined_features)
        
        # 6. Transformer处理
        # Transformer期望输入 [batch_size, seq_len, d_model], 我们这里seq_len=1
        transformer_input = fused_features.unsqueeze(1)
        
        transformer_output = transformer_input
        for layer in self.transformer_layers:
            transformer_output = layer(transformer_output)
        
        # 提取最终特征
        final_features = transformer_output.squeeze(1) # [batch_size, transformer_dim]
        
        # 7. 预测动作和价值
        action_pred = self.action_head(final_features)
        value_pred = self.value_head(final_features).squeeze(-1)
        
        # 对动作进行约束
        steering = torch.tanh(action_pred[:, 0]) * 0.8 # 限制在[-0.8, 0.8]
        throttle = torch.tanh(action_pred[:, 1]) # 限制在[-1, 1]
        
        action_pred = torch.stack([steering, throttle], dim=1)
        
        return {
            'action_pred': action_pred,
            'value_pred': value_pred
        }

def create_model(config: Dict = None) -> GraphTransformerModel:
    if config is None:
        # 提供一个用于独立测试的默认配置
        config = {
            'ego_node_dim': 7,
            'sequence_dim': 72,
            'hidden_dim': 128,
            'transformer_dim': 256,
            'num_heads': 8,
            'num_transformer_layers': 4,
            'num_layers': 2,
            'gnn_type': 'GCN',
            'dropout': 0.1,
            'action_dim': 2
        }
    return GraphTransformerModel(config)

# 测试函数
def test_model():
    print("=== Testing Refactored Graph+Transformer Model ===")

    # 检查GPU可用性
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ 测试设备: {device}")

    config = {
        'ego_node_dim': 7, 'sequence_dim': 72, 'hidden_dim': 128,
        'transformer_dim': 256, 'num_heads': 8, 'num_transformer_layers': 2,
        'num_layers': 2, 'gnn_type': 'GCN', 'dropout': 0.1, 'action_dim': 2
    }
    model = create_model(config)
    model = model.to(device)  # 移动模型到GPU
    model.eval()

    # 创建模拟数据
    batch_size = 4
    data_list = []
    for _ in range(batch_size):
        num_nodes = np.random.randint(1, 10)
        nodes = torch.randn(num_nodes, config['ego_node_dim'])

        num_edges = np.random.randint(num_nodes, num_nodes * 2)
        edge_index = torch.randint(0, num_nodes, (2, num_edges), dtype=torch.long)

        data_list.append(Data(x=nodes, edge_index=edge_index))

    graph_batch = Batch.from_data_list(data_list).to(device)  # 移动到GPU
    sequence_features = torch.randn(batch_size, config['sequence_dim']).to(device)  # 移动到GPU

    batch_data = {
        'graph_batch': graph_batch,
        'sequence_features': sequence_features
    }

    with torch.no_grad():
        outputs = model(batch_data)

    print("✅ Model test successful!")
    print(f"   Action prediction shape: {outputs['action_pred'].shape}")
    assert outputs['action_pred'].shape == (batch_size, 2)
    print(f"   Value prediction shape: {outputs['value_pred'].shape}")
    assert outputs['value_pred'].shape == (batch_size,)

    # GPU内存清理
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        print("🧹 GPU缓存已清理")

    return True

if __name__ == "__main__":
    test_model()
