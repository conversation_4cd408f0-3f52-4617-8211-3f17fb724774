#!/usr/bin/env python3
"""
测试IDM专家策略性能作为上限基准
"""

import os
import sys
import numpy as np

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.dagger_trainer import ExpertPolicy
from metadrive import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy

# 创建专门用于NPC车辆的低速IDM策略
class NPCSlowIDMPolicy(IDMPolicy):
    """
    专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
    """
    NORMAL_SPEED = 10  # km/h
    MAX_SPEED = 10  # km/h
    CREEP_SPEED = 10  # km/h
    ACC_FACTOR = 0.5  # 降低加速度因子

    def __init__(self, control_object, random_seed):
        super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
        self.target_speed = self.NORMAL_SPEED

def patch_traffic_manager_for_slow_npc():
    """
    通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy
    这样只有NPC车辆会使用低速，自车保持正常速度
    """
    import metadrive.manager.traffic_manager as tm

    # 保存原始的add_policy方法
    original_add_policy = tm.PGTrafficManager.add_policy

    def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
        # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
        if policy_class == IDMPolicy:
            policy_class = NPCSlowIDMPolicy
        return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

    # 应用patch
    tm.PGTrafficManager.add_policy = patched_add_policy

# IDM策略不需要传感器数据处理

class IDMTester:
    """IDM专家策略测试器"""

    def __init__(self, config, use_slow_npc=True):
        self.config = config
        self.use_slow_npc = use_slow_npc

        # 如果启用低速NPC，应用猴子补丁
        if use_slow_npc:
            patch_traffic_manager_for_slow_npc()
            print("✅ 已将NPC车辆速度降低到10 km/h，自车保持正常速度")

        # 使用IDM策略作为专家策略
        self.expert_policy = ExpertPolicy('idm')  # 强制使用IDM策略

        print(f"✅ IDMTester Initialized")
        print(f"🎯 使用IDM策略测试专家性能上限")

    # IDM策略不需要模型输入处理，直接使用专家策略

    def test_episode(self, episode_id, max_steps=1000, render=None):
        env_config = self.config['env_config'].copy()
        # 统一render配置：如果没有指定，使用配置文件中的设置
        if render is not None:
            env_config['use_render'] = render

        # 设置IDM策略为agent策略
        if self.config['expert_type'] == 'idm':
            env_config["agent_policy"] = IDMPolicy

        env = MetaDriveEnv(env_config)
        self.expert_policy.setup(env)

        # 计算seed：每个episode使用不同的seed，在有效范围内
        start_seed = env_config.get('start_seed', 100)
        num_scenarios = env_config.get('num_scenarios', 1)
        # 每个episode使用不同的seed，如果超出范围则循环使用
        seed = start_seed + (episode_id % num_scenarios)

        obs, info = env.reset(seed=seed)

        stats = {
            'steps': 0,
            'success': False,
            'out_of_road': False,
            'collision': False,
            'route_completion': 0.0,  # 新增：路线完成度
            'reward': 0.0,  # 新增：总奖励
            'distance': 0.0  # 新增：行驶距离
        }

        # 获取初始位置用于计算距离
        if hasattr(env.agent, 'position'):
            initial_position = np.array([env.agent.position[0], env.agent.position[1]])
        else:
            initial_position = np.array([0, 0])

        for step in range(max_steps):
            if obs is None:
                print(f"WARN: Obs is None at step {step}, skipping.")
                break

            # 使用占位动作，让环境自动使用IDM策略
            placeholder_action = [0.0, 0.0]
            obs, reward, terminated, truncated, info = env.step(placeholder_action)

            # 从info中获取实际执行的专家动作（用于记录）
            expert_action = info.get('action', placeholder_action)
            stats['steps'] = step + 1
            stats['reward'] += reward

            # 更新距离
            if hasattr(env.agent, 'position'):
                current_position = np.array([env.agent.position[0], env.agent.position[1]])
                stats['distance'] = np.linalg.norm(current_position - initial_position)

            if info.get('arrive_dest', False):
                stats['success'] = True
                print(f"✅ Episode {episode_id+1}: Success! ({stats['steps']} steps)")
                break
            if info.get('out_of_road', False):
                stats['out_of_road'] = True
                print(f"❌ Episode {episode_id+1}: Out of road. ({stats['steps']} steps)")
                break
            if info.get('crash', False):
                stats['collision'] = True
                print(f"💥 Episode {episode_id+1}: Collision! ({stats['steps']} steps)")
                break
            if terminated or truncated:
                print(f"🔄 Episode {episode_id+1}: Terminated. ({stats['steps']} steps)")
                break

        # 计算路线完成度
        try:
            stats['route_completion'] = getattr(env.agent.navigation, 'route_completion', 0.0) * 100
        except (AttributeError, TypeError):
            stats['route_completion'] = 0.0
        
        env.close()
        return stats

    def run_test(self, num_episodes=50, render=None):
        """
        运行IDM专家策略测试

        Args:
            num_episodes: 测试的episode数量
            render: 是否渲染，None表示使用配置文件中的设置
        """
        render_status = render if render is not None else self.config['env_config'].get('use_render', False)
        print(f"\n🧪 Starting IDM Expert Test ({num_episodes} episodes, render={render_status})")
        results = [self.test_episode(i, render=render) for i in range(num_episodes)]
        self.print_summary(results)
        return results

    def print_summary(self, results):
        total = len(results)
        if total == 0:
            return

        success = sum(r['success'] for r in results)
        out_of_road = sum(r['out_of_road'] for r in results)
        collision = sum(r['collision'] for r in results)
        avg_steps = np.mean([r['steps'] for r in results])

        # 新增统计信息
        avg_route_completion = np.mean([r['route_completion'] for r in results])
        avg_reward = np.mean([r['reward'] for r in results])
        avg_distance = np.mean([r['distance'] for r in results])

        # 计算超时率（既不成功也不出路也不碰撞的情况）
        timeout = total - success - out_of_road - collision
        timeout_rate = timeout / total

        print("\n" + "="*60)
        print("🏁 IDM Expert Performance Summary (Upper Bound)")
        print("="*60)
        print(f"📊 Total Episodes: {total}")
        print(f"🎯 Success Rate: {success/total:.1%}")
        print(f"🛣️ Out of Road Rate: {out_of_road/total:.1%}")
        print(f"💥 Collision Rate: {collision/total:.1%}")
        print(f"⏰ Timeout Rate: {timeout_rate:.1%}")
        print(f"👣 Average Steps: {avg_steps:.1f}")
        print(f"🗺️ Average Route Completion: {avg_route_completion:.1f}%")
        print(f"🏆 Average Reward: {avg_reward:.2f}")
        print(f"📏 Average Distance: {avg_distance:.1f}m")

def main():
    """主函数 - 测试IDM专家策略性能作为上限基准"""

    # IDM专家策略测试配置
    config = {
        'expert_type': 'idm',  # 强制使用IDM策略
        'env_config': {
            "out_of_road_done": True,
            "use_render": False,       # 默认开启渲染观察专家表现
            "num_scenarios": 10,      # 测试多个场景
            "traffic_density": 0.1,   # 与训练时保持一致
            "start_seed": 1,         # 与训练时保持一致
            "map": "SS",           # 与训练时保持一致
            "accident_prob": 0.0,
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            }
        }
    }

    try:
        print("🎯 测试IDM专家策略性能作为上限基准")
        # 启用低速NPC车辆，与训练时保持一致
        tester = IDMTester(config, use_slow_npc=True)
        # 使用配置文件中的render设置，也可以通过参数覆盖
        # render=None 表示使用配置文件中的设置
        # render=True 表示强制开启渲染
        # render=False 表示强制关闭渲染
        tester.run_test(num_episodes=100, render=None)  # 测试50个episode获得IDM专家性能基准
        return True
    except Exception as e:
        print(f"\n❌ An error occurred during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if main():
        print("\n🎉 IDM Expert test finished successfully!")
    else:
        print("\n💥 IDM Expert test failed!")
        sys.exit(1)
