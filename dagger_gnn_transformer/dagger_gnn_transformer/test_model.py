#!/usr/bin/env python3
"""
测试DAGGER训练的模型 (Refactored)
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path
from torch_geometric.data import Data, Batch

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tools.dagger_trainer import ExpertPolicy
from models.graph_transformer_model import GraphTransformerModel
from metadrive import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy

# 创建专门用于NPC车辆的低速IDM策略
class NPCSlowIDMPolicy(IDMPolicy):
    """
    专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
    """
    NORMAL_SPEED = 10  # km/h
    MAX_SPEED = 10  # km/h
    CREEP_SPEED = 10  # km/h
    ACC_FACTOR = 0.5  # 降低加速度因子

    def __init__(self, control_object, random_seed):
        super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
        self.target_speed = self.NORMAL_SPEED

def patch_traffic_manager_for_slow_npc():
    """
    通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy
    这样只有NPC车辆会使用低速，自车保持正常速度
    """
    import metadrive.manager.traffic_manager as tm

    # 保存原始的add_policy方法
    original_add_policy = tm.PGTrafficManager.add_policy

    def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
        # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
        if policy_class == IDMPolicy:
            policy_class = NPCSlowIDMPolicy
        return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

    # 应用patch
    tm.PGTrafficManager.add_policy = patched_add_policy

def get_pure_sensor_data(obs: np.ndarray) -> np.ndarray:
    """从原始88维观测中提取纯72维传感器数据."""
    if not isinstance(obs, np.ndarray):
        obs = np.array(obs).flatten()

    if obs.shape[0] != 88:
        return np.zeros(72, dtype=np.float32)

    side_detector_obs = obs[0:30]
    lane_line_detector_obs = obs[36:48]
    lidar_obs = obs[58:88]

    pure_sensors = np.concatenate([lidar_obs, side_detector_obs, lane_line_detector_obs])
    return pure_sensors.astype(np.float32)

class ModelTester:
    """模型测试器 (Refactored)"""

    def __init__(self, model_path, config, use_slow_npc=True):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.config = config
        self.use_slow_npc = use_slow_npc

        # 如果启用低速NPC，应用猴子补丁
        if use_slow_npc:
            patch_traffic_manager_for_slow_npc()
            print("✅ 已将NPC车辆速度降低到10 km/h，自车保持正常速度")
        
        self.model = GraphTransformerModel(config).to(self.device)
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        self.expert_policy = ExpertPolicy(config['expert_type'])
        
        print(f"✅ ModelTester Initialized (Refactored)")
        print(f"📂 Model Path: {model_path}")
        print(f"🖥️ Device: {self.device}")

    def _get_model_input(self, obs, env):
        """从环境状态创建模型输入."""
        sensor_features = get_pure_sensor_data(obs)

        ego = env.agent
        ego_state = np.array([
            ego.speed_km_h / 120.0, ego.heading_diff(ego.lane), ego.steering,
            ego.throttle_brake, ego.dist_to_left_side, ego.dist_to_right_side,
            np.linalg.norm(ego.velocity)
        ], dtype=np.float32)

        node_features = [ego_state]
        max_dist = self.config.get("max_npc_distance", 50)
        for v_id, v in env.engine.get_objects().items():
            if v_id != ego.id and hasattr(v, 'position'):
                rel_pos = ego.position - v.position
                distance = np.linalg.norm(rel_pos)
                if distance > max_dist:
                    continue
                rel_vel = ego.velocity - v.velocity
                npc_state = np.array([
                    np.linalg.norm(v.velocity), v.heading_diff(v.lane), rel_pos[0],
                    rel_pos[1], rel_vel[0], rel_vel[1], np.linalg.norm(rel_pos)
                ], dtype=np.float32)
                node_features.append(npc_state)

        node_features = np.array(node_features)
        num_nodes = len(node_features)
        edge_index = []
        for i in range(num_nodes):
            for j in range(i + 1, num_nodes):
                edge_index.extend([[i, j], [j, i]])
        
        if not edge_index and num_nodes > 0:
            edge_index = [[0, 0]]

        graph_data = Data(
            x=torch.from_numpy(node_features).float(),
            edge_index=torch.from_numpy(np.array(edge_index).T).long()
        )

        # 获取边界距离
        dist_left = env.agent.dist_to_left_side
        dist_right = env.agent.dist_to_right_side
        dist_to_boundaries = np.array([dist_left, dist_right], dtype=np.float32)

        return graph_data, sensor_features, dist_to_boundaries

    def test_episode(self, episode_id, max_steps=1000, render=None):
        env_config = self.config['env_config'].copy()
        # 统一render配置：如果没有指定，使用配置文件中的设置
        if render is not None:
            env_config['use_render'] = render
        env = MetaDriveEnv(env_config)
        self.expert_policy.setup(env)

        # 计算seed：每个episode使用不同的seed，在有效范围内
        start_seed = env_config.get('start_seed', 100)
        num_scenarios = env_config.get('num_scenarios', 1)
        # 每个episode使用不同的seed，如果超出范围则循环使用
        seed = start_seed + (episode_id % num_scenarios)

        obs, info = env.reset(seed=seed)

        stats = {
            'steps': 0,
            'success': False,
            'out_of_road': False,
            'collision': False,
            'route_completion': 0.0,  # 新增：路线完成度
            'reward': 0.0,  # 新增：总奖励
            'distance': 0.0  # 新增：行驶距离
        }

        # 获取初始位置用于计算距离
        if hasattr(env.agent, 'position'):
            initial_position = np.array([env.agent.position[0], env.agent.position[1]])
        else:
            initial_position = np.array([0, 0])

        for step in range(max_steps):
            if obs is None:
                print(f"WARN: Obs is None at step {step}, skipping.")
                break
            graph_data, sensor_features, dist_to_boundaries = self._get_model_input(obs, env)
            
            with torch.no_grad():
                batch = {
                    'graph_batch': Batch.from_data_list([graph_data]).to(self.device),
                    'sequence_features': torch.from_numpy(sensor_features).float().unsqueeze(0).to(self.device),
                    'dist_to_boundaries': torch.from_numpy(dist_to_boundaries).float().unsqueeze(0).to(self.device) # 新增
                }
                model_action = self.model(batch)['action_pred'].cpu().numpy()[0]

            obs, reward, terminated, truncated, info = env.step(model_action)
            stats['steps'] = step + 1
            stats['reward'] += reward

            # 更新距离
            if hasattr(env.agent, 'position'):
                current_position = np.array([env.agent.position[0], env.agent.position[1]])
                stats['distance'] = np.linalg.norm(current_position - initial_position)

            if info.get('arrive_dest', False):
                stats['success'] = True
                print(f"✅ Episode {episode_id+1}: Success! ({stats['steps']} steps)")
                break
            if info.get('out_of_road', False):
                stats['out_of_road'] = True
                print(f"❌ Episode {episode_id+1}: Out of road. ({stats['steps']} steps)")
                break
            if info.get('crash', False):
                stats['collision'] = True
                print(f"💥 Episode {episode_id+1}: Collision! ({stats['steps']} steps)")
                break
            if terminated or truncated:
                print(f"🔄 Episode {episode_id+1}: Terminated. ({stats['steps']} steps)")
                break

        # 计算路线完成度
        try:
            stats['route_completion'] = getattr(env.agent.navigation, 'route_completion', 0.0) * 100
        except (AttributeError, TypeError):
            stats['route_completion'] = 0.0
        
        env.close()
        return stats

    def run_test(self, num_episodes=50, render=None):
        """
        运行模型测试

        Args:
            num_episodes: 测试的episode数量
            render: 是否渲染，None表示使用配置文件中的设置
        """
        render_status = render if render is not None else self.config['env_config'].get('use_render', False)
        print(f"\n🧪 Starting Model Test ({num_episodes} episodes, render={render_status})")
        results = [self.test_episode(i, render=render) for i in range(num_episodes)]
        self.print_summary(results)
        return results

    def print_summary(self, results):
        total = len(results)
        if total == 0:
            return

        success = sum(r['success'] for r in results)
        out_of_road = sum(r['out_of_road'] for r in results)
        collision = sum(r['collision'] for r in results)
        avg_steps = np.mean([r['steps'] for r in results])

        # 新增统计信息
        avg_route_completion = np.mean([r['route_completion'] for r in results])
        avg_reward = np.mean([r['reward'] for r in results])
        avg_distance = np.mean([r['distance'] for r in results])

        # 计算超时率（既不成功也不出路也不碰撞的情况）
        timeout = total - success - out_of_road - collision
        timeout_rate = timeout / total

        print("\n" + "="*60)
        print("🏁 Model Test Summary")
        print("="*60)
        print(f"📊 Total Episodes: {total}")
        print(f"🎯 Success Rate: {success/total:.1%}")
        print(f"🛣️ Out of Road Rate: {out_of_road/total:.1%}")
        print(f"💥 Collision Rate: {collision/total:.1%}")
        print(f"👣 Average Steps: {avg_steps:.1f}")
        print(f"🗺️ Average Route Completion: {avg_route_completion:.1f}%")
        print(f"🏆 Average Reward: {avg_reward:.2f}")
        print(f"📏 Average Distance: {avg_distance:.1f}m")

def main():
    """主函数"""
    # model_path = "/home/<USER>/rl/RL-IL/dagger_gnn_transformer/dagger_model/dagger_training_20250706_170135/cur_model.pth"
    # model_path = "dagger_model/20250706_185949/checkpoint.pth"
    # model_path = "dagger_model/20250707_013721/best_model.pth"
    # model_path = "dagger_model/20250707_022312/best_model.pth"
    # model_path = "dagger_model/20250710_233224/best_model.pth"
    # model_path =  "dagger_model/20250712_230857/best_model.pth"
    model_path =  "/home/<USER>/rl/RL-IL/dagger_gnn_transformer/dagger_model/best_model.pth" # 云端好模型
    # model_path =  "/home/<USER>/rl/RL-IL/dagger_model/20250713_003136/checkpoint.pth"
    # model_path = "/home/<USER>/rl/RL-IL/dagger_gnn_transformer/dagger_model/best_modelbeta_0.5.pth"


    # 统一配置：与main.py中的训练配置保持一致
    config = {
        'hidden_dim': 128, 'transformer_dim': 256, 'num_layers': 2,
        'num_transformer_layers': 4, 'num_heads': 8, 'max_vehicles': 10,
        'dropout': 0.1, 'sequence_dim': 72, 'ego_node_dim': 7, 'npc_node_dim': 7,
        'action_dim': 2, 'max_npc_distance': 30, 'expert_type': 'expert',
        'env_config': {
            "out_of_road_done": True,  # 与main.py保持一致
            "use_render": True,       # 默认不渲染，可通过参数覆盖
            "num_scenarios": 100,      # 增加场景数量，支持更多不同的seed
            "traffic_density": 0.1,    # 与main.py保持一致
            "start_seed": 11,           # 与main.py保持一致
            "map": "SS",              # 与main.py保持一致
            "accident_prob": 0.0,
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            }
        }
    }

    if not Path(model_path).exists():
        print(f"❌ Error: Model path not found: {model_path}")
        return False

    try:
        # 启用低速NPC车辆，与训练时保持一致
        tester = ModelTester(model_path, config, use_slow_npc=True)
        # 使用配置文件中的render设置，也可以通过参数覆盖
        # render=None 表示使用配置文件中的设置
        # render=True 表示强制开启渲染
        # render=False 表示强制关闭渲染
        tester.run_test(num_episodes=100, render=None)
        return True
    except Exception as e:
        print(f"\n❌ An error occurred during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    if main():
        print("\n🎉 Model test finished successfully!")
    else:
        print("\n💥 Model test failed!")
        sys.exit(1)
