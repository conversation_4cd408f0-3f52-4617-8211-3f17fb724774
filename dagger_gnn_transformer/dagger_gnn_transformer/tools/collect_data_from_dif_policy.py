from ray.rllib.evaluation.sample_batch_builder import SampleBatchBuilder
from ray.rllib.offline.json_writer import JsonWriter
import os
from metadrive.envs.gym_wrapper import createGymWrapper
from metadrive.envs.metadrive_env import MetaDriveEnv

from metadrive.policy.expert_policy import ExpertPolicy
from metadrive.policy.idm_policy import IDMPolicy

# 创建专门用于NPC车辆的低速IDM策略
class NPCSlowIDMPolicy(IDMPolicy):
    """
    专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
    """
    NORMAL_SPEED = 10  # km/h
    MAX_SPEED = 10  # km/h
    CREEP_SPEED = 10  # km/h
    ACC_FACTOR = 0.5  # 降低加速度因子

    def __init__(self, control_object, random_seed):
        super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
        self.target_speed = self.NORMAL_SPEED

# 创建自定义的低速IDM策略
class SlowIDMPolicy(IDMPolicy):
    """
    自定义的低速IDM策略，将NPC车辆速度降低到10 km/h
    """
    # 将正常速度降低到10 km/h
    NORMAL_SPEED = 15  # km/h

    # 将最大速度也相应降低
    MAX_SPEED = 15  # km/h

    # 降低爬行速度
    CREEP_SPEED = 10  # km/h

    # 可以调整加速度因子使车辆更温和
    ACC_FACTOR = 0.8  # 降低加速度因子

    def __init__(self, control_object, random_seed):
        super(SlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
        # 确保目标速度设置为低速
        self.target_speed = self.NORMAL_SPEED

class DataCollector:

    def __init__(self, arr_episode, drive_policy, use_render, use_slow_traffic=True):
        # 如果启用低速交通，则替换traffic manager中的IDMPolicy
        if use_slow_traffic:
            self._patch_traffic_manager_for_slow_npc()
            print("已将NPC车辆速度降低到10 km/h，自车保持正常速度")

        # self.config = {
        #     'crash_vehicle_done': True,
        #     "use_render": use_render,
        #     'map':'CXO',
        #     # "data_directory": '/home/<USER>/data/scenes/raw_scenes_500' ,# "/home/<USER>/data/scenes/turn_scenes",
        #     "num_scenarios": 1000,
        #     "start_seed":3,
        #     'out_of_route_done': True,
        #     # 'horizon':91,
        #     "agent_policy": drive_policy,  # 直接在这里设置专家策略
        #     "traffic_density": 0.1,
        #     "vehicle_config": {
        #         "lidar": {"num_lasers": 30, "distance": 50, "num_others": 3},
        #         "side_detector": {"num_lasers": 30},
        #         "lane_line_detector": {"num_lasers": 12}
        #     }
        # }
        self.config = {
            "out_of_road_done": True,
            "use_render": True,
            "num_scenarios": 1,
            "traffic_density": 0.1,
            "start_seed": 1,
            "map": "SSSSS",
            "accident_prob": 0.0,
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            },
            "agent_policy": drive_policy,
            # 配置交通车辆使用低速IDM策略
            "traffic_vehicle_config": {
                "show_navi_mark": False,
                "show_dest_mark": False,
                "enable_reverse": False,
                "show_lidar": False,
                "show_lane_line_detector": False,
                "show_side_detector": False,
            },

        }
        
        self.use_render = use_render
        self.env = self._create_env()
        self.batch_builder = SampleBatchBuilder()
        # 确保bc_data目录存在
        os.makedirs('data/bc_data', exist_ok=True)

        self.writer = JsonWriter(os.path.join('data/bc_data', str(arr_episode)), compress_columns=[])
        self.episode = 0
        self.data_buffer = []  # 用于缓存数据的列表
        self.arr_episode = arr_episode
        # 记录成功通过的场景
        self.success_scenes = []

    def _patch_traffic_manager_for_slow_npc(self):
        """
        通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy
        这样只有NPC车辆会使用低速，自车保持正常速度
        """
        import metadrive.manager.traffic_manager as tm

        # 保存原始的add_policy方法
        original_add_policy = tm.PGTrafficManager.add_policy

        def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
            # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
            if policy_class == IDMPolicy:
                policy_class = NPCSlowIDMPolicy
            return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

        # 应用patch
        tm.PGTrafficManager.add_policy = patched_add_policy

    def _create_env(self):
        env = createGymWrapper(MetaDriveEnv)(config=self.config)
        return env

    def collect_data(self):
        obs = self.env.reset()
        obs_list = []
        action_list = []
        arrive_num = 0
        fail_num = 0
        all_step = 0
        arr_step = 0
        reward_total = 0
        while arrive_num < self.arr_episode:
            all_step += 1
            # 直接使用step传入动作
            action = [0.0, 0.0]  # 专家策略会在环境内部被调用
            new_obs, reward, done, info = self.env.step(action)
            reward_total += reward
            obs = new_obs
            obs_list.append(obs)
            # print(info)
            action = info['action']
            
            action_list.append(action)


            if info.get('out_of_road', True):
                fail_num += 1
                obs_list = []
                action_list = []
                print(f"Failed at scene: {self.env.current_seed}")
            if info.get('arrive_dest', True):
                # 记录成功通过的场景
                current_scene = self.env.current_seed
                self.success_scenes.append(current_scene)

                for obs, action in zip(obs_list, action_list):
                    self.batch_builder.add_values(obs=obs, actions=action)
                arrive_num += 1
                arr_step += len(obs_list)
                obs_list.clear()
                action_list.clear()
                success_rate = arrive_num/(arrive_num+fail_num) * 100
                print(f'到达目标! 当前成功率: {success_rate:.2f}%  (成功:{arrive_num} 失败:{fail_num}),arr_step:{arr_step}')
            if done:
                obs = self.env.reset()
            
            if self.use_render:
                self.env.render()
            
            # print(f'all_step:{all_step},arr_step:{arr_step},arrive_num:{arrive_num},fail_num:{fail_num},arrive_rate:{arrive_num/(arrive_num+fail_num+0.1)},reward_total:{reward_total},mean_eposide_reward:{reward_total/(arrive_num+fail_num+0.1)}')
        
        # 打印最终统计信息
        final_success_rate = arrive_num/(arrive_num+fail_num) * 100
        print("\n========== 最终统计 ==========")
        print(f"总成功率: {final_success_rate:.2f}%")
        print(f"总尝试次数: {arrive_num+fail_num}")
        print(f"成功次数: {arrive_num}")
        print(f"失败次数: {fail_num}")
        print("============================\n")
        self.writer.write(self.batch_builder.build_and_reset())

if __name__ == "__main__":
    # 使用IDMPolicy作为专家策略，但通过monkey patch使所有NPC车辆都变成低速
    # use_slow_traffic=True 会自动将所有IDMPolicy的速度降低到10 km/h
    collector = DataCollector(drive_policy=IDMPolicy, use_render=True, arr_episode=112, use_slow_traffic=True)
    collector.collect_data()
