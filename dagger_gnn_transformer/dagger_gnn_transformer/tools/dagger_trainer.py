#!/usr/bin/env python3
"""
DAGGER训练器 - 图神经网络+Transformer架构 (Refactored)
使用DAGGER算法解决分布偏移问题，实时调用专家策略
"""

import os
import sys
import torch
import torch.nn as nn
import numpy as np
import json
import time
from pathlib import Path
from datetime import datetime
from collections import deque
import random
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import threading
import queue
from torch.utils.tensorboard import SummaryWriter
from torch_geometric.data import Data, Batch

# 导入日志抑制工具
from tools.suppress_logs import setup_quiet_mode, quiet_metadrive

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from metadrive import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy
from metadrive.policy.expert_policy import ExpertPolicy as MetaDriveExpertPolicy

# 动态创建NPC车速策略类的全局变量
_NPCSlowIDMPolicy = None
_SlowIDMPolicy = None

def create_npc_speed_policies(npc_speed_config):
    """根据配置动态创建NPC车速策略类"""
    global _NPCSlowIDMPolicy, _SlowIDMPolicy

    # 创建专门用于NPC车辆的低速IDM策略
    class NPCSlowIDMPolicy(IDMPolicy):
        """
        专门用于NPC车辆的低速IDM策略，速度降低到10 km/h
        """
        NORMAL_SPEED = npc_speed_config.get('npc_normal_speed', 10)  # km/h
        MAX_SPEED = npc_speed_config.get('npc_max_speed', 10)  # km/h
        CREEP_SPEED = npc_speed_config.get('npc_creep_speed', 10)  # km/h
        ACC_FACTOR = npc_speed_config.get('npc_acc_factor', 0.5)  # 降低加速度因子

        def __init__(self, control_object, random_seed):
            super(NPCSlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
            self.target_speed = self.NORMAL_SPEED

    # 创建自定义的低速IDM策略
    class SlowIDMPolicy(IDMPolicy):
        """
        自定义的低速IDM策略，将NPC车辆速度降低到10 km/h
        """
        NORMAL_SPEED = npc_speed_config.get('slow_idm_normal_speed', 15)  # km/h
        MAX_SPEED = npc_speed_config.get('slow_idm_max_speed', 15)  # km/h
        CREEP_SPEED = npc_speed_config.get('slow_idm_creep_speed', 10)  # km/h
        ACC_FACTOR = npc_speed_config.get('slow_idm_acc_factor', 0.8)  # 降低加速度因子

        def __init__(self, control_object, random_seed):
            super(SlowIDMPolicy, self).__init__(control_object=control_object, random_seed=random_seed)
            # 确保目标速度设置为低速
            self.target_speed = self.NORMAL_SPEED

    _NPCSlowIDMPolicy = NPCSlowIDMPolicy
    _SlowIDMPolicy = SlowIDMPolicy

    return NPCSlowIDMPolicy, SlowIDMPolicy

def _patch_traffic_manager_for_slow_npc_worker(npc_speed_config):
    """
    在工作进程中应用NPC车速控制猴子补丁
    """
    # 动态创建NPC策略类
    NPCSlowIDMPolicy, _ = create_npc_speed_policies(npc_speed_config)

    import metadrive.manager.traffic_manager as tm

    # 保存原始的add_policy方法
    original_add_policy = tm.PGTrafficManager.add_policy

    def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
        # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
        if policy_class == IDMPolicy:
            policy_class = NPCSlowIDMPolicy
        return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

    # 应用patch
    tm.PGTrafficManager.add_policy = patched_add_policy

# 导入重构后的图神经网络模型
from models.graph_transformer_model import GraphTransformerModel

def normalize_expert_action(action):
    """
    将专家动作归一化到模型输出的相同范围

    Args:
        action: [steering, throttle]

    Returns:
        normalized_action: [steering, throttle] 归一化后的动作
    """
    steering, throttle = action[0], action[1]

    # 对转向进行归一化：限制在[-0.8, 0.8]范围内（与模型输出一致）
    steering_normalized = np.clip(steering, -0.8, 0.8)

    # 对油门进行归一化：限制在[-1, 1]范围内（与模型输出一致）
    throttle_normalized = np.clip(throttle, -1.0, 1.0)

    return np.array([steering_normalized, throttle_normalized], dtype=np.float32)


def get_pure_sensor_data(obs: np.ndarray) -> np.ndarray:
    """从原始88维观测中提取纯72维传感器数据."""
    if not isinstance(obs, np.ndarray):
        obs = np.array(obs).flatten()

    if obs.shape[0] != 88:
        return np.zeros(72, dtype=np.float32)

    # This is the corrected slicing based on the structure of LidarStateObservation
    # The 88-dim vector is composed of: state_obs (58) + lidar (30)
    # The state_obs (58) is composed of: side_detector (30) + ego_state (6) + lane_detector (12) + navi (10)
    side_detector_obs = obs[0:30]
    lane_line_detector_obs = obs[36:48]
    lidar_obs = obs[58:88]

    # Concatenate in the order: [main_lidar, side_detector, lane_detector]
    pure_sensors = np.concatenate([lidar_obs, side_detector_obs, lane_line_detector_obs])
    return pure_sensors.astype(np.float32)

def collect_episodes_continuous_dagger(args):
    """
    真正的DAGGER数据收集函数 - 混合当前策略和IDM专家策略
    """
    env_config, expert_policy_class, beta, max_steps, episode_ids, model_config, model_state_dict = args

    import logging
    logging.getLogger('metadrive').setLevel(logging.ERROR)
    os.environ['METADRIVE_LOG_LEVEL'] = 'ERROR'

    try:
        from metadrive.engine.engine_utils import close_engine
        close_engine()
    except Exception:
        pass

    all_episodes_data = []

    try:
        # 在创建环境之前应用NPC车速控制猴子补丁
        npc_speed_config = model_config.get('npc_speed_config', {})
        if npc_speed_config.get('use_slow_traffic', False):
            _patch_traffic_manager_for_slow_npc_worker(npc_speed_config)

        # 创建环境，不设置agent_policy，我们手动控制
        env_config_copy = env_config.copy()
        # 移除agent_policy，我们将手动控制动作选择
        env_config_copy.pop("agent_policy", None)

        with quiet_metadrive():
            env = MetaDriveEnv(env_config_copy)

        # 如果有模型状态，加载模型用于预测
        model = None
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        # print(f"    🖥️ 数据收集进程使用设备: {device}")
        if model_state_dict is not None:
            from models.graph_transformer_model import GraphTransformerModel
            model = GraphTransformerModel(model_config).to(device)
            model.load_state_dict(model_state_dict)
            model.eval()
            # 确保模型在GPU上
            if torch.cuda.is_available():
                torch.cuda.empty_cache()  # 清理GPU缓存

        # 连续收集多个episodes
        for episode_id in episode_ids:
            try:
                obs, info = env.reset()
                episode_data = []

                # 🔧 关键修复：为每个episode重新创建IDM专家策略
                # 因为env.reset()后agent对象会改变，IDM策略需要绑定到新的agent
                idm_policy = IDMPolicy(env.agent, random_seed=episode_id)
                # print(f"    🤖 为Episode {episode_id}创建新的IDM策略，agent ID: {env.agent.id}")

                for step in range(max_steps):
                    sensor_features = get_pure_sensor_data(obs)

                    ego = env.agent
                    ego_state = np.array([
                        ego.speed_km_h / 120.0, ego.heading_diff(ego.lane), ego.steering,
                        ego.throttle_brake, ego.dist_to_left_side, ego.dist_to_right_side,
                        np.linalg.norm(ego.velocity)
                    ], dtype=np.float32)

                    node_features = [ego_state]
                    max_dist = model_config.get("max_npc_distance", 50)
                    for v_id, v in env.engine.get_objects().items():
                        if v_id != ego.id and hasattr(v, 'position'):
                            rel_pos = ego.position - v.position
                            distance = np.linalg.norm(rel_pos)
                            if distance > max_dist:
                                continue
                            rel_vel = ego.velocity - v.velocity
                            npc_state = np.array([
                                np.linalg.norm(v.velocity), v.heading_diff(v.lane), rel_pos[0],
                                rel_pos[1], rel_vel[0], rel_vel[1], np.linalg.norm(rel_pos)
                            ], dtype=np.float32)
                            node_features.append(npc_state)

                    node_features = np.array(node_features)
                    num_nodes = len(node_features)

                    edge_index = []
                    for i in range(num_nodes):
                        for j in range(i + 1, num_nodes):
                            edge_index.extend([[i, j], [j, i]])

                    if not edge_index and num_nodes > 0:
                        edge_index = [[0, 0]]
                    elif not edge_index and num_nodes == 0:
                        continue

                    graph_data = Data(
                        x=torch.from_numpy(node_features).float(),
                        edge_index=torch.from_numpy(np.array(edge_index).T).long()
                    )

                    # 获取IDM专家动作
                    expert_action = idm_policy.act()
                    expert_action_normalized = normalize_expert_action(expert_action)

                    # 决定使用哪个动作：根据beta概率选择专家动作或模型动作
                    if np.random.random() < beta or model is None:
                        # 使用专家动作
                        action_to_execute = expert_action
                        action_source = "expert"
                    else:
                        # print("use model action")
                        # 使用模型预测动作
                        with torch.no_grad():
                            batch_data = {
                                'graph_batch': Batch.from_data_list([graph_data]).to(device),
                                'sequence_features': torch.from_numpy(sensor_features).float().unsqueeze(0).to(device)
                            }
                            outputs = model(batch_data)
                            model_action = outputs['action_pred'].cpu().numpy()[0]
                            action_to_execute = model_action
                            action_source = "model"

                    # 执行选择的动作
                    obs, reward, terminated, truncated, info = env.step(action_to_execute)

                    # 采集边界距离用于安全损失计算
                    dist_left = getattr(ego, 'dist_to_left_side', 0.0)
                    dist_right = getattr(ego, 'dist_to_right_side', 0.0)

                    # 保存数据：始终使用专家动作作为训练目标
                    step_data = {
                        'graph_data': graph_data,
                        'sensor_features': sensor_features,
                        'action_target': expert_action_normalized,  # 始终使用专家动作作为目标
                        'dist_to_boundaries': np.array([dist_left, dist_right], dtype=np.float32),
                        'action_source': action_source,  # 记录动作来源用于调试
                        'executed_action': normalize_expert_action(action_to_execute)  # 记录实际执行的动作
                    }
                    episode_data.append(step_data)

                    if terminated or truncated:
                        break

                # 将这个episode的数据添加到总数据中
                all_episodes_data.append({
                    'episode_id': episode_id,
                    'data': episode_data,
                    'success': True,
                    'length': len(episode_data)
                })

            except Exception as e:
                print(f"    ❌ Episode {episode_id} 收集失败: {e}")
                # 添加失败的episode记录
                all_episodes_data.append({
                    'episode_id': episode_id,
                    'data': [],
                    'success': False,
                    'error': str(e)
                })

            finally:
                # 每个episode结束后清理内存
                import gc
                gc.collect()

                # 清理GPU缓存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

        env.close()
        return all_episodes_data

    except Exception as e:
        import traceback
        traceback.print_exc()
        # 返回失败的结果，保持与原来格式兼容
        return [{'episode_id': episode_ids[0] if episode_ids else 0, 'data': [], 'success': False, 'error': str(e)}]


def collect_episodes_continuous(args):
    """
    保持向后兼容的旧函数，重定向到新的DAGGER函数
    """
    # 为了向后兼容，添加model_state_dict参数
    if len(args) == 6:
        args = args + (None,)  # 添加model_state_dict=None
    return collect_episodes_continuous_dagger(args)


def collect_episode_parallel(args):
    """
    单个episode收集函数 - 保持向后兼容
    """
    env_config, expert_policy_class, beta, max_steps, episode_id, model_config = args
    # 调用新的连续收集函数，但只收集一个episode
    result = collect_episodes_continuous((env_config, expert_policy_class, beta, max_steps, [episode_id], model_config))
    if result and len(result) > 0:
        return result[0]  # 返回第一个（也是唯一的）episode结果
    else:
        return {'episode_id': episode_id, 'data': [], 'success': False, 'error': 'Collection failed'}


class ExpertPolicy:
    def __init__(self, policy_type="expert"):
        self.policy_type = policy_type
        if policy_type == "idm":
            self.policy_class = IDMPolicy
        elif policy_type == "expert":
            self.policy_class = MetaDriveExpertPolicy
        else:
            raise ValueError(f"Unsupported expert policy type: {policy_type}")
        print(f"✅ ExpertPolicy wrapper initialized with {policy_type}")

    def setup(self, env):
        pass
        # print(f"✅ Expert policy setup complete, MetaDrive will use {self.policy_class.__name__}")


class DaggerTrainer:
    def __init__(self, config):
        self.config = config

        # GPU设置和检查
        if torch.cuda.is_available():
            self.device = torch.device('cuda')
            print(f"🖥️ 使用GPU: {torch.cuda.get_device_name()}")
            print(f"🖥️ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            # 清理GPU缓存
            torch.cuda.empty_cache()
        else:
            self.device = torch.device('cpu')
            print("⚠️ 未检测到GPU，使用CPU训练")

        # 初始化模型并移动到GPU
        self.model = GraphTransformerModel(config).to(self.device)
        print(f"🧠 模型已移动到设备: {self.device}")

        # 优化器和损失函数
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=config['learning_rate'], weight_decay=config['weight_decay'])

        # 学习率调度器配置 - 解决后期loss上升问题
        scheduler_type = config.get('scheduler_type', 'cosine_annealing')
        if scheduler_type == 'cosine_annealing':
            # 余弦退火调度器 - 在训练过程中平滑降低学习率
            self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=config['num_iterations'],
                eta_min=config['learning_rate'] * 0.01  # 最小学习率为初始学习率的1%
            )
        elif scheduler_type == 'step':
            # 阶梯式学习率调度器
            self.scheduler = torch.optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=config.get('scheduler_step_size', 20),
                gamma=config.get('scheduler_gamma', 0.5)
            )
        elif scheduler_type == 'reduce_on_plateau':
            # 基于验证loss的自适应调度器
            self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                mode='min',
                factor=0.5,
                patience=5
            )
        else:
            self.scheduler = None

        self.scheduler_type = scheduler_type
        self.loss_fn = nn.MSELoss().to(self.device)  # 确保损失函数也在GPU上

        self.expert_policy = ExpertPolicy(config['expert_type'])
        self.data_buffer = deque(maxlen=config['buffer_size'])
        self.iteration_losses = []
        self.best_loss = float('inf')
        self.best_route_completion = 0.0  # 新增：跟踪最佳路线完成度

        # NPC车速控制配置
        self.npc_speed_config = config.get('npc_speed_config', {})
        if self.npc_speed_config.get('use_slow_traffic', False):
            # 根据配置动态创建NPC车速策略类
            self.NPCSlowIDMPolicy, self.SlowIDMPolicy = create_npc_speed_policies(self.npc_speed_config)
            self._patch_traffic_manager_for_slow_npc()
            print("✅ 已将NPC车辆速度降低到10 km/h，自车保持正常速度")

        print("✅ DaggerTrainer Initialized (Refactored)")
        print(f"🖥️ Device: {self.device}")
        print(f"🧠 Model Parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        print(f"👨‍🏫 Expert Policy: {config['expert_type']}")
        self.beta_schedule = config.get('beta_schedule', 'conservative')
        self.min_beta = config.get('min_beta', 0.1)
        self.max_beta = config.get('max_beta', 1.0)
        self.use_tensorboard = config.get('use_tensorboard', True)
        self.tensorboard_writer = None
        self.validation_episodes = config.get('validation_episodes', 10)
        self.validation_frequency = config.get('validation_frequency', 3)

    def _patch_traffic_manager_for_slow_npc(self):
        """
        通过monkey patching替换traffic manager中的IDMPolicy为NPCSlowIDMPolicy
        这样只有NPC车辆会使用低速，自车保持正常速度
        """
        import metadrive.manager.traffic_manager as tm

        # 保存原始的add_policy方法
        original_add_policy = tm.PGTrafficManager.add_policy

        # 获取动态创建的NPCSlowIDMPolicy类
        NPCSlowIDMPolicy = self.NPCSlowIDMPolicy

        def patched_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs):
            # 如果是IDMPolicy，替换为NPCSlowIDMPolicy
            if policy_class == IDMPolicy:
                policy_class = NPCSlowIDMPolicy
            return original_add_policy(self, object_id, policy_class, *policy_args, **policy_kwargs)

        # 应用patch
        tm.PGTrafficManager.add_policy = patched_add_policy

    def calculate_beta(self, iteration, total_iterations):
        progress = iteration / max(total_iterations - 1, 1)
        if self.beta_schedule == 'linear':
            beta = self.max_beta - (self.max_beta - self.min_beta) * progress
        else:
            beta = self.max_beta * (1 - progress) 
        return max(self.min_beta, beta)

    def setup_tensorboard(self, save_dir):
        if self.use_tensorboard:
            log_dir = save_dir / "tensorboard" / datetime.now().strftime('%Y%m%d_%H%M%S')
            log_dir.mkdir(parents=True, exist_ok=True)
            self.tensorboard_writer = SummaryWriter(log_dir=str(log_dir))
            print(f"📊 TensorBoard log directory: {log_dir}")

    def log_to_tensorboard(self, metrics, iteration):
        if self.tensorboard_writer:
            for key, value in metrics.items():
                self.tensorboard_writer.add_scalar(key, value, iteration)
            self.tensorboard_writer.flush()

    def get_model_action(self, graph_data, sensor_features):
        self.model.eval()
        with torch.no_grad():
            batch_data = {
                'graph_batch': Batch.from_data_list([graph_data]).to(self.device),
                'sequence_features': torch.from_numpy(sensor_features).float().unsqueeze(0).to(self.device)
            }
            outputs = self.model(batch_data)
            action = outputs['action_pred'].cpu().numpy()[0]
        return action

    def create_batch(self, samples):
        graph_list = [s['graph_data'] for s in samples]
        sensor_features = np.array([s['sensor_features'] for s in samples])
        action_targets = np.array([s['action_target'] for s in samples])
        dist_to_boundaries = np.array([s['dist_to_boundaries'] for s in samples]) # 新增

        graph_batch = Batch.from_data_list(graph_list).to(self.device)
        sensor_features = torch.from_numpy(sensor_features).float().to(self.device)
        action_targets = torch.from_numpy(action_targets).float().to(self.device)
        dist_to_boundaries = torch.from_numpy(dist_to_boundaries).float().to(self.device) # 新增
        # print(f"dist_to_boundaries:{dist_to_boundaries}")
        return {
            'graph_batch': graph_batch,
            'sequence_features': sensor_features,
            'action_targets': action_targets,
            'dist_to_boundaries': dist_to_boundaries # 新增
        }

    def train_on_data(self, training_data, epochs):
        if not training_data:
            return float('inf')

        self.model.train()
        total_loss = 0
        num_batches = 0
        random.shuffle(training_data)
        batch_size = self.config['batch_size']

        # GPU内存管理
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        for epoch in range(epochs):
            epoch_loss = 0
            epoch_batches = 0
            for i in range(0, len(training_data), batch_size):
                batch_samples = training_data[i:i+batch_size]
                if not batch_samples:
                    continue

                # 创建批次数据并确保在GPU上
                batch_data = self.create_batch(batch_samples)

                self.optimizer.zero_grad()

                # 前向传播
                outputs = self.model(batch_data)
                action_pred = outputs['action_pred']

                # 计算模仿损失
                imitation_loss = self.loss_fn(action_pred, batch_data['action_targets'])

                # 计算安全损失
                dists = batch_data['dist_to_boundaries'] # [batch_size, 2]
                safety_threshold = 2.0 # 距离道路边界小于2米开始惩罚
                safety_penalty = torch.sum(torch.relu(safety_threshold - dists), dim=-1) # 对左右距离求和
                safety_loss = torch.mean(safety_penalty) # 对批次求平均

                # 总损失 = 模仿损失 + lambda * 安全损失
                lmbda = 0.01 # 安全损失的权重
                loss = imitation_loss + lmbda * safety_loss

                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()

                epoch_loss += loss.item()
                epoch_batches += 1

                # GPU内存清理
                if torch.cuda.is_available() and (i + 1) % 10 == 0:
                    torch.cuda.empty_cache()

            total_loss += epoch_loss
            num_batches += epoch_batches

        # 训练完成后清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        return total_loss / num_batches if num_batches > 0 else float('inf')

    def run_dagger_parallel(self, num_iterations, episodes_per_iteration, num_workers):
        print("\n🚀 Starting Parallel DAGGER Training (True DAGGER with IDM Expert)")
        save_dir = self.setup_save_directory()
        self.setup_tensorboard(save_dir)
        start_time = time.time()

        for iteration in range(num_iterations):
            beta = self.calculate_beta(iteration, num_iterations)
            print(f"\n--- DAGGER Iteration {iteration + 1}/{num_iterations} | Beta: {beta:.2f} ---")
            print(f"  📊 专家策略使用概率: {beta:.1%}, 当前策略使用概率: {(1-beta):.1%}")

            # 获取当前模型状态用于数据收集
            model_state_dict = self.model.state_dict() if iteration > 0 else None

            # 优化：将episodes分配给workers，每个worker连续收集多个episodes
            episodes_per_worker = episodes_per_iteration // num_workers
            remaining_episodes = episodes_per_iteration % num_workers

            collection_args = []
            episode_start = 0

            for worker_id in range(num_workers):
                # 计算这个worker要收集的episodes数量
                worker_episodes = episodes_per_worker + (1 if worker_id < remaining_episodes else 0)
                if worker_episodes > 0:
                    episode_ids = list(range(episode_start, episode_start + worker_episodes))
                    collection_args.append((
                        self.config['env_config'],
                        self.expert_policy.policy_class,  # 这里传递IDMPolicy类
                        beta,
                        self.config['max_steps_per_episode'],
                        episode_ids,
                        self.config,
                        model_state_dict  # 传递模型状态
                    ))
                    episode_start += worker_episodes

            print(f"  🔄 Collecting {episodes_per_iteration} episodes with {len(collection_args)} workers (True DAGGER mode)...")
            iteration_data = []
            expert_action_count = 0
            model_action_count = 0

            with ProcessPoolExecutor(max_workers=num_workers) as executor:
                futures = [executor.submit(collect_episodes_continuous_dagger, args) for args in collection_args]
                for future in as_completed(futures):
                    try:
                        worker_results = future.result()
                        for result in worker_results:
                            if result['success']:
                                episode_data = result['data']
                                iteration_data.extend(episode_data)

                                # 统计动作来源
                                for step_data in episode_data:
                                    if step_data.get('action_source') == 'expert':
                                        expert_action_count += 1
                                    else:
                                        model_action_count += 1

                                # print(f"    ✅ Episode {result['episode_id'] + 1}: 收集了{result['length']}步")
                            else:
                                print(f"    ❌ Episode {result.get('episode_id', '?') + 1}: 失败 - {result.get('error', 'Unknown error')}")

                        # 及时清理worker结果，释放内存
                        del worker_results

                    except Exception as e:
                        print(f"    ❌ Worker进程异常: {e}")
                        continue

                    finally:
                        # 强制垃圾回收
                        import gc
                        gc.collect()

                        # 清理GPU缓存
                        if torch.cuda.is_available():
                            torch.cuda.empty_cache()

            total_actions = expert_action_count + model_action_count
            if total_actions > 0:
                expert_ratio = expert_action_count / total_actions
                model_ratio = model_action_count / total_actions
                print(f"  📊 动作统计: 专家动作 {expert_action_count} ({expert_ratio:.1%}), 模型动作 {model_action_count} ({model_ratio:.1%})")

            # print(f"  📦 Collected {len(iteration_data)} samples this iteration.")
            self.data_buffer.extend(iteration_data)
            print(f"  💾 Buffer size: {len(self.data_buffer)}")

            # 及时清理iteration_data，释放内存
            del iteration_data
            import gc
            gc.collect()

            if len(self.data_buffer) >= self.config['batch_size']:
                print("  🧠 Training model...")
                avg_loss = self.train_on_data(list(self.data_buffer), self.config['training_epochs'])
                print(f"    Loss: {avg_loss:.4f}")

                # 学习率调度器更新
                current_lr = self.optimizer.param_groups[0]['lr']
                if self.scheduler is not None:
                    if self.scheduler_type == 'reduce_on_plateau':
                        # 基于loss的调度器需要传入loss值
                        self.scheduler.step(avg_loss)
                    else:
                        # 其他调度器按迭代次数更新
                        self.scheduler.step()

                new_lr = self.optimizer.param_groups[0]['lr']
                if new_lr != current_lr:
                    print(f"    📉 Learning rate updated: {current_lr:.2e} -> {new_lr:.2e}")

                self.iteration_losses.append(avg_loss)
                self.log_to_tensorboard({
                    'Training/Loss': avg_loss,
                    'Training/Beta': beta,
                    'Training/Learning_Rate': new_lr,
                    'Training/Expert_Action_Ratio': expert_ratio if total_actions > 0 else 0,
                    'Training/Model_Action_Ratio': model_ratio if total_actions > 0 else 0
                }, iteration)

                # 保存检查点模型
                if (iteration + 1) % 10 == 0 or iteration == num_iterations - 1:
                    self.save_model(save_dir / "checkpoint.pth", iteration, avg_loss, "checkpoint")

                # 训练后清理GPU缓存
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
            else:
                print("  ⚠️ Not enough data to train, skipping.")

            # 每次迭代结束后强制垃圾回收
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 验证模型并根据路线完成度更新最佳模型
            if (iteration + 1) % self.validation_frequency == 0:
                validation_results = self.validate_model(iteration, save_dir)
                if validation_results:
                    route_completion = validation_results['mean_route_completion']
                    # 基于路线完成度判断最佳模型
                    if route_completion > self.best_route_completion:
                        self.best_route_completion = route_completion
                        self.save_model(save_dir / "best_model.pth", iteration, avg_loss if 'avg_loss' in locals() else 0.0, "best")
                        print(f"    🏆 新的最佳模型! 路线完成度: {route_completion:.1f}%")

        total_time = time.time() - start_time
        self.save_model(save_dir / "final_model.pth", num_iterations, self.iteration_losses[-1] if self.iteration_losses else float('inf'), "final")
        print(f"\n🎉 True DAGGER Training finished in {total_time:.2f}s. Best route completion: {self.best_route_completion:.1f}%")
        return str(save_dir / "best_model.pth")

    def validate_model(self, iteration, save_dir):
        """验证模型性能，返回验证指标"""
        print("  🧪 Validating model...")

        # 创建验证环境配置
        validation_config = self.config['env_config'].copy()
        # validation_config["use_render"] =True

        num_episodes = self.validation_episodes
        episode_rewards = []
        episode_lengths = []
        episode_distances = []
        episode_route_completions = []
        success_count = 0
        collision_count = 0
        out_of_road_count = 0
        timeout_count = 0

        try:
            for episode in range(num_episodes):
                # 创建验证环境
                env = MetaDriveEnv(validation_config)
                obs, info = env.reset()

                episode_reward = 0
                episode_length = 0
                episode_distance = 0
                success = False
                collision = False
                out_of_road = False
                timeout = False

                # 获取初始位置用于计算距离
                if hasattr(env.agent, 'position'):
                    initial_position = np.array([env.agent.position[0], env.agent.position[1]])
                else:
                    initial_position = np.array([0, 0])

                for step in range(self.config.get('max_steps_per_episode', 1000)):
                    # 使用模型预测动作
                    with torch.no_grad():
                        # 设置模型为评估模式
                        self.model.eval()

                        # 获取传感器数据
                        sensor_features = get_pure_sensor_data(obs)

                        # 构建图数据
                        ego = env.agent
                        ego_state = np.array([
                            ego.speed_km_h / 120.0, ego.heading_diff(ego.lane), ego.steering,
                            ego.throttle_brake, ego.dist_to_left_side, ego.dist_to_right_side,
                            np.linalg.norm(ego.velocity)
                        ], dtype=np.float32)

                        node_features = [ego_state]
                        max_dist = self.config.get("max_npc_distance", 50)
                        for v_id, v in env.engine.get_objects().items():
                            if v_id != ego.id and hasattr(v, 'position'):
                                rel_pos = ego.position - v.position
                                distance = np.linalg.norm(rel_pos)
                                if distance > max_dist:
                                    continue
                                rel_vel = ego.velocity - v.velocity
                                npc_state = np.array([
                                    np.linalg.norm(v.velocity), v.heading_diff(v.lane), rel_pos[0],
                                    rel_pos[1], rel_vel[0], rel_vel[1], np.linalg.norm(rel_pos)
                                ], dtype=np.float32)
                                node_features.append(npc_state)

                        node_features = np.array(node_features)
                        num_nodes = len(node_features)

                        edge_index = []
                        for i in range(num_nodes):
                            for j in range(i + 1, num_nodes):
                                edge_index.extend([[i, j], [j, i]])

                        if not edge_index and num_nodes > 0:
                            edge_index = [[0, 0]]
                        elif not edge_index and num_nodes == 0:
                            break

                        # 创建图数据（使用与训练时相同的格式）
                        graph_data = Data(
                            x=torch.from_numpy(node_features).float(),
                            edge_index=torch.from_numpy(np.array(edge_index).T).long()
                        )

                        # 创建批次并确保在GPU上
                        graph_batch = Batch.from_data_list([graph_data]).to(self.device)
                        sensor_tensor = torch.from_numpy(sensor_features).float().unsqueeze(0).to(self.device)

                        batch_data = {
                            'graph_batch': graph_batch,
                            'sequence_features': sensor_tensor
                        }

                        # 模型预测（在GPU上）
                        outputs = self.model(batch_data)
                        action = outputs['action_pred'].cpu().numpy().flatten()

                        # 清理GPU缓存
                        if torch.cuda.is_available():
                            del graph_batch, sensor_tensor, outputs
                            torch.cuda.empty_cache()

                    # 执行动作
                    obs, reward, terminated, truncated, info = env.step(action)
                    episode_reward += reward
                    episode_length += 1

                    # 更新距离
                    if hasattr(env.agent, 'position'):
                        current_position = np.array([env.agent.position[0], env.agent.position[1]])
                        episode_distance = np.linalg.norm(current_position - initial_position)

                    if terminated or truncated:
                        # 检查终止原因
                        if info.get('arrive_dest', False):
                            success = True
                        elif info.get('crash', False) or info.get('crash_vehicle', False) or info.get('crash_object', False):
                            collision = True
                        elif info.get('out_of_road', False):
                            out_of_road = True
                        else:
                            timeout = True  # 其他情况视为超时
                        break

                # 计算路线完成度（基于距离和奖励）
                route_completion = 0.0
                
                route_completion = getattr(env.agent.navigation, 'route_completion', 0.0) * 100

                episode_rewards.append(episode_reward)
                episode_lengths.append(episode_length)
                episode_distances.append(episode_distance)
                episode_route_completions.append(route_completion)

                # 更新计数器
                if success:
                    success_count += 1
                if collision:
                    collision_count += 1
                if out_of_road:
                    out_of_road_count += 1
                if timeout:
                    timeout_count += 1

                env.close()

            # 计算验证指标
            mean_reward = np.mean(episode_rewards)
            mean_length = np.mean(episode_lengths)
            mean_distance = np.mean(episode_distances)
            mean_route_completion = np.mean(episode_route_completions)
            success_rate = success_count / num_episodes
            collision_rate = collision_count / num_episodes
            out_of_road_rate = out_of_road_count / num_episodes
            timeout_rate = timeout_count / num_episodes

            # 记录到TensorBoard
            validation_metrics = {
                'Validation/Mean_Reward': mean_reward,
                'Validation/Mean_Length': mean_length,
                'Validation/Mean_Distance': mean_distance,
                'Validation/Route_Completion': mean_route_completion,
                'Validation/Success_Rate': success_rate,
                'Validation/Collision_Rate': collision_rate,
                'Validation/Out_of_Road_Rate': out_of_road_rate,
                'Validation/Timeout_Rate': timeout_rate,
            }
            self.log_to_tensorboard(validation_metrics, iteration)

            print("    📊 验证结果:")
            print(f"      平均奖励: {mean_reward:.3f}")
            print(f"      平均长度: {mean_length:.1f}")
            print(f"      平均距离: {mean_distance:.1f}m")
            print(f"      路线完成度: {mean_route_completion:.1f}%")
            print(f"      成功率: {success_rate:.2%}")
            print(f"      碰撞率: {collision_rate:.2%}")
            print(f"      出路率: {out_of_road_rate:.2%}")
            print(f"      超时率: {timeout_rate:.2%}")

            # 返回验证指标
            return {
                'mean_reward': mean_reward,
                'mean_length': mean_length,
                'mean_distance': mean_distance,
                'mean_route_completion': mean_route_completion,
                'success_rate': success_rate,
                'collision_rate': collision_rate,
                'out_of_road_rate': out_of_road_rate,
                'timeout_rate': timeout_rate,
            }

        except Exception as e:
            print(f"    ❌ 验证失败: {e}")
            import traceback
            traceback.print_exc()
            # 验证失败时返回默认值
            return {
                'mean_reward': 0.0,
                'mean_length': 0.0,
                'mean_distance': 0.0,
                'mean_route_completion': 0.0,
                'success_rate': 0.0,
                'collision_rate': 1.0,
                'out_of_road_rate': 1.0,
                'timeout_rate': 1.0,
            }

    def save_model(self, path, iteration, loss, model_type="checkpoint"):
        path.parent.mkdir(parents=True, exist_ok=True)
        save_data = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'iteration': iteration,
            'loss': loss,
            'config': self.config,
        }
        torch.save(save_data, path)
        print(f"    💾 {model_type.capitalize()} model saved to {path}")

    def setup_save_directory(self, base_dir="./dagger_model"):
        save_dir = Path(base_dir) / datetime.now().strftime('%Y%m%d_%H%M%S')
        save_dir.mkdir(parents=True, exist_ok=True)
        print(f"📁 Training directory: {save_dir}")
        return save_dir