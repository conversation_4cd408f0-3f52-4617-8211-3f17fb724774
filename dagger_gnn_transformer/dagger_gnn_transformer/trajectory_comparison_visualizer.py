#!/usr/bin/env python3
"""
轨迹对比可视化工具
展示自车轨迹vs专家轨迹，包含超车变道等复杂场景
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyArrowPatch, Circle
import matplotlib.patheffects as path_effects
from pathlib import Path
from datetime import datetime
import seaborn as sns

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_top_down_visualizer import patch_traffic_manager_for_slow_npc, get_pure_sensor_data
from tools.dagger_trainer import ExpertPolicy
from models.graph_transformer_model import GraphTransformerModel
from metadrive import MetaDriveEnv
from torch_geometric.data import Data, Batch

# 设置高质量绘图
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman', 'DejaVu Serif']
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

class TrajectoryComparisonVisualizer:
    """轨迹对比可视化器"""
    
    def __init__(self, model_path, config, output_dir="trajectory_comparison"):
        """
        初始化轨迹对比可视化器
        
        Args:
            model_path: 模型路径
            config: 配置字典
            output_dir: 输出目录
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.config = config
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 应用低速NPC补丁
        patch_traffic_manager_for_slow_npc()
        
        # 加载模型
        self.model = GraphTransformerModel(config).to(self.device)
        checkpoint = torch.load(model_path, map_location=self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        # 初始化专家策略
        self.expert_policy = ExpertPolicy(config['expert_type'])
        
        # 颜色方案
        self.colors = {
            'background': '#FFFFFF',
            'road': '#F5F5F5',
            'lane_line': '#CCCCCC',
            'model_trajectory': '#2E86AB',  # 蓝色 - 模型轨迹
            'expert_trajectory': '#C73E1D',  # 红色 - 专家轨迹
            'ego_vehicle': '#2E86AB',
            'npc_vehicle': '#FF6B6B',
            'npc_trajectory': '#FFE66D',  # 黄色 - NPC轨迹
            'arrow_throttle': '#28A745',
            'arrow_brake': '#DC3545',
            'arrow_steering': '#6F42C1',
            'text': '#000000'
        }
        
        print("✅ 轨迹对比可视化器初始化完成")
        
    def _get_model_input(self, obs, env):
        """从环境状态创建模型输入"""
        sensor_features = get_pure_sensor_data(obs)
        
        ego = env.agent
        ego_state = np.array([
            ego.speed_km_h / 120.0, ego.heading_diff(ego.lane), ego.steering,
            ego.throttle_brake, ego.dist_to_left_side, ego.dist_to_right_side,
            np.linalg.norm(ego.velocity)
        ], dtype=np.float32)
        
        node_features = [ego_state]
        max_dist = self.config.get("max_npc_distance", 50)
        for v_id, v in env.engine.get_objects().items():
            if v_id != ego.id and hasattr(v, 'position'):
                rel_pos = ego.position - v.position
                distance = np.linalg.norm(rel_pos)
                if distance > max_dist:
                    continue
                rel_vel = ego.velocity - v.velocity
                npc_state = np.array([
                    np.linalg.norm(v.velocity), v.heading_diff(v.lane), rel_pos[0],
                    rel_pos[1], rel_vel[0], rel_vel[1], np.linalg.norm(rel_pos)
                ], dtype=np.float32)
                node_features.append(npc_state)
        
        node_features = np.array(node_features)
        num_nodes = len(node_features)
        edge_index = []
        for i in range(num_nodes):
            for j in range(i + 1, num_nodes):
                edge_index.extend([[i, j], [j, i]])
        
        if not edge_index and num_nodes > 0:
            edge_index = [[0, 0]]
        
        graph_data = Data(
            x=torch.from_numpy(node_features).float(),
            edge_index=torch.from_numpy(np.array(edge_index).T).long()
        )
        
        # 获取边界距离
        dist_left = env.agent.dist_to_left_side
        dist_right = env.agent.dist_to_right_side
        dist_to_boundaries = np.array([dist_left, dist_right], dtype=np.float32)
        
        return graph_data, sensor_features, dist_to_boundaries
    
    def collect_dual_trajectories(self, episode_steps=150, seed=10):
        """
        收集模型轨迹和专家轨迹
        
        Args:
            episode_steps: 运行步数
            seed: 随机种子
        """
        print(f"\n🎯 收集双轨迹数据 (步数: {episode_steps})")
        
        # 收集模型轨迹
        print("📊 收集模型轨迹...")
        model_data = self._collect_single_trajectory(episode_steps, seed, use_model=True)
        
        # 收集专家轨迹
        print("📊 收集专家轨迹...")
        expert_data = self._collect_single_trajectory(episode_steps, seed, use_model=False)
        
        return model_data, expert_data
    
    def _collect_single_trajectory(self, episode_steps, seed, use_model=True):
        """收集单条轨迹"""
        env_config = self.config['env_config'].copy()
        env_config['use_render'] = False  # 关闭可视化
        # 增加交通密度以创造超车场景
        
        env = MetaDriveEnv(env_config)
        self.expert_policy.setup(env)
        
        # 重置环境 - 使用与test_model.py相同的seed计算方式
        start_seed = env_config.get('start_seed', 10)
        num_scenarios = env_config.get('num_scenarios', 10)
        # 每个episode使用不同的seed，如果超出范围则循环使用
        episode_seed = start_seed + (seed % num_scenarios)
        obs, info = env.reset(seed=episode_seed)
        print(f"  使用seed: {episode_seed} (start_seed={start_seed}, offset={seed % num_scenarios})")
        
        trajectory_data = {
            'ego_positions': [],
            'ego_headings': [],
            'ego_speeds': [],
            'actions': [],
            'npc_data': [],  # 存储每一步的NPC信息
            'rewards': [],
            'step_info': []
        }
        
        for step in range(episode_steps):
            # 记录当前状态
            ego_pos = [env.agent.position[0], env.agent.position[1]]
            ego_heading = env.agent.heading_theta
            ego_speed = env.agent.speed_km_h
            
            trajectory_data['ego_positions'].append(ego_pos)
            trajectory_data['ego_headings'].append(ego_heading)
            trajectory_data['ego_speeds'].append(ego_speed)
            
            # 记录NPC车辆信息
            npc_step_data = []
            for v_id, vehicle in env.engine.get_objects().items():
                if v_id != env.agent.id and hasattr(vehicle, 'position'):
                    distance = np.linalg.norm(np.array(ego_pos) - np.array([vehicle.position[0], vehicle.position[1]]))
                    if distance < 80:  # 记录80米范围内的NPC
                        npc_step_data.append({
                            'id': v_id,
                            'position': [vehicle.position[0], vehicle.position[1]],
                            'heading': vehicle.heading_theta,
                            'speed': getattr(vehicle, 'speed_km_h', 0)
                        })
            trajectory_data['npc_data'].append(npc_step_data)
            
            # 获取动作
            if use_model:
                # 模型动作
                graph_data, sensor_features, dist_to_boundaries = self._get_model_input(obs, env)
                with torch.no_grad():
                    batch = {
                        'graph_batch': Batch.from_data_list([graph_data]).to(self.device),
                        'sequence_features': torch.from_numpy(sensor_features).float().unsqueeze(0).to(self.device),
                        'dist_to_boundaries': torch.from_numpy(dist_to_boundaries).float().unsqueeze(0).to(self.device)
                    }
                    action = self.model(batch)['action_pred'].cpu().numpy()[0]

                trajectory_data['actions'].append(action)

                # 执行模型动作
                obs, reward, terminated, truncated, info = env.step(action)
            else:
                # 专家轨迹 - 使用IDM策略作为专家
                from metadrive.policy.idm_policy import IDMPolicy

                # 创建IDM专家策略（如果还没有创建）
                if not hasattr(self, 'idm_policy') or self.idm_policy is None:
                    self.idm_policy = IDMPolicy(env.agent, 0)

                # 获取IDM专家动作
                expert_action = self.idm_policy.act()
                action = [expert_action[0], expert_action[1]]  # [steering, throttle]

                trajectory_data['actions'].append(action)

                # 执行专家动作
                obs, reward, terminated, truncated, info = env.step(action)
            
            trajectory_data['rewards'].append(reward)
            trajectory_data['step_info'].append({
                'terminated': terminated,
                'truncated': truncated,
                'arrive_dest': info.get('arrive_dest', False),
                'out_of_road': info.get('out_of_road', False),
                'crash': info.get('crash', False)
            })
            
            # 检查终止条件
            if terminated or truncated or info.get('arrive_dest', False):
                print(f"  {'模型' if use_model else '专家'}轨迹在第{step+1}步完成")
                break
            if info.get('out_of_road', False) or info.get('crash', False):
                print(f"  {'模型' if use_model else '专家'}轨迹在第{step+1}步失败")
                break
        
        env.close()
        return trajectory_data
    
    def create_trajectory_comparison_visualization(self, model_data, expert_data, 
                                                 focus_range=None, save_path=None):
        """
        创建轨迹对比可视化
        
        Args:
            model_data: 模型轨迹数据
            expert_data: 专家轨迹数据
            focus_range: 聚焦范围 [start_step, end_step]
            save_path: 保存路径
        """
        # 创建大尺寸图像
        fig, ax = plt.subplots(1, 1, figsize=(16, 10))
        ax.set_facecolor(self.colors['background'])
        
        # 获取轨迹数据
        model_positions = np.array(model_data['ego_positions'])
        expert_positions = np.array(expert_data['ego_positions'])
        
        # 确定聚焦范围
        if focus_range is None:
            # 自动选择有趣的区间（轨迹差异较大的部分）
            focus_range = self._find_interesting_range(model_positions, expert_positions)
        
        start_step, end_step = focus_range
        print(f"🔍 聚焦范围: 步骤 {start_step} - {end_step}")
        
        # 获取聚焦区域的数据
        model_focus = model_positions[start_step:end_step+1]
        expert_focus = expert_positions[start_step:end_step+1]
        
        # 设置视图范围
        all_positions = np.vstack([model_focus, expert_focus])
        x_min, x_max = all_positions[:, 0].min() - 20, all_positions[:, 0].max() + 20
        y_min, y_max = all_positions[:, 1].min() - 15, all_positions[:, 1].max() + 15
        
        ax.set_xlim(x_min, x_max)
        ax.set_ylim(y_min, y_max)
        
        # 绘制道路环境
        self._draw_road_environment(ax, all_positions.mean(axis=0), x_max - x_min, y_max - y_min)
        
        # 绘制NPC车辆轨迹（使用模型数据中的NPC信息）
        self._draw_npc_trajectories(ax, model_data['npc_data'], start_step, end_step)
        
        # 绘制主要轨迹
        self._draw_trajectory_with_arrows(ax, model_focus, model_data['actions'][start_step:end_step+1], 
                                        self.colors['model_trajectory'], 'Model Trajectory', 'model')
        self._draw_trajectory_with_arrows(ax, expert_focus, expert_data['actions'][start_step:end_step+1], 
                                        self.colors['expert_trajectory'], 'Expert Trajectory', 'expert')
        
        # 绘制关键点标注
        self._add_key_points_annotation(ax, model_focus, expert_focus, start_step)
        
        # 设置图像属性
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3, color=self.colors['text'], linewidth=0.5)
        ax.set_xlabel('X Position (m)', fontsize=14, fontweight='bold')
        ax.set_ylabel('Y Position (m)', fontsize=14, fontweight='bold')
        ax.set_title('Model vs Expert Trajectory Comparison\n(Overtaking and Lane Change Scenario)', 
                    fontsize=16, fontweight='bold')
        
        # 添加图例
        self._add_comprehensive_legend(ax)
        
        # 添加统计信息
        self._add_trajectory_statistics(ax, model_data, expert_data, focus_range)
        
        plt.tight_layout()
        
        # 保存图像
        if save_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_path = Path(save_path).with_suffix('')
            
            plt.savefig(f"{base_path}_{timestamp}.pdf", format='pdf', bbox_inches='tight', dpi=300)
            plt.savefig(f"{base_path}_{timestamp}.png", format='png', bbox_inches='tight', dpi=600)
            plt.savefig(f"{base_path}_{timestamp}.svg", format='svg', bbox_inches='tight', dpi=300)
            
            print(f"📸 轨迹对比图已保存:")
            print(f"  - PDF: {base_path}_{timestamp}.pdf")
            print(f"  - PNG: {base_path}_{timestamp}.png")
            print(f"  - SVG: {base_path}_{timestamp}.svg")
        
        return fig, ax
    
    def _find_interesting_range(self, model_pos, expert_pos, window_size=50):
        """找到轨迹差异最大的有趣区间"""
        min_len = min(len(model_pos), len(expert_pos))
        if min_len < window_size:
            return [0, min_len-1]
        
        max_diff = 0
        best_start = 0
        
        for start in range(min_len - window_size):
            end = start + window_size
            model_segment = model_pos[start:end]
            expert_segment = expert_pos[start:end]
            
            # 计算轨迹差异
            diff = np.mean(np.linalg.norm(model_segment - expert_segment, axis=1))
            
            if diff > max_diff:
                max_diff = diff
                best_start = start
        
        return [best_start, best_start + window_size]
    
    def _draw_road_environment(self, ax, center_pos, width, height):
        """绘制道路环境"""
        x_center, y_center = center_pos
        
        # 绘制道路背景
        road_width = max(12, height * 0.8)  # 自适应道路宽度
        road = patches.Rectangle(
            (x_center - width/2, y_center - road_width/2),
            width, road_width,
            facecolor=self.colors['road'],
            edgecolor=self.colors['lane_line'],
            linewidth=2,
            alpha=0.8,
            zorder=1
        )
        ax.add_patch(road)
        
        # 绘制车道线
        lane_positions = [-road_width/3, 0, road_width/3]
        for lane_y in lane_positions:
            y_pos = y_center + lane_y
            # 虚线车道线
            x_positions = np.arange(x_center - width/2, x_center + width/2, 3)
            for i, x_pos in enumerate(x_positions):
                if i % 2 == 0:  # 虚线效果
                    line = patches.Rectangle(
                        (x_pos, y_pos - 0.1),
                        2, 0.2,
                        facecolor=self.colors['lane_line'],
                        edgecolor='none',
                        alpha=0.8,
                        zorder=2
                    )
                    ax.add_patch(line)

    def _draw_npc_trajectories(self, ax, npc_data, start_step, end_step):
        """绘制NPC车辆轨迹"""
        # 收集NPC轨迹
        npc_trajectories = {}

        for step in range(start_step, min(end_step+1, len(npc_data))):
            for npc in npc_data[step]:
                npc_id = npc['id']
                if npc_id not in npc_trajectories:
                    npc_trajectories[npc_id] = []
                npc_trajectories[npc_id].append({
                    'step': step,
                    'position': npc['position'],
                    'heading': npc['heading'],
                    'speed': npc['speed']
                })

        # 绘制NPC轨迹和车辆
        for npc_id, trajectory in npc_trajectories.items():
            if len(trajectory) < 5:  # 跳过太短的轨迹
                continue

            positions = np.array([point['position'] for point in trajectory])

            # 绘制NPC轨迹线
            ax.plot(positions[:, 0], positions[:, 1],
                   color=self.colors['npc_trajectory'],
                   linewidth=2, alpha=0.6, linestyle='--',
                   zorder=3, label='NPC Trajectory' if npc_id == list(npc_trajectories.keys())[0] else "")

            # 绘制NPC车辆（最后位置）
            last_pos = trajectory[-1]['position']
            last_heading = trajectory[-1]['heading']

            self._draw_vehicle(ax, last_pos, last_heading, 'npc', scale=0.8)

    def _draw_trajectory_with_arrows(self, ax, positions, actions, color, label, traj_type):
        """绘制带箭头的轨迹"""
        if len(positions) < 2:
            return

        # 绘制轨迹线
        ax.plot(positions[:, 0], positions[:, 1],
               color=color, linewidth=4, alpha=0.8,
               zorder=5, label=label)

        # 在轨迹上添加方向箭头（每隔几个点）
        arrow_interval = max(1, len(positions) // 8)  # 大约8个箭头

        for i in range(0, len(positions)-1, arrow_interval):
            if i + 1 < len(positions):
                start_pos = positions[i]
                end_pos = positions[i + 1]

                # 计算方向
                direction = end_pos - start_pos
                if np.linalg.norm(direction) > 0.1:  # 避免零向量
                    direction = direction / np.linalg.norm(direction)

                    # 创建方向箭头
                    arrow_end = start_pos + direction * 2.0
                    arrow = FancyArrowPatch(
                        start_pos, arrow_end,
                        arrowstyle='-|>',
                        mutation_scale=15,
                        linewidth=2,
                        color=color,
                        alpha=0.9,
                        zorder=6
                    )
                    ax.add_patch(arrow)

        # 绘制起点和终点
        ax.scatter(positions[0, 0], positions[0, 1],
                  s=100, c='green', marker='o',
                  edgecolors='black', linewidth=2,
                  zorder=7, label=f'{traj_type.title()} Start' if traj_type == 'model' else "")

        ax.scatter(positions[-1, 0], positions[-1, 1],
                  s=100, c='red', marker='s',
                  edgecolors='black', linewidth=2,
                  zorder=7, label=f'{traj_type.title()} End' if traj_type == 'model' else "")

        # 在终点绘制车辆
        if len(positions) > 0:
            last_pos = positions[-1]
            # 计算最后的朝向
            if len(positions) > 1:
                direction = positions[-1] - positions[-2]
                heading = np.arctan2(direction[1], direction[0])
            else:
                heading = 0

            vehicle_color = self.colors['ego_vehicle'] if traj_type == 'model' else self.colors['expert_trajectory']
            self._draw_vehicle(ax, last_pos, heading, 'ego', color=vehicle_color)

    def _draw_vehicle(self, ax, position, heading, vehicle_type='ego', scale=1.0, color=None):
        """绘制车辆"""
        x, y = position

        # 车辆尺寸
        length = 4.5 * scale
        width = 1.8 * scale

        # 选择颜色
        if color is None:
            if vehicle_type == 'ego':
                color = self.colors['ego_vehicle']
                edge_color = 'black'
                linewidth = 2
            else:
                color = self.colors['npc_vehicle']
                edge_color = 'darkred'
                linewidth = 1.5
        else:
            edge_color = 'black'
            linewidth = 2

        # 创建车辆矩形
        vehicle = patches.Rectangle(
            (-length/2, -width/2), length, width,
            facecolor=color,
            edgecolor=edge_color,
            linewidth=linewidth,
            alpha=0.9,
            zorder=8
        )

        # 应用旋转和平移变换
        t = plt.matplotlib.transforms.Affine2D().rotate(heading).translate(x, y) + ax.transData
        vehicle.set_transform(t)
        ax.add_patch(vehicle)

        # 添加方向指示（车头）
        front_x = x + (length/2 + 0.5) * np.cos(heading) * scale
        front_y = y + (length/2 + 0.5) * np.sin(heading) * scale

        direction_marker = Circle(
            (front_x, front_y), 0.4 * scale,
            facecolor='white',
            edgecolor=edge_color,
            linewidth=linewidth,
            zorder=9
        )
        ax.add_patch(direction_marker)

    def _add_key_points_annotation(self, ax, model_pos, expert_pos, start_step):
        """添加关键点标注"""
        # 找到轨迹差异最大的点
        if len(model_pos) != len(expert_pos):
            min_len = min(len(model_pos), len(expert_pos))
            model_pos = model_pos[:min_len]
            expert_pos = expert_pos[:min_len]

        differences = np.linalg.norm(model_pos - expert_pos, axis=1)
        max_diff_idx = np.argmax(differences)

        if differences[max_diff_idx] > 1.0:  # 只有差异足够大时才标注
            model_point = model_pos[max_diff_idx]
            expert_point = expert_pos[max_diff_idx]

            # 标注最大差异点
            ax.annotate(f'Max Difference\nStep {start_step + max_diff_idx}\nΔ={differences[max_diff_idx]:.1f}m',
                       xy=model_point, xytext=(model_point[0] + 5, model_point[1] + 5),
                       fontsize=10, ha='left',
                       bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.8),
                       arrowprops=dict(arrowstyle='->', color='black', lw=1.5),
                       zorder=10)

            # 绘制差异连线
            ax.plot([model_point[0], expert_point[0]],
                   [model_point[1], expert_point[1]],
                   'k--', linewidth=2, alpha=0.7, zorder=4)

    def _add_comprehensive_legend(self, ax):
        """添加综合图例"""
        # 创建图例元素
        legend_elements = [
            plt.Line2D([0], [0], color=self.colors['model_trajectory'], lw=4, label='Model Trajectory'),
            plt.Line2D([0], [0], color=self.colors['expert_trajectory'], lw=4, label='Expert Trajectory'),
            plt.Line2D([0], [0], color=self.colors['npc_trajectory'], lw=2, linestyle='--', label='NPC Trajectory'),
            plt.scatter([], [], c='green', s=100, marker='o', label='Start Point'),
            plt.scatter([], [], c='red', s=100, marker='s', label='End Point'),
            patches.Rectangle((0, 0), 1, 1, facecolor=self.colors['ego_vehicle'], label='Ego Vehicle'),
            patches.Rectangle((0, 0), 1, 1, facecolor=self.colors['npc_vehicle'], label='NPC Vehicle')
        ]

        ax.legend(handles=legend_elements, loc='upper right',
                 fontsize=11, framealpha=0.9, fancybox=True, shadow=True)

    def _add_trajectory_statistics(self, ax, model_data, expert_data, focus_range):
        """添加轨迹统计信息"""
        start_step, end_step = focus_range

        # 计算统计信息
        model_positions = np.array(model_data['ego_positions'][start_step:end_step+1])
        expert_positions = np.array(expert_data['ego_positions'][start_step:end_step+1])

        # 轨迹长度
        model_distance = np.sum(np.linalg.norm(np.diff(model_positions, axis=0), axis=1))
        expert_distance = np.sum(np.linalg.norm(np.diff(expert_positions, axis=0), axis=1))

        # 平均速度
        model_speeds = model_data['ego_speeds'][start_step:end_step+1]
        expert_speeds = expert_data['ego_speeds'][start_step:end_step+1]

        # 平均差异
        min_len = min(len(model_positions), len(expert_positions))
        avg_difference = np.mean(np.linalg.norm(
            model_positions[:min_len] - expert_positions[:min_len], axis=1))

        # 创建统计信息文本
        stats_text = f"""Trajectory Statistics (Steps {start_step}-{end_step}):
Model Distance: {model_distance:.1f}m
Expert Distance: {expert_distance:.1f}m
Model Avg Speed: {np.mean(model_speeds):.1f} km/h
Expert Avg Speed: {np.mean(expert_speeds):.1f} km/h
Average Difference: {avg_difference:.1f}m"""

        # 添加统计信息框
        ax.text(0.02, 0.02, stats_text, transform=ax.transAxes,
               fontsize=10, verticalalignment='bottom',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9),
               color=self.colors['text'])


def create_trajectory_comparison_demo(
    model_path="/home/<USER>/rl/RL-IL/dagger_gnn_transformer/dagger_model/best_model.pth",
    output_dir="trajectory_comparison",
    episode_steps=120,
    seed=15
):
    """
    创建轨迹对比演示

    Args:
        model_path: 模型路径
        episode_steps: 运行步数
        seed: 随机种子
    """
    config = {
        'hidden_dim': 128, 'transformer_dim': 256, 'num_layers': 2,
        'num_transformer_layers': 4, 'num_heads': 8, 'max_vehicles': 10,
        'dropout': 0.1, 'sequence_dim': 72, 'ego_node_dim': 7, 'npc_node_dim': 7,
        'action_dim': 2, 'max_npc_distance': 30, 'expert_type': 'expert',
        'env_config': {
            "out_of_road_done": True,
            "use_render": False,
            "num_scenarios": 10,
            "traffic_density": 0.1,  # 增加交通密度
            "start_seed": 1,
            "map": "SSSSS",
            "accident_prob": 0.0,
            "vehicle_config": {
                "lidar": {"num_lasers": 30, "distance": 50},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            }
        }
    }

    print("=== 轨迹对比可视化演示 ===")
    print(f"📊 参数设置:")
    print(f"  - 模型路径: {model_path}")
    print(f"  - 输出目录: {output_dir}")
    print(f"  - 运行步数: {episode_steps}")
    print(f"  - 随机种子: {seed}")

    visualizer = TrajectoryComparisonVisualizer(model_path, config, output_dir)

    # 收集双轨迹数据
    model_data, expert_data = visualizer.collect_dual_trajectories(episode_steps, seed)

    # 创建对比可视化
    save_path = visualizer.output_dir / "trajectory_comparison"
    fig, ax = visualizer.create_trajectory_comparison_visualization(
        model_data, expert_data, save_path=save_path
    )

    print(f"\n🎉 轨迹对比可视化完成！")
    print(f"📁 文件保存在: {output_dir}")

    return model_data, expert_data


def main():
    """主函数"""
    print("🎯 轨迹对比可视化工具")
    print("=" * 50)

    # 生成轨迹对比可视化
    create_trajectory_comparison_demo(
        episode_steps=100,
        seed=20  # 尝试不同的种子找到有趣的场景
    )

    return True


if __name__ == "__main__":
    if main():
        print("\n🎉 轨迹对比可视化生成成功！")
    else:
        print("\n💥 轨迹对比可视化生成失败！")
        sys.exit(1)
