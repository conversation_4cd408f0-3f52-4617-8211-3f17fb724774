#!/usr/bin/env python3
"""
矢量图形可视化工具
专门为论文生成高质量、可缩放的矢量图形
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import FancyArrowPatch, Circle, Polygon
import matplotlib.patheffects as path_effects
from pathlib import Path
import seaborn as sns

# 设置matplotlib为矢量输出
plt.rcParams['pdf.fonttype'] = 42  # 确保字体嵌入
plt.rcParams['ps.fonttype'] = 42
plt.rcParams['font.family'] = 'serif'
plt.rcParams['font.serif'] = ['Times New Roman', 'DejaVu Serif']
plt.rcParams['mathtext.fontset'] = 'stix'
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300

class VectorVisualizationTool:
    """矢量图形可视化工具"""
    
    def __init__(self, style='ieee_paper'):
        """
        初始化矢量可视化工具
        
        Args:
            style: 论文风格 ('ieee_paper', 'nature', 'acm', 'custom')
        """
        self.style = style
        self.setup_style()
        
    def setup_style(self):
        """设置不同期刊的可视化风格"""
        if self.style == 'ieee_paper':
            self.colors = {
                'background': '#FFFFFF',
                'road': '#F5F5F5',
                'lane_line': '#CCCCCC',
                'ego_vehicle': '#1f77b4',  # 蓝色
                'npc_vehicle': '#ff7f0e',  # 橙色
                'trajectory': '#2ca02c',   # 绿色
                'throttle_arrow': '#d62728',  # 红色
                'brake_arrow': '#9467bd',     # 紫色
                'steering_arrow': '#8c564b',  # 棕色
                'text': '#000000',
                'grid': '#E0E0E0'
            }
            self.font_sizes = {
                'title': 14,
                'label': 12,
                'tick': 10,
                'legend': 10,
                'annotation': 9
            }
        elif self.style == 'nature':
            self.colors = {
                'background': '#FFFFFF',
                'road': '#F8F8F8',
                'lane_line': '#D0D0D0',
                'ego_vehicle': '#0173B2',
                'npc_vehicle': '#DE8F05',
                'trajectory': '#029E73',
                'throttle_arrow': '#CC78BC',
                'brake_arrow': '#CA9161',
                'steering_arrow': '#FBAFE4',
                'text': '#000000',
                'grid': '#E8E8E8'
            }
            self.font_sizes = {
                'title': 16,
                'label': 14,
                'tick': 12,
                'legend': 12,
                'annotation': 10
            }
        else:  # 默认风格
            self.colors = {
                'background': '#FFFFFF',
                'road': '#F0F0F0',
                'lane_line': '#CCCCCC',
                'ego_vehicle': '#2E86AB',
                'npc_vehicle': '#A23B72',
                'trajectory': '#F18F01',
                'throttle_arrow': '#C73E1D',
                'brake_arrow': '#0077BE',
                'steering_arrow': '#7209B7',
                'text': '#000000',
                'grid': '#E0E0E0'
            }
            self.font_sizes = {
                'title': 14,
                'label': 12,
                'tick': 10,
                'legend': 10,
                'annotation': 9
            }
    
    def create_vector_arrow(self, ax, start, end, arrow_type='throttle', magnitude=1.0, 
                          label=None, show_magnitude=True):
        """
        创建高质量矢量箭头
        
        Args:
            ax: matplotlib轴对象
            start: 起始点 (x, y)
            end: 终点 (x, y)
            arrow_type: 箭头类型
            magnitude: 大小 (0-1)
            label: 标签
            show_magnitude: 是否显示数值
        """
        # 选择颜色和样式
        if arrow_type == 'throttle':
            color = self.colors['throttle_arrow']
            linewidth = 3.0
            alpha = 0.8
        elif arrow_type == 'brake':
            color = self.colors['brake_arrow']
            linewidth = 3.0
            alpha = 0.8
        elif arrow_type == 'steering':
            color = self.colors['steering_arrow']
            linewidth = 2.5
            alpha = 0.8
        else:
            color = self.colors['text']
            linewidth = 2.0
            alpha = 0.7
        
        # 创建箭头
        arrow = FancyArrowPatch(
            start, end,
            arrowstyle='-|>',
            mutation_scale=20 * magnitude,
            linewidth=linewidth * magnitude,
            color=color,
            alpha=alpha,
            zorder=10,
            # 添加描边效果
            path_effects=[
                path_effects.Stroke(linewidth=linewidth * magnitude + 1, foreground='white'),
                path_effects.Normal()
            ]
        )
        ax.add_patch(arrow)
        
        # 添加标签
        if label:
            mid_x = (start[0] + end[0]) / 2
            mid_y = (start[1] + end[1]) / 2
            ax.annotate(label, (mid_x, mid_y), 
                       fontsize=self.font_sizes['annotation'],
                       ha='center', va='bottom',
                       color=color, fontweight='bold')
        
        # 显示数值
        if show_magnitude:
            mid_x = (start[0] + end[0]) / 2
            mid_y = (start[1] + end[1]) / 2 - 0.5
            ax.annotate(f'{magnitude:.2f}', (mid_x, mid_y),
                       fontsize=self.font_sizes['annotation'] - 1,
                       ha='center', va='top',
                       color=color, alpha=0.8)
        
        return arrow
    
    def create_vehicle_shape(self, ax, position, heading, vehicle_type='ego', 
                           scale=1.0, show_direction=True):
        """
        创建精确的车辆形状
        
        Args:
            ax: matplotlib轴对象
            position: 车辆位置 (x, y)
            heading: 车辆朝向（弧度）
            vehicle_type: 车辆类型
            scale: 缩放因子
            show_direction: 是否显示方向指示
        """
        x, y = position
        
        # 车辆尺寸（真实比例）
        length = 4.5 * scale
        width = 1.8 * scale
        
        # 选择颜色
        if vehicle_type == 'ego':
            facecolor = self.colors['ego_vehicle']
            edgecolor = 'black'
            linewidth = 2
            alpha = 0.9
        else:
            facecolor = self.colors['npc_vehicle']
            edgecolor = 'darkred'
            linewidth = 1.5
            alpha = 0.8
        
        # 创建车辆矩形
        vehicle = patches.Rectangle(
            (-length/2, -width/2), length, width,
            facecolor=facecolor,
            edgecolor=edgecolor,
            linewidth=linewidth,
            alpha=alpha,
            zorder=5
        )
        
        # 应用旋转和平移变换
        t = plt.matplotlib.transforms.Affine2D().rotate(heading).translate(x, y) + ax.transData
        vehicle.set_transform(t)
        ax.add_patch(vehicle)
        
        # 添加方向指示（车头）
        if show_direction:
            front_x = x + (length/2 + 0.5) * np.cos(heading) * scale
            front_y = y + (length/2 + 0.5) * np.sin(heading) * scale
            
            direction_marker = Circle(
                (front_x, front_y), 0.3 * scale,
                facecolor='white',
                edgecolor=edgecolor,
                linewidth=linewidth,
                zorder=6
            )
            ax.add_patch(direction_marker)
        
        return vehicle
    
    def create_road_environment(self, ax, center_pos, view_range=40, road_width=7.2):
        """
        创建道路环境
        
        Args:
            ax: matplotlib轴对象
            center_pos: 中心位置
            view_range: 视图范围
            road_width: 道路宽度
        """
        x_center, y_center = center_pos
        
        # 设置视图范围
        ax.set_xlim(x_center - view_range, x_center + view_range)
        ax.set_ylim(y_center - view_range/2, y_center + view_range/2)
        
        # 绘制道路背景
        road = patches.Rectangle(
            (x_center - view_range, y_center - road_width/2),
            2 * view_range, road_width,
            facecolor=self.colors['road'],
            edgecolor=self.colors['lane_line'],
            linewidth=2,
            alpha=0.8,
            zorder=1
        )
        ax.add_patch(road)
        
        # 绘制车道线
        lane_positions = [-road_width/3, 0, road_width/3]
        for lane_y in lane_positions:
            # 虚线车道线
            x_positions = np.arange(x_center - view_range, x_center + view_range, 2)
            for i, x_pos in enumerate(x_positions):
                if i % 2 == 0:  # 虚线效果
                    line = patches.Rectangle(
                        (x_pos, y_center + lane_y - 0.05),
                        1.5, 0.1,
                        facecolor=self.colors['lane_line'],
                        edgecolor='none',
                        alpha=0.8,
                        zorder=2
                    )
                    ax.add_patch(line)
    
    def create_comprehensive_visualization(self, vehicle_data, action_data, 
                                         trajectory=None, save_path=None, 
                                         title="Vehicle State Visualization"):
        """
        创建综合可视化图像
        
        Args:
            vehicle_data: 车辆数据字典
            action_data: 动作数据字典
            trajectory: 轨迹数据
            save_path: 保存路径
            title: 图像标题
        """
        # 创建图像
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.set_facecolor(self.colors['background'])
        
        # 获取自车位置
        ego_pos = vehicle_data['ego']['position']
        ego_heading = vehicle_data['ego']['heading']
        
        # 创建道路环境
        self.create_road_environment(ax, ego_pos)
        
        # 绘制轨迹
        if trajectory is not None and len(trajectory) > 1:
            traj_array = np.array(trajectory)
            ax.plot(traj_array[:, 0], traj_array[:, 1],
                   color=self.colors['trajectory'],
                   linewidth=3, alpha=0.7, zorder=3,
                   label='Vehicle Trajectory')
        
        # 绘制NPC车辆
        if 'npcs' in vehicle_data:
            for npc in vehicle_data['npcs']:
                self.create_vehicle_shape(
                    ax, npc['position'], npc['heading'], 'npc', scale=0.8
                )
        
        # 绘制自车
        ego_vehicle = self.create_vehicle_shape(
            ax, ego_pos, ego_heading, 'ego', scale=1.0
        )
        
        # 绘制动作箭头
        self._draw_action_arrows(ax, ego_pos, ego_heading, action_data)
        
        # 设置图像属性
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3, color=self.colors['grid'], linewidth=0.5)
        ax.set_xlabel('X Position (m)', fontsize=self.font_sizes['label'])
        ax.set_ylabel('Y Position (m)', fontsize=self.font_sizes['label'])
        ax.set_title(title, fontsize=self.font_sizes['title'], fontweight='bold')
        
        # 设置刻度
        ax.tick_params(labelsize=self.font_sizes['tick'])
        
        # 添加图例
        self._add_legend(ax)
        
        # 添加信息面板
        self._add_info_panel(ax, vehicle_data, action_data)
        
        # 保存图像
        if save_path:
            # 保存为多种格式
            base_path = Path(save_path).with_suffix('')
            
            # 保存为PDF（矢量格式）
            plt.savefig(f"{base_path}.pdf", format='pdf', bbox_inches='tight',
                       facecolor=self.colors['background'], dpi=300)
            
            # 保存为SVG（矢量格式）
            plt.savefig(f"{base_path}.svg", format='svg', bbox_inches='tight',
                       facecolor=self.colors['background'], dpi=300)
            
            # 保存为高分辨率PNG
            plt.savefig(f"{base_path}.png", format='png', bbox_inches='tight',
                       facecolor=self.colors['background'], dpi=600)
            
            print(f"📸 矢量图形已保存:")
            print(f"  - PDF: {base_path}.pdf")
            print(f"  - SVG: {base_path}.svg")
            print(f"  - PNG: {base_path}.png")
        
        return fig, ax
    
    def _draw_action_arrows(self, ax, ego_pos, ego_heading, action_data):
        """绘制动作箭头"""
        x, y = ego_pos
        
        # 油门/刹车箭头
        throttle = action_data.get('throttle', 0.0)
        if abs(throttle) > 0.05:
            arrow_type = 'throttle' if throttle > 0 else 'brake'
            arrow_length = abs(throttle) * 5.0
            
            start_pos = (x + 2.5 * np.cos(ego_heading), y + 2.5 * np.sin(ego_heading))
            end_pos = (start_pos[0] + arrow_length * np.cos(ego_heading),
                      start_pos[1] + arrow_length * np.sin(ego_heading))
            
            self.create_vector_arrow(
                ax, start_pos, end_pos, arrow_type, abs(throttle),
                label=f"{'Throttle' if throttle > 0 else 'Brake'}"
            )
        
        # 转向箭头
        steering = action_data.get('steering', 0.0)
        if abs(steering) > 0.05:
            steering_angle = ego_heading + np.pi/2 * np.sign(steering)
            arrow_length = abs(steering) * 3.0
            
            start_pos = (x + 1.5 * np.cos(steering_angle), y + 1.5 * np.sin(steering_angle))
            end_pos = (start_pos[0] + arrow_length * np.cos(steering_angle),
                      start_pos[1] + arrow_length * np.sin(steering_angle))
            
            self.create_vector_arrow(
                ax, start_pos, end_pos, 'steering', abs(steering),
                label="Steering"
            )
    
    def _add_legend(self, ax):
        """添加图例"""
        legend_elements = [
            plt.Line2D([0], [0], color=self.colors['ego_vehicle'], lw=4, label='Ego Vehicle'),
            plt.Line2D([0], [0], color=self.colors['npc_vehicle'], lw=4, label='NPC Vehicle'),
            plt.Line2D([0], [0], color=self.colors['trajectory'], lw=3, label='Trajectory'),
            plt.Line2D([0], [0], color=self.colors['throttle_arrow'], lw=3, label='Throttle'),
            plt.Line2D([0], [0], color=self.colors['brake_arrow'], lw=3, label='Brake'),
            plt.Line2D([0], [0], color=self.colors['steering_arrow'], lw=3, label='Steering')
        ]
        
        ax.legend(handles=legend_elements, loc='upper right', 
                 fontsize=self.font_sizes['legend'],
                 framealpha=0.9, fancybox=True, shadow=True)
    
    def _add_info_panel(self, ax, vehicle_data, action_data):
        """添加信息面板"""
        info_lines = []
        
        # 车辆信息
        if 'ego' in vehicle_data:
            ego = vehicle_data['ego']
            info_lines.append(f"Speed: {ego.get('speed', 0):.1f} km/h")
            info_lines.append(f"Heading: {np.degrees(ego.get('heading', 0)):.1f}°")
        
        # 动作信息
        info_lines.append("--- Actions ---")
        info_lines.append(f"Steering: {action_data.get('steering', 0):.3f}")
        info_lines.append(f"Throttle: {action_data.get('throttle', 0):.3f}")
        
        # 专家动作（如果有）
        if 'expert_steering' in action_data:
            info_lines.append("--- Expert ---")
            info_lines.append(f"E_Steering: {action_data.get('expert_steering', 0):.3f}")
            info_lines.append(f"E_Throttle: {action_data.get('expert_throttle', 0):.3f}")
        
        info_text = '\n'.join(info_lines)
        ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
               fontsize=self.font_sizes['annotation'],
               verticalalignment='top', horizontalalignment='left',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='white', 
                        alpha=0.9, edgecolor='gray'),
               color=self.colors['text'])
