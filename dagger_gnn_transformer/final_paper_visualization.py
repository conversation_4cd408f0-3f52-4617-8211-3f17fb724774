#!/usr/bin/env python3
"""
生成最终的论文可视化图片
"""

import sys
import os
sys.path.append('dagger_gnn_transformer')

from dagger_gnn_transformer.make_pic import create_paper_visualization

def generate_final_paper_figures():
    """生成最终的高质量论文图片"""
    
    print("🎯 生成最终论文可视化图片")
    print("📐 高分辨率: 1200x900 → 400x300 (每张小图)")
    print("🔤 大字体: 标题36px, 副标题24px, 信息24px")
    print("=" * 60)
    
    # 配置1: 6张图片，最佳质量和清晰度
    print("\n📸 配置1: 6张图片 (3x2布局) - 推荐用于论文")
    print("   特点: 图片大、字体清晰、布局美观")
    create_paper_visualization(
        num_episodes=2,  # 生成成功和失败案例
        vis_save_interval=3.0,  # 每3秒一张，确保关键时刻
        vis_max_images=6,
        output_dir="final_6pics_recommended"
    )
    
    print("\n🎉 最终论文图片生成完成！")

if __name__ == "__main__":
    generate_final_paper_figures()
