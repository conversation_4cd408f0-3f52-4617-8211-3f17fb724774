#!/usr/bin/env python3
"""
修复专家动作归一化问题
"""

import sys
import os
sys.path.append('.')
sys.path.append('./dagger_gnn_transformer')

import numpy as np
import torch

def normalize_expert_action(action):
    """
    将专家动作归一化到模型输出的相同范围
    
    Args:
        action: [steering, throttle]
    
    Returns:
        normalized_action: [steering, throttle] 归一化后的动作
    """
    steering, throttle = action[0], action[1]
    
    # 对转向进行归一化：限制在[-0.8, 0.8]范围内（与模型输出一致）
    steering_normalized = np.clip(steering, -0.8, 0.8)
    
    # 对油门进行归一化：限制在[-1, 1]范围内（与模型输出一致）
    throttle_normalized = np.clip(throttle, -1.0, 1.0)
    
    return np.array([steering_normalized, throttle_normalized], dtype=np.float32)

def test_normalization():
    """测试归一化函数"""
    print("🧪 测试动作归一化函数")
    
    # 测试用例
    test_cases = [
        [-0.08192018, 1.3700502],   # ExpertPolicy典型输出
        [-0.07749602, 1.5466545],   # ExpertPolicy超出范围的输出
        [0.0, 1.0],                 # IDM典型输出
        [-1.5, 2.0],                # 极端情况
        [0.5, -0.5]                 # 正常情况
    ]
    
    for i, action in enumerate(test_cases):
        normalized = normalize_expert_action(action)
        print(f"测试 {i+1}: {action} -> {normalized}")
    
    print("✅ 归一化函数测试完成")

def create_fixed_data_collection_function():
    """创建修复后的数据收集函数"""
    
    fixed_code = '''
def collect_episodes_continuous_fixed(args):
    """
    修复后的连续数据收集函数 - 对专家动作进行归一化
    """
    env_config, expert_policy_class, beta, max_steps, episode_ids, model_config = args

    import logging
    logging.getLogger('metadrive').setLevel(logging.ERROR)
    os.environ['METADRIVE_LOG_LEVEL'] = 'ERROR'

    try:
        from metadrive.engine.engine_utils import close_engine
        close_engine()
    except Exception:
        pass

    all_episodes_data = []

    try:
        # 在创建环境之前应用NPC车速控制猴子补丁
        npc_speed_config = model_config.get('npc_speed_config', {})
        if npc_speed_config.get('use_slow_traffic', False):
            _patch_traffic_manager_for_slow_npc_worker(npc_speed_config)

        env_config_copy = env_config.copy()
        env_config_copy["agent_policy"] = expert_policy_class

        with quiet_metadrive():
            env = MetaDriveEnv(env_config_copy)

        # 连续收集多个episodes
        for episode_id in episode_ids:
            obs, info = env.reset()
            episode_data = []

            for step in range(max_steps):
                sensor_features = get_pure_sensor_data(obs)

                ego = env.agent
                ego_state = np.array([
                    ego.speed_km_h / 120.0, ego.heading_diff(ego.lane), ego.steering,
                    ego.throttle_brake, ego.dist_to_left_side, ego.dist_to_right_side,
                    np.linalg.norm(ego.velocity)
                ], dtype=np.float32)

                # 构建图数据...
                # [图数据构建代码保持不变]

                action = [0.0, 0.0]
                obs, reward, terminated, truncated, info = env.step(action)
                expert_action = info.get('action', action)

                # 🔧 关键修复：对专家动作进行归一化
                expert_action_normalized = normalize_expert_action(expert_action)

                # 采集边界距离用于安全损失计算
                dist_left = getattr(ego, 'dist_to_left_side', 0.0)
                dist_right = getattr(ego, 'dist_to_right_side', 0.0)

                step_data = {
                    'graph_data': graph_data,
                    'sensor_features': sensor_features,
                    'action_target': expert_action_normalized,  # 使用归一化后的动作
                    'dist_to_boundaries': np.array([dist_left, dist_right], dtype=np.float32)
                }
                episode_data.append(step_data)

                if terminated or truncated:
                    break

            all_episodes_data.append({
                'episode_id': episode_id,
                'data': episode_data,
                'success': len(episode_data) > 0
            })

    except Exception as e:
        print(f"Episode collection failed: {e}")
        return []
    finally:
        try:
            env.close()
        except:
            pass

    return all_episodes_data
'''
    
    print("📝 修复后的数据收集函数代码：")
    print(fixed_code)
    return fixed_code

if __name__ == "__main__":
    test_normalization()
    print("\n" + "="*50)
    create_fixed_data_collection_function()
    
    print("\n🔧 修复建议：")
    print("1. 在数据收集时对专家动作进行归一化")
    print("2. 确保训练目标与模型输出范围一致")
    print("3. 考虑使用ExpertPolicy而不是IDMPolicy，因为IDM动作变化太小")
    print("4. 如果必须使用IDM，可能需要调整IDM参数使其产生更多样化的动作")
