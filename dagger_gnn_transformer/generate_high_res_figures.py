#!/usr/bin/env python3
"""
生成高分辨率论文图片
"""

import sys
import os
sys.path.append('dagger_gnn_transformer')

from dagger_gnn_transformer.make_pic import create_paper_visualization

def generate_high_resolution_figures():
    """生成高分辨率的论文可视化图片"""
    
    print("🎯 生成高分辨率DAGGER可视化图片")
    print("📐 原始分辨率: 1200x900 (提升3倍)")
    print("=" * 60)
    
    # 配置1: 9张图片，高质量展示
    print("\n📸 生成配置1: 9张图片 (3x3布局) - 高分辨率")
    print("   每张小图尺寸: 400x300 像素")
    create_paper_visualization(
        num_episodes=1,
        vis_save_interval=2.0,
        vis_max_images=9,
        output_dir="high_res_9pics"
    )
    
    # 配置2: 6张图片，超高质量
    print("\n📸 生成配置2: 6张图片 (3x2布局) - 超高分辨率")
    print("   每张小图尺寸: 400x300 像素")
    create_paper_visualization(
        num_episodes=1,
        vis_save_interval=3.0,
        vis_max_images=6,
        output_dir="high_res_6pics"
    )
    
    # 配置3: 4张图片，最高质量
    print("\n📸 生成配置3: 4张图片 (2x2布局) - 最高分辨率")
    print("   每张小图尺寸: 400x300 像素")
    create_paper_visualization(
        num_episodes=1,
        vis_save_interval=4.0,
        vis_max_images=4,
        output_dir="high_res_4pics"
    )
    
    print("\n🎉 高分辨率图片生成完成！")
    print("\n📊 分辨率对比:")
    print("  - 原版: 400x300 → 200x150 (小图)")
    print("  - 新版: 1200x900 → 400x300 (小图)")
    print("  - 提升: 4倍像素密度")
    
    print("\n📁 生成的高分辨率目录:")
    print("  - high_res_4pics/  (2x2布局，最高质量)")
    print("  - high_res_6pics/  (3x2布局，超高质量)")
    print("  - high_res_9pics/  (3x3布局，高质量)")
    
    print("\n💡 论文使用建议:")
    print("  - 4张图片: 适合重点展示关键时刻")
    print("  - 6张图片: 适合展示完整驾驶流程")
    print("  - 9张图片: 适合详细的行为分析")
    
    print("\n🔍 图片质量特点:")
    print("  - 原始分辨率: 1200x900 (3倍提升)")
    print("  - 小图尺寸: 400x300 (4倍像素)")
    print("  - 边框效果: 清晰的灰色分隔")
    print("  - 输出格式: 高质量PNG")

if __name__ == "__main__":
    generate_high_resolution_figures()
