#!/usr/bin/env python3
"""
生成论文用的DAGGER可视化图片
"""

import sys
import os
sys.path.append('dagger_gnn_transformer')

from dagger_gnn_transformer.make_pic import create_paper_visualization

def generate_paper_figures():
    """生成不同配置的论文图片"""
    
    print("🎯 生成论文用DAGGER可视化图片")
    print("=" * 50)
    
    # 配置1: 9张图片，适合展示关键时刻
    print("\n📸 生成配置1: 9张图片 (3x3布局)")
    create_paper_visualization(
        num_episodes=2,
        vis_save_interval=2.0,
        vis_max_images=9,
        output_dir="paper_figures_9pics"
    )
    
    # 配置2: 12张图片，适合详细展示
    print("\n📸 生成配置2: 12张图片 (4x3布局)")
    create_paper_visualization(
        num_episodes=2,
        vis_save_interval=1.5,
        vis_max_images=12,
        output_dir="paper_figures_12pics"
    )
    
    # 配置3: 16张图片，适合完整过程展示
    print("\n📸 生成配置3: 16张图片 (4x4布局)")
    create_paper_visualization(
        num_episodes=1,
        vis_save_interval=1.2,
        vis_max_images=16,
        output_dir="paper_figures_16pics"
    )
    
    print("\n🎉 所有论文图片生成完成！")
    print("\n📁 生成的目录:")
    print("  - paper_figures_9pics/  (3x3布局，适合展示关键时刻)")
    print("  - paper_figures_12pics/ (4x3布局，适合详细分析)")
    print("  - paper_figures_16pics/ (4x4布局，适合完整过程)")
    
    print("\n💡 使用建议:")
    print("  - 成功案例：展示模型的优秀表现")
    print("  - 失败案例：分析模型的局限性")
    print("  - 对比分析：不同episode的横向对比")
    print("  - 关键决策：重要驾驶决策时刻的展示")

if __name__ == "__main__":
    generate_paper_figures()
