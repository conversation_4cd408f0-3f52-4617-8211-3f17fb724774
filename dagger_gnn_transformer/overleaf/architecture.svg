<?xml version="1.0" encoding="utf-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="-34.099 -51.665 1159.241 581.631" xmlns:bx="https://boxy-svg.com">
  <defs>
    <linearGradient id="inputBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0" style="stop-color:#fef7f0;stop-opacity:1"/>
      <stop offset="1" style="stop-color:#fce0c8;stop-opacity:1"/>
    </linearGradient>
    <linearGradient id="normBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0" style="stop-color:#f0f8ff;stop-opacity:1"/>
      <stop offset="1" style="stop-color:#e6f3ff;stop-opacity:1"/>
    </linearGradient>
    <linearGradient id="gnnBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0" style="stop-color:#fffef0;stop-opacity:1"/>
      <stop offset="1" style="stop-color:#ffffcd;stop-opacity:1"/>
    </linearGradient>
    <linearGradient id="transformerBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0" style="stop-color:#f0fffe;stop-opacity:1"/>
      <stop offset="1" style="stop-color:#b4f5fe;stop-opacity:1"/>
    </linearGradient>
    <linearGradient id="fusionBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0" style="stop-color:#fff5f5;stop-opacity:1"/>
      <stop offset="1" style="stop-color:#ffc9c9;stop-opacity:1"/>
    </linearGradient>
    <linearGradient id="attentionBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0" style="stop-color:#f8fff8;stop-opacity:1"/>
      <stop offset="1" style="stop-color:#ceeab0;stop-opacity:1"/>
    </linearGradient>
    <linearGradient id="outputBg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0" style="stop-color:#f8f8ff;stop-opacity:1"/>
      <stop offset="1" style="stop-color:#e6e6fa;stop-opacity:1"/>
    </linearGradient>
    <marker id="arrow" markerWidth="8" markerHeight="8" refX="6" refY="3" orient="auto" markerUnits="strokeWidth">
      <path d="M0,0 L0,6 L6,3 z" fill="#666"/>
    </marker>
    <!-- Remove problematic feDropShadow filter for better PDF compatibility -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="1"/>
      <feOffset dx="2" dy="2" result="offset"/>
      <feFlood flood-color="#00000020"/>
      <feComposite in2="offset" operator="in"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    <bx:export>
      <bx:file format="svg" excluded="true"/>
      <bx:file format="png" excluded="true"/>
      <bx:file format="svg" href="#object-0" path="Untitled 2.svg" excluded="true"/>
      <bx:file format="svg" href="#input-section" path="Untitled 3.svg" excluded="true"/>
      <bx:file format="svg" href="#normalization-section" path="Untitled 4.svg" excluded="true"/>
      <bx:file format="svg" href="#gnn-section" path="Untitled 5.svg" excluded="true"/>
      <bx:file format="svg" href="#sensor-section" path="Untitled 6.svg" excluded="true"/>
      <bx:file format="svg" href="#fusion-section" path="Untitled 7.svg" excluded="true"/>
      <bx:file format="svg" href="#transformer-section" path="Untitled 8.svg" excluded="true"/>
      <bx:file format="svg" href="#output-section" path="Untitled 9.svg" excluded="true"/>
      <bx:file format="svg" href="#object-1" path="Untitled 10.svg" excluded="true"/>
      <bx:file format="svg" href="#object-2" path="Untitled 11.svg" excluded="true"/>
      <bx:file format="svg" href="#object-3" path="Untitled 12.svg" excluded="true"/>
      <bx:file format="svg" href="#object-4" path="Untitled 13.svg" excluded="true"/>
      <bx:file format="svg" href="#object-5" path="Untitled 14.svg" excluded="true"/>
      <bx:file format="svg" href="#object-6" path="Untitled 15.svg" excluded="true"/>
      <bx:file format="svg" href="#object-7" path="Untitled 16.svg" excluded="true"/>
      <bx:file format="svg" href="#object-8" path="Untitled 17.svg" excluded="true"/>
      <bx:file format="svg" href="#object-9" path="Untitled 18.svg" excluded="true"/>
      <bx:file format="svg" href="#object-10" path="Untitled 19.svg" excluded="true"/>
      <bx:file format="svg" href="#object-11" path="Untitled 20.svg" excluded="true"/>
      <bx:file format="svg" href="#object-12" path="Untitled 21.svg" excluded="true"/>
      <bx:file format="svg" href="#object-13" path="Untitled 22.svg" excluded="true"/>
      <bx:file format="svg" href="#object-14" path="Untitled 23.svg" excluded="true"/>
      <bx:file format="svg" path="Untitled 24.svg"/>
    </bx:export>
  </defs>

  <g id="input-section" transform="matrix(1, 0, 0, 1, -14.953109, -38.047002)" style="transform-origin: 550px 270.265px;">
    <rect x="20" y="50" width="200" height="480" rx="5" fill="url(#inputBg)" stroke="#6c757d" stroke-width="1" stroke-dasharray="5,5" filter="url(#shadow)"/>
    <text x="120" y="70" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" style="white-space: pre;">Input</text>
    <rect x="30" y="90" width="180" height="120" rx="3" fill="#ffffff" stroke="#495057" stroke-width="1"/>
    <text x="120" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" style="white-space: pre;">Vehicle Graph</text>
    <!-- 将线段放在圆圈下面 -->
    <line x1="120" y1="140" x2="80" y2="130" stroke="#6c757d" stroke-width="1"/>
    <line x1="120" y1="140" x2="160" y2="130" stroke="#6c757d" stroke-width="1"/>
    <line x1="120" y1="140" x2="100" y2="160" stroke="#6c757d" stroke-width="1"/>
    <line x1="120" y1="140" x2="140" y2="160" stroke="#6c757d" stroke-width="1"/>
    <!-- 圆圈在线段上面 -->
    <circle cx="120" cy="140" r="8" fill="#007bff" stroke="#0056b3" stroke-width="2"/>
    <circle cx="80" cy="130" r="5" fill="#28a745" stroke="#1e7e34" stroke-width="1"/>
    <circle cx="160" cy="130" r="5" fill="#28a745" stroke="#1e7e34" stroke-width="1"/>
    <circle cx="100" cy="160" r="5" fill="#28a745" stroke="#1e7e34" stroke-width="1"/>
    <circle cx="140" cy="160" r="5" fill="#28a745" stroke="#1e7e34" stroke-width="1"/>
    <text x="120" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">Node Features:</text>
    <text x="120" y="195" text-anchor="middle" font-family="Times, serif" font-size="7" fill="black" style="white-space: pre;">[x, y, vₓ, vᵧ, θ, aₓ, aᵧ] ∈ ℝ⁷</text>
    <rect x="30" y="230" width="180" height="120" rx="3" fill="#fff3cd" stroke="#856404" stroke-width="1"/>
    <text x="120" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" style="white-space: pre;">Expert Policy</text>
    <text x="120" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" style="white-space: pre;">IDM Strategy</text>
    <text x="120" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" style="white-space: pre;">β-mixing Strategy</text>
    <text x="120" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" style="white-space: pre;">DAGGER Training</text>
    <text x="120" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">Expert Action Generation</text>
    <text x="120" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">Data Aggregation</text>
    <rect x="30" y="370" width="180" height="120" rx="3" fill="#ffffff" stroke="#495057" stroke-width="1"/>
    <text x="120" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" style="white-space: pre;">Sensor Data</text>
    <g transform="translate(120, 410)">
      <!-- LiDAR/Radar sensor icon -->
      <circle cx="0" cy="15" r="25" fill="none" stroke="#17a2b8" stroke-width="1.5" opacity="0.3"/>
      <circle cx="0" cy="15" r="18" fill="none" stroke="#17a2b8" stroke-width="1.5" opacity="0.5"/>
      <circle cx="0" cy="15" r="12" fill="none" stroke="#17a2b8" stroke-width="1.5" opacity="0.7"/>
      <circle cx="0" cy="15" r="6" fill="#17a2b8" opacity="0.8"/>
      <!-- Scanning beam -->
      <path d="M0,15 L-20,-5 A25,25 0 0,1 20,-5 Z" fill="#17a2b8" opacity="0.2"/>
      <!-- Detection points -->
      <circle cx="-15" cy="5" r="1.5" fill="#ffc107"/>
      <circle cx="12" cy="8" r="1.5" fill="#ffc107"/>
      <circle cx="-8" cy="-2" r="1.5" fill="#ffc107"/>
      <circle cx="18" cy="2" r="1.5" fill="#ffc107"/>
      <circle cx="-22" cy="12" r="1.5" fill="#ffc107"/>
    </g>
    <text x="120" y="460" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" style="white-space: pre;">LiDAR: 30 + Side: 30 + Lane: 12</text>
    <text x="120" y="475" text-anchor="middle" font-family="Times, serif" font-size="9" fill="black" style="white-space: pre;">Sₜ ∈ ℝ⁷²</text>
  </g>
  <g id="normalization-section" transform="matrix(1, 0, 0, 1, -14.953109, -38.047002)" style="transform-origin: 550px 270.265px;">
    <rect x="240" y="50" width="80" height="480" rx="5" fill="url(#normBg)" stroke="#4a90e2" stroke-width="2" filter="url(#shadow)"/>
    <text x="280" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" style="white-space: pre;">Norm</text>
    <rect x="250" y="120" width="60" height="80" rx="3" fill="#ffffff" stroke="#4a90e2" stroke-width="1"/>
    <text x="280" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" style="white-space: pre;">Graph</text>
    <text x="280" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">Min-Max</text>
    <text x="280" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">Scaling</text>
    <text x="280" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" style="white-space: pre;">[0, 1]</text>
    <rect x="248.949" y="389.9" width="60" height="80" rx="3" fill="#ffffff" stroke="#4a90e2" stroke-width="1"/>
    <text x="277.898" y="417.784" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" style="white-space: pre; font-size: 10px;">Sensor</text>
    <text x="279.475" y="434.36" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre; font-size: 10px;">Z-Score</text>
    <text x="277.372" y="448.309" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre; font-size: 10px;">Norm</text>
    <text x="278.423" y="462.784" text-anchor="middle" font-family="Times, serif" font-size="7" style="white-space: pre; font-size: 10px;">μ=0, σ=1</text>
  </g>
  <g id="gnn-section" transform="matrix(1, 0, 0, 1, -14.953109, -38.047002)" style="transform-origin: 550px 270.265px;">
    <rect x="340" y="50" width="180" height="220" rx="5" fill="url(#gnnBg)" stroke="#b8860b" stroke-width="2" filter="url(#shadow)"/>
    <text x="430" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" style="white-space: pre;">GNN Branch</text>
    <text x="350" y="95" font-family="Times, serif" font-size="9" fill="black" style="white-space: pre;">ℝ⁷</text>
    <text x="500" y="95" font-family="Times, serif" font-size="9" fill="black" style="white-space: pre;">ℝ¹²⁸</text>
    <rect x="350" y="110" width="160" height="30" rx="3" fill="#ffffff" stroke="#b8860b" stroke-width="1"/>
    <text x="430" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" style="white-space: pre;">Node Embedding</text>
    <rect x="350" y="150" width="160" height="30" rx="3" fill="#ffffff" stroke="#b8860b" stroke-width="1"/>
    <text x="430" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" style="white-space: pre;">GCN Layer 1</text>
    <rect x="350" y="190" width="160" height="30" rx="3" fill="#ffffff" stroke="#b8860b" stroke-width="1"/>
    <text x="430" y="210" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" style="white-space: pre;">GCN Layer 2</text>
    <rect x="350" y="230" width="160" height="30" rx="3" fill="#ffffff" stroke="#b8860b" stroke-width="1"/>
    <text x="430" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" style="white-space: pre;">Global Pooling</text>

  </g>
  <g id="sensor-section" transform="matrix(1, 0, 0, 1, -14.953109, -38.047002)" style="transform-origin: 550px 270.265px;">
    <rect x="340" y="300" width="180" height="230" rx="5" fill="url(#transformerBg)" stroke="#20b2aa" stroke-width="2" filter="url(#shadow)"/>
    <text x="430" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" style="white-space: pre;">Sensor Branch</text>
    <text x="350" y="345" font-family="Times, serif" font-size="9" fill="black" style="white-space: pre;">ℝ⁷²</text>
    <text x="500" y="345" font-family="Times, serif" font-size="9" fill="black" style="white-space: pre;">ℝ¹²⁸</text>
    <rect x="350" y="360" width="160" height="30" rx="3" fill="#ffffff" stroke="#20b2aa" stroke-width="1"/>
    <text x="430" y="380" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" style="white-space: pre;">Linear Projection</text>
    <rect x="350" y="400" width="160" height="30" rx="3" fill="#ffffff" stroke="#20b2aa" stroke-width="1"/>
    <text x="430" y="420" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" style="white-space: pre;">Feature Extraction</text>
    <rect x="350" y="440" width="160" height="30" rx="3" fill="#ffffff" stroke="#20b2aa" stroke-width="1"/>
    <text x="430" y="460" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" style="white-space: pre;">Normalization</text>
    <rect x="350" y="480" width="160" height="30" rx="3" fill="#ffffff" stroke="#20b2aa" stroke-width="1"/>
    <text x="430" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" style="white-space: pre;">Activation</text>

  </g>
  <g id="fusion-section" transform="matrix(1, 0, 0, 1, -14.953109, -38.047002)" style="transform-origin: 550px 270.265px;">
    <rect x="550" y="50" width="120" height="480" rx="5" fill="url(#fusionBg)" stroke="#cd5c5c" stroke-width="2" filter="url(#shadow)"/>
    <text x="610" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" style="white-space: pre;">Feature</text>
    <text x="610" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" style="white-space: pre;">Fusion</text>

    <!-- Graph Features Input -->
    <rect x="560" y="110" width="100" height="30" rx="3" fill="#98fb98" stroke="#32cd32" stroke-width="1.5"/>
    <text x="610" y="126" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="black" style="white-space: pre;">Graph Features</text>
    <text x="610" y="136" text-anchor="middle" font-family="Times, serif" font-size="8" fill="black" style="white-space: pre;">ℝ¹²⁸</text>

    <!-- Sensor Features Input -->
    <rect x="560" y="150" width="100" height="30" rx="3" fill="#87ceeb" stroke="#4682b4" stroke-width="1.5"/>
    <text x="610" y="166" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" fill="black" style="white-space: pre;">Sensor Features</text>
    <text x="610" y="176" text-anchor="middle" font-family="Times, serif" font-size="8" fill="black" style="white-space: pre;">ℝ¹²⁸</text>

    <!-- Concatenation Visualization -->
    <g transform="translate(610, 195)">
      <!-- Input arrows pointing to fusion point -->
      <path d="M-25,0 L-5,0" stroke="#32cd32" stroke-width="2" marker-end="url(#arrow)"/>
      <path d="M25,0 L5,0" stroke="#4682b4" stroke-width="2" marker-end="url(#arrow)"/>
      <!-- Fusion symbol -->
      <circle cx="0" cy="0" r="8" fill="#cd5c5c" stroke="#8b0000" stroke-width="2"/>
      <text x="0" y="4" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="white" style="white-space: pre;">⊕</text>
    </g>
    <text x="610" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" style="white-space: pre;">Concatenation</text>
    <text x="610" y="228" text-anchor="middle" font-family="Times, serif" font-size="9" fill="black" style="white-space: pre;">ℝ²⁵⁶</text>

    <!-- Processing Layers -->
    <text x="610" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" style="white-space: pre;">Processing Layers</text>

    <!-- Linear Layer -->
    <rect x="560" y="270" width="100" height="32" rx="3" fill="#ffffff" stroke="#cd5c5c" stroke-width="1.5"/>
    <text x="610" y="286" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" style="white-space: pre;">Linear Layer</text>
    <text x="610" y="298" text-anchor="middle" font-family="Times, serif" font-size="8" fill="#666" style="white-space: pre;">256 → 256</text>

    <!-- Layer Normalization -->
    <rect x="560" y="310" width="100" height="30" rx="3" fill="#fff8dc" stroke="#cd5c5c" stroke-width="1"/>
    <text x="610" y="327" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" style="white-space: pre;">Layer Norm</text>
    <text x="610" y="337" text-anchor="middle" font-family="Times, serif" font-size="7" fill="#666" style="white-space: pre;">Stabilization</text>

    <!-- ReLU Activation -->
    <rect x="560" y="350" width="100" height="30" rx="3" fill="#f0fff0" stroke="#cd5c5c" stroke-width="1"/>
    <text x="610" y="367" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" style="white-space: pre;">ReLU</text>
    <text x="610" y="377" text-anchor="middle" font-family="Times, serif" font-size="7" fill="#666" style="white-space: pre;">Non-linearity</text>

    <!-- Dropout -->
    <rect x="560" y="390" width="100" height="30" rx="3" fill="#f5f5f5" stroke="#cd5c5c" stroke-width="1"/>
    <text x="610" y="407" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" style="white-space: pre;">Dropout(0.1)</text>
    <text x="610" y="417" text-anchor="middle" font-family="Times, serif" font-size="7" fill="#666" style="white-space: pre;">Regularization</text>

    <!-- Final Output -->
    <rect x="560" y="430" width="100" height="35" rx="3" fill="#ffe4e1" stroke="#cd5c5c" stroke-width="2"/>
    <text x="610" y="447" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" style="white-space: pre;">Fused Features</text>
    <text x="610" y="460" text-anchor="middle" font-family="Times, serif" font-size="9" fill="black" style="white-space: pre;">ℝ²⁵⁶</text>
  </g>
  <g id="transformer-section" transform="matrix(1, 0, 0, 1, -14.953109, -38.047002)" style="transform-origin: 550px 270.265px;">
    <rect x="700" y="50" width="200" height="480" rx="5" fill="url(#attentionBg)" stroke="#228b22" stroke-width="2" filter="url(#shadow)"/>
    <text x="800" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" style="white-space: pre;">Transformer</text>
    <text x="800" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" style="white-space: pre;">Encoder</text>
    <text x="710" y="110" font-family="Times, serif" font-size="9" fill="black" style="white-space: pre;">ℝ²⁵⁶</text>
    <text x="870" y="110" font-family="Times, serif" font-size="9" fill="black" style="white-space: pre;">ℝ²⁵⁶</text>
    <rect x="710" y="120" width="180" height="80" rx="3" fill="#ffffff" stroke="#228b22" stroke-width="1"/>
    <text x="800" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" style="white-space: pre;">Transformer Block 1</text>
    <text x="800" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" style="white-space: pre;">Multi-Head Attention</text>
    <text x="800" y="168" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">8 heads</text>
    <g transform="translate(800, 175)">
      <rect x="-60" y="0" width="14" height="8" rx="1" fill="#ffd700"/>
      <rect x="-43" y="0" width="14" height="8" rx="1" fill="#ffd700"/>
      <rect x="-26" y="0" width="14" height="8" rx="1" fill="#ffd700"/>
      <rect x="-9" y="0" width="14" height="8" rx="1" fill="#ffd700"/>
      <rect x="8" y="0" width="14" height="8" rx="1" fill="#ffd700"/>
      <rect x="25" y="0" width="14" height="8" rx="1" fill="#ffd700"/>
      <rect x="42" y="0" width="14" height="8" rx="1" fill="#ffd700"/>
      <rect x="59" y="0" width="14" height="8" rx="1" fill="#ffd700"/>
    </g>
    <rect x="720" y="188" width="160" height="10" rx="2" fill="#f0fff0" stroke="#228b22" stroke-width="1"/>
    <text x="800" y="195" text-anchor="middle" font-family="Arial, sans-serif" font-size="7" style="white-space: pre;">Feed Forward</text>
    <rect x="710" y="210" width="180" height="60" rx="3" fill="#ffffff" stroke="#228b22" stroke-width="1"/>
    <text x="800" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" style="white-space: pre;">Transformer Block 2</text>
    <text x="800" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">MHA + FFN + Residual</text>
    <rect x="710" y="280" width="180" height="60" rx="3" fill="#ffffff" stroke="#228b22" stroke-width="1"/>
    <text x="800" y="305" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" style="white-space: pre;">Transformer Block 3</text>
    <text x="800" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">MHA + FFN + Residual</text>
    <rect x="710" y="350" width="180" height="60" rx="3" fill="#ffffff" stroke="#228b22" stroke-width="1"/>
    <text x="800" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" style="white-space: pre;">Transformer Block 4</text>
    <text x="800" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">MHA + FFN + Residual</text>
    <rect x="710" y="420" width="180" height="30" rx="3" fill="#f0fff0" stroke="#228b22" stroke-width="1"/>
    <text x="800" y="440" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" style="white-space: pre;">Contextual Features</text>

    <text x="800" y="490" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#228b22" style="white-space: pre;">Combineheads</text>
    <text x="800" y="510" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#228b22" style="white-space: pre;">Average Pooling</text>
  </g>
  <g id="output-section" transform="matrix(1, 0, 0, 1, -14.953109, -38.047002)" style="transform-origin: 550px 270.265px;">
    <rect x="930" y="50" width="150" height="480" rx="5" fill="url(#outputBg)" stroke="#9370db" stroke-width="2" filter="url(#shadow)"/>
    <text x="1005" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" style="white-space: pre;">Output</text>
    <text x="1005" y="90" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" style="white-space: pre;">Layer</text>

    <!-- Input Section -->
    <text x="1005" y="115" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" style="white-space: pre;">Input</text>
    <rect x="940" y="125" width="130" height="25" rx="3" fill="#f8f8ff" stroke="#9370db" stroke-width="1"/>
    <text x="1005" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" style="white-space: pre;">Fused Features</text>
    <text x="1005" y="150" text-anchor="middle" font-family="Times, serif" font-size="8" fill="black" style="white-space: pre;">ℝ²⁵⁶</text>

    <!-- Action Head Section -->
    <text x="1005" y="175" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" font-weight="bold" style="white-space: pre;">Action Head</text>
    <text x="1005" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#666" style="white-space: pre;">Multi-Layer Perceptron</text>

    <!-- Hidden Layer -->
    <rect x="940" y="200" width="130" height="30" rx="2" fill="#f8f8ff" stroke="#9370db" stroke-width="1"/>
    <text x="1005" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" style="white-space: pre;">Hidden Layer</text>
    <text x="1005" y="227" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">Linear(256 → 128)</text>

    <rect x="940" y="235" width="130" height="20" rx="2" fill="#e6e6fa" stroke="#9370db" stroke-width="1"/>
    <text x="1005" y="250" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">ReLU Activation</text>

    <rect x="940" y="260" width="130" height="20" rx="2" fill="#f5f5f5" stroke="#9370db" stroke-width="1"/>
    <text x="1005" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">Dropout(0.1)</text>

    <!-- Output Layer -->
    <rect x="940" y="285" width="130" height="30" rx="2" fill="#f8f8ff" stroke="#9370db" stroke-width="1"/>
    <text x="1005" y="300" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" style="white-space: pre;">Output Layer</text>
    <text x="1005" y="312" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" style="white-space: pre;">Linear(128 → 2)</text>

    <!-- Action Outputs -->
    <text x="1005" y="330" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" style="white-space: pre;">Control Actions</text>
    <rect x="940" y="340" width="130" height="30" rx="2" fill="#ffb6c1" stroke="#dc143c" stroke-width="1"/>
    <text x="1005" y="355" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="black" style="white-space: pre;">Steering</text>
    <text x="1005" y="367" text-anchor="middle" font-family="Times, serif" font-size="8" fill="black" style="white-space: pre;">aₛₜₑₑᵣ ∈ [-1,1]</text>

    <rect x="940" y="375" width="130" height="30" rx="2" fill="#98fb98" stroke="#32cd32" stroke-width="1"/>
    <text x="1005" y="390" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="black" style="white-space: pre;">Throttle</text>
    <text x="1005" y="402" text-anchor="middle" font-family="Times, serif" font-size="8" fill="black" style="white-space: pre;">aₜₕᵣₒₜₜₗₑ ∈ [-1,1]</text>

    <!-- Vehicle Control Icon -->
    <g transform="translate(1005, 420)">
      <rect x="-20" y="0" width="40" height="15" rx="3" fill="none" stroke="#9370db" stroke-width="1"/>
      <circle cx="-12" cy="15" r="3" fill="none" stroke="#9370db" stroke-width="1"/>
      <circle cx="12" cy="15" r="3" fill="none" stroke="#9370db" stroke-width="1"/>
      <path d="M-5,7 L5,7" stroke="#9370db" stroke-width="2"/>
    </g>

    <!-- Performance Info -->
    <text x="1005" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" font-weight="bold" style="white-space: pre;">Continuous Control</text>
    <text x="1005" y="465" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#666" style="white-space: pre;">Real-time Decision</text>
    <text x="1005" y="480" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#666" style="white-space: pre;">Making</text>

    <!-- Action Range Info -->
    <text x="1005" y="500" text-anchor="middle" font-family="Arial, sans-serif" font-size="8" fill="#666" style="white-space: pre;">Action Space: ℝ²</text>
  </g>
  <line x1="205.05" y1="111.952" x2="225.046" y2="111.952" stroke="#666" stroke-width="2" marker-end="url(#arrow)" style="stroke-width: 2; transform-origin: 535.046px 232.217px;" id="object-1"/>
  <line x1="205.05" y1="391.952" x2="225.046" y2="391.952" stroke="#666" stroke-width="2" marker-end="url(#arrow)" style="stroke-width: 2; transform-origin: 535.046px 232.217px;" id="object-2"/>
  <line x1="305.047" y1="111.952" x2="325.047" y2="111.952" stroke="#666" stroke-width="2" marker-end="url(#arrow)" style="stroke-width: 2; transform-origin: 535.046px 232.217px;" id="object-3"/>
  <line x1="305.047" y1="391.952" x2="325.047" y2="391.952" stroke="#666" stroke-width="2" marker-end="url(#arrow)" style="stroke-width: 2; transform-origin: 535.046px 232.217px;" id="object-4"/>
  <line x1="505.046" y1="161.952" x2="535.046" y2="161.952" stroke="#666" stroke-width="2" marker-end="url(#arrow)" style="stroke-width: 2; transform-origin: 535.046px 232.217px;" id="object-5"/>
  <line x1="505.046" y1="391.952" x2="535.046" y2="391.952" stroke="#666" stroke-width="2" marker-end="url(#arrow)" style="stroke-width: 2; transform-origin: 535.046px 232.217px;" id="object-6"/>
  <line x1="655.047" y1="251.952" x2="685.047" y2="251.953" stroke="#666" stroke-width="2" marker-end="url(#arrow)" style="stroke-width: 2; transform-origin: 535.046px 232.217px;" id="object-7"/>
  <line x1="885.047" y1="251.953" x2="915.047" y2="251.953" stroke="#666" stroke-width="2" marker-end="url(#arrow)" style="stroke-width: 2; transform-origin: 535.046px 232.217px;" id="object-8"/>
  <text x="215.05" y="106.952" text-anchor="middle" font-family="Times, serif" font-size="8" fill="black" style="white-space: pre; stroke-width: 1; font-size: 10px; transform-origin: 535.046px 232.217px;" id="object-9">ℝ⁷</text>
  <text x="215.05" y="386.952" text-anchor="middle" font-family="Times, serif" font-size="8" fill="black" style="white-space: pre; stroke-width: 1; font-size: 10px; transform-origin: 535.046px 232.217px;" id="object-10">ℝ⁷²</text>
  <text x="520.046" y="156.952" text-anchor="middle" font-family="Times, serif" font-size="8" fill="black" style="white-space: pre; stroke-width: 1; font-size: 10px; transform-origin: 535.046px 232.217px;" id="object-11">ℝ¹²⁸</text>
  <text x="520.046" y="386.952" text-anchor="middle" font-family="Times, serif" font-size="8" fill="black" style="white-space: pre; stroke-width: 1; font-size: 10px; transform-origin: 535.046px 232.217px;" id="object-12">ℝ¹²⁸</text>
  <text x="670.046" y="246.952" text-anchor="middle" font-family="Times, serif" font-size="8" fill="black" style="white-space: pre; stroke-width: 1; font-size: 10px; transform-origin: 535.046px 232.217px;" id="object-13">ℝ²⁵⁶</text>
  <text x="900.046" y="246.952" text-anchor="middle" font-family="Times, serif" font-size="8" fill="black" style="white-space: pre; stroke-width: 1; font-size: 10px; transform-origin: 535.046px 232.217px;" id="object-14">ℝ²⁵⁶</text>
</svg>