This is pdfTeX, Version 3.14159265-2.6-1.40.20 (TeX Live 2019/Debian) (preloaded format=pdflatex 2025.6.25)  23 JUL 2025 23:32
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/conference_101719.tex
(/home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/conference_101719.tex
LaTeX2e <2020-02-02> patch level 2
L3 programming layer <2020-02-14> (/usr/share/texlive/texmf-dist/tex/latex/IEEEtran/IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen134
\@IEEEtrantmpdimenB=\dimen135
\@IEEEtrantmpdimenC=\dimen136
\@IEEEtrantmpcountA=\count167
\@IEEEtrantmpcountB=\count168
\@IEEEtrantmpcountC=\count169
\@IEEEtrantmptoksA=\toks14
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 503.
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen137
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen138
\CLASSINFOnormalsizeunitybaselineskip=\dimen139
\IEEEnormaljot=\dimen140
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <5> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <7> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <8> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <9> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <10> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <11> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <11> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <12> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <17> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <17> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <20> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <20> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <24> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <24> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
\IEEEquantizedlength=\dimen141
\IEEEquantizedlengthdiff=\dimen142
\IEEEquantizedtextheightdiff=\dimen143
\IEEEilabelindentA=\dimen144
\IEEEilabelindentB=\dimen145
\IEEEilabelindent=\dimen146
\IEEEelabelindent=\dimen147
\IEEEdlabelindent=\dimen148
\IEEElabelindent=\dimen149
\IEEEiednormlabelsep=\dimen150
\IEEEiedmathlabelsep=\dimen151
\IEEEiedtopsep=\skip47
\c@section=\count170
\c@subsection=\count171
\c@subsubsection=\count172
\c@paragraph=\count173
\c@IEEEsubequation=\count174
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\c@figure=\count175
\c@table=\count176
\@IEEEeqnnumcols=\count177
\@IEEEeqncolcnt=\count178
\@IEEEsubeqnnumrollback=\count179
\@IEEEquantizeheightA=\dimen152
\@IEEEquantizeheightB=\dimen153
\@IEEEquantizeheightC=\dimen154
\@IEEEquantizeprevdepth=\dimen155
\@IEEEquantizemultiple=\count180
\@IEEEquantizeboxA=\box45
\@IEEEtmpitemindent=\dimen156
\IEEEPARstartletwidth=\dimen157
\c@IEEEbiography=\count181
\@IEEEtranrubishbin=\box46
)
** ATTENTION: Overriding command lockouts (line 2).
(/usr/share/texlive/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2020/01/20 v2.17e AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2000/06/29 v2.01 AMS text
 (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks15
\ex@=\dimen158
)) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen159
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2016/03/08 v2.02 operator names
)
\inf@bad=\count182
LaTeX Info: Redefining \frac on input line 227.
\uproot@=\count183
\leftroot@=\count184
LaTeX Info: Redefining \overline on input line 389.
\classnum@=\count185
\DOTSCASE@=\count186
LaTeX Info: Redefining \ldots on input line 486.
LaTeX Info: Redefining \dots on input line 489.
LaTeX Info: Redefining \cdots on input line 610.
\Mathstrutbox@=\box47
\strutbox@=\box48
\big@size=\dimen160
LaTeX Font Info:    Redeclaring font encoding OML on input line 733.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 734.
\macc@depth=\count187
\c@MaxMatrixCols=\count188
\dotsspace@=\muskip16
\c@parentequation=\count189
\dspbrk@lvl=\count190
\tag@help=\toks16
\row@=\count191
\column@=\count192
\maxfields@=\count193
\andhelp@=\toks17
\eqnshift@=\dimen161
\alignsep@=\dimen162
\tagshift@=\dimen163
\tagwidth@=\dimen164
\totwidth@=\dimen165
\lineht@=\dimen166
\@envbody=\toks18
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks19
LaTeX Info: Redefining \[ on input line 2859.
LaTeX Info: Redefining \] on input line 2860.
) (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/share/texlive/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
 (/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2014/09/29 v1.1c Standard LaTeX ifthen package (DPC)
) (/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks20
)
\c@ALC@unique=\count194
\c@ALC@line=\count195
\c@ALC@rem=\count196
\c@ALC@depth=\count197
\ALC@tlm=\skip53
\algorithmicindent=\skip54
) (/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2019/11/30 v1.2a Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2019/11/30 v1.4a Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2016/01/03 v1.10 sin cos tan (DPC)
) (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 105.
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2018/01/08 v1.0l Graphics/color driver for pdftex
))
\Gin@req@height=\dimen167
\Gin@req@width=\dimen168
) (/usr/share/texlive/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
) (/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2016/05/11 v2.12 LaTeX color extensions (UK)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 225.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1348.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1352.
Package xcolor Info: Model `RGB' extended on input line 1364.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1366.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1367.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1370.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1371.
) (/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdfmode.def
File: l3backend-pdfmode.def 2020-02-03 L3 backend support: PDF mode
\l__kernel_color_stack_int=\count198
\l__pdf_internal_box=\box49
) (/home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/conference_101719.aux)
\openout1 = `conference_101719.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.

-- Lines per column: 56 (exact).
(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count199
\scratchdimen=\dimen169
\scratchbox=\box50
\nofMPsegments=\count266
\nofMParguments=\count267
\everyMPshowfont=\toks21
\MPscratchCnt=\count268
\MPscratchDim=\dimen170
\MPnumerator=\count269
\makeMPintoPDFobject=\count270
\everyMPtoPDFconversion=\toks22
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
LaTeX Font Info:    Trying to load font information for U+msa on input line 57.
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 57.
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Calculating math sizes for size <11> on input line 57.


LaTeX Warning: Reference `AA' on page 1 undefined on input line 88.


LaTeX Warning: Reference `SCM' on page 1 undefined on input line 88.


LaTeX Warning: Reference `eq' on page 1 undefined on input line 122.


LaTeX Warning: Reference `eq' on page 1 undefined on input line 122.


LaTeX Warning: Reference `eq' on page 1 undefined on input line 122.


LaTeX Warning: Reference `eq' on page 1 undefined on input line 123.

[1{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}


]
LaTeX Font Info:    Trying to load font information for OT1+pcr on input line 127.
 (/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1pcr.fd
File: ot1pcr.fd 2001/06/04 font definitions for OT1/pcr.
)

LaTeX Warning: Citation `b7' on page 2 undefined on input line 176.


LaTeX Warning: Reference `fig' on page 2 undefined on input line 212.


LaTeX Warning: File `fig1.png' not found on input line 232.


/home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/conference_101719.tex:232: Package pdftex.def Error: File `fig1.png' not found: using draft setting.

See the pdftex.def package documentation for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.232 \centerline{\includegraphics{fig1.png}}
                                             
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

LaTeX Font Info:    Trying to load font information for OMS+ptm on input line 243.
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/omsptm.fd
File: omsptm.fd 
)
LaTeX Font Info:    Font shape `OMS/ptm/m/n' in size <10> not available
(Font)              Font shape `OMS/cmsy/m/n' tried instead on input line 243.
 [2]

LaTeX Warning: Citation `b1' on page 3 undefined on input line 256.


LaTeX Warning: Citation `b2' on page 3 undefined on input line 257.


LaTeX Warning: Citation `b3' on page 3 undefined on input line 258.


LaTeX Warning: Citation `b3' on page 3 undefined on input line 258.


LaTeX Warning: Citation `b3' on page 3 undefined on input line 258.


LaTeX Warning: Citation `b3' on page 3 undefined on input line 259.


LaTeX Warning: Citation `b4' on page 3 undefined on input line 267.


LaTeX Warning: Citation `b5' on page 3 undefined on input line 268.


LaTeX Warning: Citation `b6' on page 3 undefined on input line 273.


** Conference Paper **
Before submitting the final camera ready copy, remember to:

 1. Manually equalize the lengths of two columns on the last page
 of your paper;

 2. Ensure that any PostScript and/or PDF output post-processing
 uses only Type 1 fonts and that every step in the generation
 process uses the appropriate paper size.

[3] (/home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/conference_101719.aux)

LaTeX Warning: There were undefined references.


LaTeX Warning: Label(s) may have changed. Rerun to get cross-references right.

 ) 
Here is how much of TeX's memory you used:
 3969 strings out of 482590
 56916 string characters out of 5953557
 302030 words of memory out of 5000000
 19082 multiletter control sequences out of 15000+600000
 583443 words of font info for 125 fonts, out of 8000000 for 9000
 309 hyphenation exceptions out of 8191
 30i,11n,37p,639b,290s stack positions out of 5000i,500n,10000p,200000b,80000s
{/usr/share/texlive/texmf-dist/fonts/enc/dvips/base/8r.enc}</usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr6.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/courier/ucrr8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmb8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmbi8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmr8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmri8a.pfb>
Output written on /home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/conference_101719.pdf (3 pages, 124013 bytes).
PDF statistics:
 59 PDF objects out of 1000 (max. 8388607)
 42 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

