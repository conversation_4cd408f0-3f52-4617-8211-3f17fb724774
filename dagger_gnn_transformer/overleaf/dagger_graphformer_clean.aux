\relax 
\providecommand{\transparent@use}[1]{}
\citation{kipf2016gcn,velickovic2017gat}
\citation{li2022vehiclegraph}
\citation{vaswani2017attention}
\citation{chen2023drivetransformer}
\citation{zhang2024sensortransformer}
\citation{pomerleau1989alvinn}
\citation{ross2011dagger}
\citation{zhang2023safedagger,liu2024conservative}
\citation{wang2023graphformer}
\providecommand \oddpage@label [2]{}
\@writefile{toc}{\contentsline {section}{\numberline {I}Introduction}{1}\protected@file@percent }
\newlabel{Introduction}{{I}{1}}
\@writefile{toc}{\contentsline {section}{\numberline {II}Related Work}{1}\protected@file@percent }
\newlabel{RelatedWork}{{II}{1}}
\citation{kipf2016gcn}
\@writefile{toc}{\contentsline {section}{\numberline {III}Preliminaries}{2}\protected@file@percent }
\newlabel{Preliminaries}{{III}{2}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-A}}Problem Formulation}{2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-B}}DAGGER Algorithm}{2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-C}}Graph Neural Networks}{2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {IV}Methodology}{2}\protected@file@percent }
\newlabel{Methodology}{{IV}{2}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-A}}The DGT Architecture}{2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}1}Graph-based Vehicle Modeling}{2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}2}Graph Neural Network Processing}{2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}3}Transformer Sensor Processing}{2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-A}4}Feature Fusion and Policy Output}{2}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces DGT Architecture Overview. The framework integrates Graph Neural Networks (GNNs) for modeling spatial vehicle interactions with multi-head Transformer encoders for processing temporal sensor sequences. The graph component captures vehicle-to-vehicle relationships within a 30-meter radius, while the Transformer processes multi-modal sensor data including LiDAR, side detectors, and lane line detectors. The optimized feature fusion layer ensures clean integration of spatial and temporal information.\relax }}{3}\protected@file@percent }
\providecommand*\caption@xref[2]{\@setref\relax\@undefined{#1}}
\newlabel{fig:architecture}{{1}{3}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {IV-B}}DAGGER Training Framework}{3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-B}1}DAGGER with DGT Architecture}{3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-B}2}Beta Scheduling Strategy}{3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {IV-B}3}Training Algorithm}{3}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{\numberline {V}Experimental Results}{3}\protected@file@percent }
\newlabel{Results}{{V}{3}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {V-A}}Experimental Setup}{3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-A}1}Expert Demonstration Collection}{3}\protected@file@percent }
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces DGT Training Algorithm\relax }}{4}\protected@file@percent }
\newlabel{alg:dgt_training}{{1}{4}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-A}2}Environment and Scenario Configuration}{4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-A}3}Model and Training Configuration}{4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {V-A}4}Evaluation Metrics and Baselines}{4}\protected@file@percent }
\@writefile{lot}{\contentsline {table}{\numberline {I}{\ignorespaces Performance Comparison on Test Set (100 Episodes)\relax }}{5}\protected@file@percent }
\newlabel{tab:performance_results}{{I}{5}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {V-B}}Performance Analysis}{5}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces Lane Change Scenario Visualization. The figure demonstrates the DGT model's learned lane changing behavior compared to expert demonstrations. The visualization shows how DAGGER training enables the model to execute smooth lane transitions while maintaining safe distances from surrounding vehicles, closely mimicking expert driving patterns. The learned trajectory demonstrates effective spatial reasoning and safe maneuvering capabilities.\relax }}{5}\protected@file@percent }
\newlabel{fig:lane_change_scenario}{{2}{5}}
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces Overtaking Scenario Visualization. The figure shows DGT trajectory compared with expert policy demonstration in a complex overtaking scenario. NPC vehicles are shown with their trajectories, demonstrating the model's ability to learn expert-like navigation patterns, safely maneuvering around slower traffic while maintaining appropriate lane positioning and spacing.\relax }}{5}\protected@file@percent }
\newlabel{fig:overtaking_scenario}{{3}{5}}
\bibcite{ross2011dagger}{1}
\bibcite{kipf2016gcn}{2}
\bibcite{vaswani2017attention}{3}
\bibcite{li2021metadrive}{4}
\bibcite{pomerleau1989alvinn}{5}
\bibcite{velickovic2017gat}{6}
\bibcite{li2022vehiclegraph}{7}
\bibcite{chen2023drivetransformer}{8}
\bibcite{zhang2024sensortransformer}{9}
\bibcite{zhang2023safedagger}{10}
\bibcite{liu2024conservative}{11}
\bibcite{wang2023graphformer}{12}
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces Temporal Sequence Visualization of DGT Autonomous Driving. The figure shows 12 sequential snapshots captured at 1-second intervals during a complete driving episode. The sequence demonstrates: (0-3s) stable cruising behavior, (3-5s) safe lane changing and overtaking of the first vehicle, (5-7s) return to cruising mode, and (7-10s) successful overtaking of the second vehicle. Each panel shows the ego vehicle (center), surrounding NPC vehicles, road infrastructure, and sensor detection ranges, illustrating the model's consistent spatial awareness and decision-making capabilities throughout the driving task.\relax }}{6}\protected@file@percent }
\newlabel{fig:temporal_sequence}{{4}{6}}
\@writefile{toc}{\contentsline {section}{\numberline {VI}Conclusion}{6}\protected@file@percent }
\newlabel{Conclusion}{{VI}{6}}
\@writefile{toc}{\contentsline {section}{References}{6}\protected@file@percent }
