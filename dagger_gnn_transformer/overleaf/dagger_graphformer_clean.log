This is pdfTeX, Version 3.14159265-2.6-1.40.20 (TeX Live 2019/Debian) (preloaded format=pdflatex 2025.6.25)  23 JUL 2025 23:45
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**/home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/dagger_graphformer_clean.tex
(/home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/dagger_graphformer_clean.tex
LaTeX2e <2020-02-02> patch level 2
L3 programming layer <2020-02-14> (/usr/share/texlive/texmf-dist/tex/latex/IEEEtran/IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen134
\@IEEEtrantmpdimenB=\dimen135
\@IEEEtrantmpdimenC=\dimen136
\@IEEEtrantmpcountA=\count167
\@IEEEtrantmpcountB=\count168
\@IEEEtrantmpcountC=\count169
\@IEEEtrantmptoksA=\toks14
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 503.
(/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen137
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen138
\CLASSINFOnormalsizeunitybaselineskip=\dimen139
\IEEEnormaljot=\dimen140
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <5> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <7> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <8> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <9> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <10> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <11> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <11> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <12> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <17> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <17> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <20> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <20> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <24> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1090.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <24> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1090.
\IEEEquantizedlength=\dimen141
\IEEEquantizedlengthdiff=\dimen142
\IEEEquantizedtextheightdiff=\dimen143
\IEEEilabelindentA=\dimen144
\IEEEilabelindentB=\dimen145
\IEEEilabelindent=\dimen146
\IEEEelabelindent=\dimen147
\IEEEdlabelindent=\dimen148
\IEEElabelindent=\dimen149
\IEEEiednormlabelsep=\dimen150
\IEEEiedmathlabelsep=\dimen151
\IEEEiedtopsep=\skip47
\c@section=\count170
\c@subsection=\count171
\c@subsubsection=\count172
\c@paragraph=\count173
\c@IEEEsubequation=\count174
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\c@figure=\count175
\c@table=\count176
\@IEEEeqnnumcols=\count177
\@IEEEeqncolcnt=\count178
\@IEEEsubeqnnumrollback=\count179
\@IEEEquantizeheightA=\dimen152
\@IEEEquantizeheightB=\dimen153
\@IEEEquantizeheightC=\dimen154
\@IEEEquantizeprevdepth=\dimen155
\@IEEEquantizemultiple=\count180
\@IEEEquantizeboxA=\box45
\@IEEEtmpitemindent=\dimen156
\IEEEPARstartletwidth=\dimen157
\c@IEEEbiography=\count181
\@IEEEtranrubishbin=\box46
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2020/01/20 v2.17e AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(/usr/share/texlive/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2000/06/29 v2.01 AMS text
 (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks15
\ex@=\dimen158
)) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen159
) (/usr/share/texlive/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2016/03/08 v2.02 operator names
)
\inf@bad=\count182
LaTeX Info: Redefining \frac on input line 227.
\uproot@=\count183
\leftroot@=\count184
LaTeX Info: Redefining \overline on input line 389.
\classnum@=\count185
\DOTSCASE@=\count186
LaTeX Info: Redefining \ldots on input line 486.
LaTeX Info: Redefining \dots on input line 489.
LaTeX Info: Redefining \cdots on input line 610.
\Mathstrutbox@=\box47
\strutbox@=\box48
\big@size=\dimen160
LaTeX Font Info:    Redeclaring font encoding OML on input line 733.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 734.
\macc@depth=\count187
\c@MaxMatrixCols=\count188
\dotsspace@=\muskip16
\c@parentequation=\count189
\dspbrk@lvl=\count190
\tag@help=\toks16
\row@=\count191
\column@=\count192
\maxfields@=\count193
\andhelp@=\toks17
\eqnshift@=\dimen161
\alignsep@=\dimen162
\tagshift@=\dimen163
\tagwidth@=\dimen164
\totwidth@=\dimen165
\lineht@=\dimen166
\@envbody=\toks18
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks19
LaTeX Info: Redefining \[ on input line 2859.
LaTeX Info: Redefining \] on input line 2860.
) (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2019/08/31 v2.4l Tabular extension package (FMi)
\col@sep=\dimen167
\ar@mcellbox=\box49
\extrarowheight=\dimen168
\NC@list=\toks20
\extratabsurround=\skip53
\backup@length=\skip54
\ar@cellbox=\box50
) (/usr/share/texlive/texmf-dist/tex/latex/tools/tabularx.sty
Package: tabularx 2020/01/15 v2.11c `tabularx' package (DPC)
\TX@col@width=\dimen169
\TX@old@table=\dimen170
\TX@old@col=\dimen171
\TX@target=\dimen172
\TX@delta=\dimen173
\TX@cols=\count194
\TX@ftn=\toks21
) (/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2019/11/30 v1.2a Enhanced LaTeX Graphics (DPC,SPQR)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks22
) (/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2019/11/30 v1.4a Standard LaTeX Graphics (DPC,SPQR)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2016/01/03 v1.10 sin cos tan (DPC)
) (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 105.
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2018/01/08 v1.0l Graphics/color driver for pdftex
))
\Gin@req@height=\dimen174
\Gin@req@width=\dimen175
) (/usr/share/texlive/texmf-dist/tex/latex/svg/svg.sty
Package: svg 2020/01/13 v2.02e (include SVG pictures)
 (/usr/share/texlive/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2019/11/07 v1.0c TeX engine tests
) (/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrbase.sty
Package: scrbase 2020/01/24 v3.29 KOMA-Script package (KOMA-Script-independent basics and keyval usage)
 (/usr/share/texlive/texmf-dist/tex/latex/koma-script/scrlfile.sty
Package: scrlfile 2020/01/24 v3.29 KOMA-Script package (loading files)
)) (/usr/share/texlive/texmf-dist/tex/latex/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2019/11/24 v0.31 Utility functions of pdfTeX for LuaTeX (HO)
 (/usr/share/texlive/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
) (/usr/share/texlive/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2019/12/15 v1.24 LaTeX kernel commands for general use (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
) (/usr/share/texlive/texmf-dist/tex/latex/tools/shellesc.sty
Package: shellesc 2019/11/08 v1.0c unified shell escape interface for LaTeX
Package shellesc Info: Restricted shell escape enabled on input line 77.
) (/usr/share/texlive/texmf-dist/tex/latex/trimspaces/trimspaces.sty
Package: trimspaces 2009/09/17 v1.1 Trim spaces around a token list
)
\svg@box=\box51
\c@svg@param@lastpage=\count195
\c@svg@param@currpage=\count196
) (/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2016/05/11 v2.12 LaTeX color extensions (UK)
 (/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 225.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1348.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1352.
Package xcolor Info: Model `RGB' extended on input line 1364.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1366.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1367.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1370.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1371.
) (/usr/share/texlive/texmf-dist/tex/latex/transparent/transparent.sty
Package: transparent 2019/11/29 v1.4 Transparency via pdfTeX's color stack (HO)
 (/usr/share/texlive/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)) (/usr/share/texlive/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
) (/usr/share/texlive/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (/usr/share/texlive/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
) (/usr/share/texlive/texmf-dist/tex/latex/algorithm2e/algorithm2e.sty
Package: algorithm2e 2017/07/18 v5.2 algorithms environments
\c@AlgoLine=\count197
 (/usr/share/texlive/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2014/09/29 v1.1c Standard LaTeX ifthen package (DPC)
)
\algocf@hangindent=\skip55
 (/usr/share/texlive/texmf-dist/tex/latex/ifoddpage/ifoddpage.sty
Package: ifoddpage 2016/04/23 v1.1 Conditionals for odd/even page detection
\c@checkoddpage=\count198
) (/usr/share/texlive/texmf-dist/tex/latex/tools/xspace.sty
Package: xspace 2014/10/28 v1.13 Space after command names (DPC,MH)
) (/usr/share/texlive/texmf-dist/tex/latex/relsize/relsize.sty
Package: relsize 2013/03/29 ver 4.1
)
\skiptotal=\skip56
\skiplinenumber=\skip57
\skiprule=\skip58
\skiphlne=\skip59
\skiptext=\skip60
\skiplength=\skip61
\algomargin=\skip62
\skipalgocfslide=\skip63
\algowidth=\dimen176
\inoutsize=\dimen177
\inoutindent=\dimen178
\interspacetitleruled=\dimen179
\interspacealgoruled=\dimen180
\interspacetitleboxruled=\dimen181
\algocf@ruledwidth=\skip64
\algocf@inoutbox=\box52
\algocf@inputbox=\box53
\AlCapSkip=\skip65
\AlCapHSkip=\skip66
\algoskipindent=\skip67
\algocf@nlbox=\box54
\algocf@hangingbox=\box55
\algocf@untilbox=\box56
\algocf@skipuntil=\skip68
\algocf@capbox=\box57
\algocf@lcaptionbox=\skip69
\algoheightruledefault=\skip70
\algoheightrule=\skip71
\algotitleheightruledefault=\skip72
\algotitleheightrule=\skip73
\c@algocfline=\count199
\c@algocfproc=\count266
\c@algocf=\count267
\algocf@algoframe=\box58
\algocf@algobox=\box59
) (/usr/share/texlive/texmf-dist/tex/latex/tools/bm.sty
Package: bm 2019/07/24 v1.2d Bold Symbol Support (DPC/FMi)
\symboldoperators=\mathgroup6
\symboldletters=\mathgroup7
\symboldsymbols=\mathgroup8
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 141.
LaTeX Info: Redefining \bm on input line 209.
) (/usr/share/texlive/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2020/01/03 v3.4h Customizing captions (AR)
 (/usr/share/texlive/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2020/01/03 v1.8h caption3 kernel (AR)
Package caption3 Info: TeX engine: e-TeX on input line 61.
\captionmargin=\dimen182
\captionmargin@=\dimen183
\captionwidth=\dimen184
\caption@tempdima=\dimen185
\caption@indent=\dimen186
\caption@parindent=\dimen187
\caption@hangindent=\dimen188
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\ifx \@captype \@IEEEtablestring \footnotesize \bgroup \par \centering \@IEEEtabletopskipstrut {\normalfont \footnotesize #1}\\{\normalfont \footnotesize \scshape #2}\par \addvspace {0.5\baselineskip }\egroup \@IEEEtablecaptionsepspace \else \@IEEEfigurecaptionsepspace \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace #2}\ifdim \wd \@tempboxa >\hsize \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace }\parbox [t]{\hsize }{\normalfont \footnotesize \noindent \unhbox \@tempboxa #2}\else \ifCLASSOPTIONconference \hbox to\hsize {\normalfont \footnotesize \hfil \box \@tempboxa \hfil }\else \hbox to\hsize {\normalfont \footnotesize \box \@tempboxa \hfil }\fi \fi \fi  on input line 1082.
)

Package caption Warning: Unknown document class (or package),
(caption)                standard defaults will be used.
See the caption package documentation for explanation.

\c@caption@flags=\count268
\c@continuedfloat=\count269
) (/usr/share/texlive/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2020/01/22 v1.3d Sub-captions (AR)
\c@subfigure=\count270
\c@subtable=\count271
) (/usr/share/texlive/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2019/05/31 v2.5 Span multiple rows of a table
\multirow@colwidth=\skip74
\multirow@cntb=\count272
\multirow@dima=\skip75
\bigstrutjot=\dimen189
) (/usr/share/texlive/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen190
\lightrulewidth=\dimen191
\cmidrulewidth=\dimen192
\belowrulesep=\dimen193
\belowbottomsep=\dimen194
\aboverulesep=\dimen195
\abovetopsep=\dimen196
\cmidrulesep=\dimen197
\cmidrulekern=\dimen198
\defaultaddspace=\dimen199
\@cmidla=\count273
\@cmidlb=\count274
\@aboverulesep=\dimen256
\@belowrulesep=\dimen257
\@thisruleclass=\count275
\@lastruleclass=\count276
\@thisrulewidth=\dimen258
) (/usr/share/texlive/texmf-dist/tex/latex/microtype/microtype.sty
Package: microtype 2019/11/18 v2.7d Micro-typographical refinements (RS)
\MT@toks=\toks23
\MT@count=\count277
LaTeX Info: Redefining \textls on input line 790.
\MT@outer@kern=\dimen259
LaTeX Info: Redefining \textmicrotypecontext on input line 1354.
\MT@listname@count=\count278
 (/usr/share/texlive/texmf-dist/tex/latex/microtype/microtype-pdftex.def
File: microtype-pdftex.def 2019/11/18 v2.7d Definitions specific to pdftex (RS)
LaTeX Info: Redefining \lsstyle on input line 914.
LaTeX Info: Redefining \lslig on input line 914.
\MT@outer@space=\skip76
)
Package microtype Info: Loading configuration file microtype.cfg.
 (/usr/share/texlive/texmf-dist/tex/latex/microtype/microtype.cfg
File: microtype.cfg 2019/11/18 v2.7d microtype main configuration file (RS)
)) (/usr/share/texlive/texmf-dist/tex/latex/ragged2e/ragged2e.sty
Package: ragged2e 2019/07/28 v2.2 ragged2e Package (MS)
 (/usr/share/texlive/texmf-dist/tex/latex/ms/everysel.sty
Package: everysel 2011/10/28 v1.2 EverySelectfont Package (MS)
)
\CenteringLeftskip=\skip77
\RaggedLeftLeftskip=\skip78
\RaggedRightLeftskip=\skip79
\CenteringRightskip=\skip80
\RaggedLeftRightskip=\skip81
\RaggedRightRightskip=\skip82
\CenteringParfillskip=\skip83
\RaggedLeftParfillskip=\skip84
\RaggedRightParfillskip=\skip85
\JustifyingParfillskip=\skip86
\CenteringParindent=\skip87
\RaggedLeftParindent=\skip88
\RaggedRightParindent=\skip89
\JustifyingParindent=\skip90
) (/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdfmode.def
File: l3backend-pdfmode.def 2020-02-03 L3 backend support: PDF mode
\l__kernel_color_stack_int=\count279
\l__pdf_internal_box=\box60
) (/home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/dagger_graphformer_clean.aux)
\openout1 = `dagger_graphformer_clean.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 32.
LaTeX Font Info:    ... okay on input line 32.

-- Lines per column: 58 (exact).
(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count280
\scratchdimen=\dimen260
\scratchbox=\box61
\nofMPsegments=\count281
\nofMParguments=\count282
\everyMPshowfont=\toks24
\MPscratchCnt=\count283
\MPscratchDim=\dimen261
\MPnumerator=\count284
\makeMPintoPDFobject=\count285
\everyMPtoPDFconversion=\toks25
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.
LaTeX Info: Redefining \microtypecontext on input line 32.
Package microtype Info: Generating PDF output.
Package microtype Info: Character protrusion enabled (level 2).
Package microtype Info: Using default protrusion set `alltext'.
Package microtype Info: Automatic font expansion enabled (level 2),
(microtype)             stretch: 20, shrink: 20, step: 1, non-selected.
Package microtype Info: Using default expansion set `basictext'.
LaTeX Info: Redefining \showhyphens on input line 32.
Package microtype Info: No adjustment of tracking.
Package microtype Info: No adjustment of interword spacing.
Package microtype Info: No adjustment of character kerning.
 (/usr/share/texlive/texmf-dist/tex/latex/microtype/mt-ptm.cfg
File: mt-ptm.cfg 2006/04/20 v1.7 microtype config. file: Times (RS)
) ABD: EverySelectfont initializing macros
LaTeX Info: Redefining \selectfont on input line 32.
 (/usr/share/texlive/texmf-dist/tex/latex/microtype/mt-cmr.cfg
File: mt-cmr.cfg 2013/05/19 v2.2 microtype config. file: Computer Modern Roman (RS)
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 81.
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
) (/usr/share/texlive/texmf-dist/tex/latex/microtype/mt-msa.cfg
File: mt-msa.cfg 2006/02/04 v1.1 microtype config. file: AMS symbols (a) (RS)
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 81.
 (/usr/share/texlive/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
) (/usr/share/texlive/texmf-dist/tex/latex/microtype/mt-msb.cfg
File: mt-msb.cfg 2005/06/01 v1.0 microtype config. file: AMS symbols (b) (RS)
) [1{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}


]
<architecture_fixed.pdf, id=10, 872.69109pt x 437.85907pt>
File: architecture_fixed.pdf Graphic file (type pdf)
<use architecture_fixed.pdf>
Package pdftex.def Info: architecture_fixed.pdf  used on input line 121.
(pdftex.def)             Requested size: 567.60315pt x 284.7854pt.
 [2]
LaTeX Font Info:    Trying to load font information for OT1+pcr on input line 207.
 (/usr/share/texlive/texmf-dist/tex/latex/psnfss/ot1pcr.fd
File: ot1pcr.fd 2001/06/04 font definitions for OT1/pcr.
)
Package microtype Info: Loading generic protrusion settings for font family
(microtype)             `pcr' (encoding: OT1).
(microtype)             For optimal results, create family-specific settings.
(microtype)             See the microtype manual for details.
 [3 </home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/architecture_fixed.pdf>] [4]
<pic/Lane_change_scenario_overtaking_20250714_194648.png, id=142, 1028.643pt x 237.40695pt>
File: pic/Lane_change_scenario_overtaking_20250714_194648.png Graphic file (type png)
<use pic/Lane_change_scenario_overtaking_20250714_194648.png>
Package pdftex.def Info: pic/Lane_change_scenario_overtaking_20250714_194648.png  used on input line 318.
(pdftex.def)             Requested size: 252.0pt x 58.15977pt.
<pic/overtaking_scenario_overtaking_20250714_155421.png, id=144, 952.5186pt x 169.71405pt>
File: pic/overtaking_scenario_overtaking_20250714_155421.png Graphic file (type png)
<use pic/overtaking_scenario_overtaking_20250714_155421.png>
Package pdftex.def Info: pic/overtaking_scenario_overtaking_20250714_155421.png  used on input line 327.
(pdftex.def)             Requested size: 252.0pt x 44.89891pt.
<pic/dagger_visualization_ep1_20250714_231648.png, id=145, 61.28496pt x 35.02686pt>
File: pic/dagger_visualization_ep1_20250714_231648.png Graphic file (type png)
<use pic/dagger_visualization_ep1_20250714_231648.png>
Package pdftex.def Info: pic/dagger_visualization_ep1_20250714_231648.png  used on input line 338.
(pdftex.def)             Requested size: 490.19843pt x 280.18417pt.
 [5 </home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/pic/Lane_change_scenario_overtaking_20250714_194648.png> </home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/pic/overtaking_scenario_overtaking_20250714_155421.png>] [6 </home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/pic/dagger_visualization_ep1_20250714_231648.png>] (/home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/dagger_graphformer_clean.aux) ) 
Here is how much of TeX's memory you used:
 9331 strings out of 482590
 150681 string characters out of 5953557
 461510 words of memory out of 5000000
 24121 multiletter control sequences out of 15000+600000
 590812 words of font info for 252 fonts, out of 8000000 for 9000
 309 hyphenation exceptions out of 8191
 43i,21n,52p,1226b,647s stack positions out of 5000i,500n,10000p,200000b,80000s
{/usr/share/texlive/texmf-dist/fonts/enc/dvips/base/8r.enc}</usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi5.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi6.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi9.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr5.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr6.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmr9.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy5.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy6.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy9.pfb></usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/symbols/msbm10.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/courier/ucrr8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmb8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmbi8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmr8a.pfb></usr/share/texlive/texmf-dist/fonts/type1/urw/times/utmri8a.pfb>
Output written on /home/<USER>/rl/RL-IL/dagger_gnn_transformer/overleaf/dagger_graphformer_clean.pdf (6 pages, 2273859 bytes).
PDF statistics:
 223 PDF objects out of 1000 (max. 8388607)
 149 compressed objects within 2 object streams
 0 named destinations out of 1000 (max. 500000)
 38421 words of extra memory for PDF output out of 42996 (max. 10000000)

