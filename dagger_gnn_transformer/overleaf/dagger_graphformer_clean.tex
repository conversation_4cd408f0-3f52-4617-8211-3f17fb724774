\documentclass[letterpaper,journal]{IEEEtran}
% Document configuration
\usepackage{amsmath,amssymb}
\usepackage{array}
\usepackage{tabularx}
\usepackage{graphicx}
\usepackage[inkscapelatex=false,inkscapearea=page]{svg} % SVG support for high-quality vector graphics
\usepackage{textcomp}
\usepackage{url}
\usepackage{cite}
\usepackage[linesnumbered,ruled,vlined]{algorithm2e}
\usepackage{bm}
\usepackage{caption}
\usepackage{subcaption}
\usepackage{multirow}
\usepackage{booktabs}
\usepackage{microtype} % 改善排版和断词
\usepackage{ragged2e} % 改善对齐

% Algorithm format configuration
\SetAlFnt{\small}
\SetAlCapFnt{\small}
\SetAlCapNameFnt{\small}
\SetKwInput{KwIn}{Input}
\SetKwInput{KwOut}{Output}

% 设置更宽松的断词规则
% \sloppy
\hyphenpenalty=1000
\exhyphenpenalty=1000

\begin{document}

\title{DGT: A DAGGER-driven Graph-Transformer for Imitation Learning in Autonomous Driving}

\author{Hui~Zhang,~\IEEEmembership{Student Member,~IEEE,}
        and~Research~Team
\thanks{H. Zhang is with the Department of Computer Science, University Research Institute, City, Country (e-mail: <EMAIL>).}
\thanks{This work was supported by the Autonomous Driving Research Grant.}}

\markboth{Journal of Autonomous Driving Research,~Vol.~1, No.~1, 2025}%
{DGT for Autonomous Driving}

\maketitle

\begin{abstract}
Autonomous driving systems require robust decision-making in complex environments with dynamic vehicle interactions. Traditional imitation learning approaches suffer from distribution shift, where learned policies encounter states not present in the expert demonstration dataset, leading to compounding errors during deployment. This paper presents DGT (DAGGER-Graph-Transformer), a novel framework that addresses distribution shift through Dataset Aggregation (DAGGER) combined with a hybrid Graph-Transformer architecture. The architecture integrates Graph Neural Networks for modeling spatial vehicle interactions with Transformers for processing temporal, multi-modal sensor data. A conservative beta scheduling strategy maintains expert policy guidance throughout training, preventing performance degradation in later iterations. Comprehensive experiments in the MetaDrive simulation environment demonstrate superior performance compared to behavioral cloning baselines, with DGT achieving significant improvements in success rates and collision reduction while effectively mitigating distribution shift across different driving scenarios.
\end{abstract}

\begin{IEEEkeywords}
Autonomous driving, imitation learning, DAGGER, graph neural networks, transformer, distribution shift
\end{IEEEkeywords}

\section{Introduction}
\label{Introduction}

Autonomous driving represents one of the most challenging applications of artificial intelligence, requiring robust decision-making in complex, dynamic environments. Traditional rule-based approaches struggle with the complexity and variability of real-world driving scenarios, leading to increased interest in learning-based methods. Imitation learning has emerged as a promising paradigm that leverages expert demonstrations to learn safe driving policies without requiring extensive exploration in potentially dangerous environments.

However, current autonomous driving systems face critical limitations that motivate the DGT framework. Traditional imitation learning suffers from distribution shift, where learned policies encounter states not present in the expert demonstration dataset, leading to compounding errors during deployment. To address this challenge, the Dataset Aggregation (DAGGER) algorithm is implemented to iteratively collect data under the current learned policy and aggregate it with expert demonstrations, ensuring policies learn to handle self-generated states. Existing neural network architectures fail to effectively capture spatial relationships between vehicles and struggle with variable numbers of surrounding vehicles while integrating temporal sensor processing. Therefore, the Graph-Transformer architecture is proposed, combining Graph Neural Networks for modeling spatial vehicle interactions with Transformer encoders for processing sequential multi-modal sensor data.

The DGT framework introduces three key innovations: (1) Distribution shift mitigation through DAGGER with conservative beta scheduling that maintains expert guidance throughout training; (2) Spatial structure modeling via Graph Neural Networks that capture vehicle relationships through message passing mechanisms; (3) Temporal dependency processing via Transformer architecture that handles sequential multi-modal sensor data including LiDAR, side detectors, and lane line detectors.

This work contributes to autonomous driving research by demonstrating how spatial-temporal modeling can be effectively integrated within an imitation learning framework, providing a comprehensive solution that addresses both distribution shift and architectural limitations while achieving significant improvements in safety and navigation performance compared to traditional behavioral cloning methods.

\section{Related Work}
\label{RelatedWork}

Graph Neural Networks in Autonomous Driving: While Graph Neural Networks (GNNs) \cite{kipf2016gcn,velickovic2017gat} excel at modeling spatial vehicle interactions in driving scenarios \cite{li2022vehiclegraph}, they often lack effective integration with temporal sensor processing.

Transformer Architectures for Sequential Data: Transformers \cite{vaswani2017attention} have shown promise in autonomous driving for spatial-temporal feature processing \cite{chen2023drivetransformer} and multi-modal sensor fusion \cite{zhang2024sensortransformer}, but struggle with variable-sized vehicle interactions and spatial relationship modeling.

Imitation Learning and Distribution Shift: Behavioral cloning \cite{pomerleau1989alvinn} suffers from distribution shift when learned policies encounter states absent from training data. DAGGER \cite{ross2011dagger} addresses this by iteratively collecting data under current policies and aggregating with expert demonstrations, with recent variants maintaining safety constraints \cite{zhang2023safedagger,liu2024conservative}.

Hybrid Architectures: Recent research has explored combining different neural architectures for autonomous driving. Wang et al. \cite{wang2023graphformer} proposed Graph-Transformer for molecular property prediction, demonstrating the effectiveness of GNN-Transformer hybrids. However, no prior work has specifically addressed the integration of GNNs and Transformers for autonomous driving with DAGGER training to mitigate distribution shift.

\section{Preliminaries}
\label{Preliminaries}

\subsection{Problem Formulation}

Autonomous driving is formulated as MDP $\langle \mathcal{S}, \mathcal{A}, \mathcal{P}, \mathcal{R}, \gamma \rangle$ where $\mathcal{S}$ represents states, $\mathcal{A}$ actions, $\mathcal{P}$ transitions, $\mathcal{R}$ rewards, and $\gamma$ discount factor.

The environment is a vehicle interaction graph $G = (V, E)$ where $V$ includes ego and surrounding vehicles, $E$ encodes spatial relationships.

State Space: The state space $\mathcal{S}$ consists of three key components. First, the ego vehicle state $s_{ego} \in \mathbb{R}^7$ contains position $(x, y)$, velocity $(v_x, v_y)$, heading angle $\theta$, and acceleration $(a_x, a_y)$. Second, the vehicle interaction graph $G_t = (V_t, E_t, X_t)$ represents the spatial relationships among vehicles, where $V_t$ represents vehicles within 30m radius, $E_t$ encodes spatial proximity relationships, and $X_t \in \mathbb{R}^{|V_t| \times 7}$ contains vehicle state features. Third, multi-modal sensor data $S_t \in \mathbb{R}^{72}$ concatenates LiDAR readings (30 dimensions, 50m range), side detector measurements (30 dimensions), and lane line detector outputs (12 dimensions).
The complete state is defined as $s_t = (s_{ego}, G_t, S_t)$, providing comprehensive environmental awareness for decision-making.

Action Space: Continuous control commands:
\begin{equation}
a_t = (a_t^{\text{steer}}, a_t^{\text{throttle}}) \in [-1, 1]^2
\end{equation}

\subsection{DAGGER Algorithm}

DAGGER addresses distribution shift through iterative aggregation. Let $\pi^*$ denote expert policy, $\pi_\theta$ learned policy. The algorithm begins by initializing dataset $\mathcal{D}_0$ with expert demonstrations. For iterations $i = 1, 2, ..., N$, the algorithm executes $\pi_{\theta_{i-1}}$ to collect trajectory $\tau_i$, queries the expert for optimal actions at states in $\tau_i$, aggregates the data as $\mathcal{D}_i = \mathcal{D}_{i-1} \cup \{(s, \pi^*(s)) : s \in \tau_i\}$, and trains a new policy $\pi_{\theta_i}$ on $\mathcal{D}_i$.

DAGGER provides theoretical guarantees:
\begin{equation}
\mathbb{E}[\text{cost}(\pi_{\theta_N})] \leq \mathbb{E}[\text{cost}(\pi^*)] + O(\epsilon_N)
\end{equation}

\subsection{Graph Neural Networks}

Our implementation uses Graph Convolutional Networks (GCNs) \cite{kipf2016gcn} to learn spatial representations by aggregating features from neighboring nodes. The layer-wise propagation rule is defined as:
\begin{equation}
h_i^{(l+1)} = \sigma\left(W^{(l)} \sum_{j \in \mathcal{N}(i) \cup \{i\}} \frac{h_j^{(l)}}{\sqrt{|\mathcal{N}(i)||\mathcal{N}(j)|}}\right)
\end{equation}
The GNN module also includes residual connections, graph normalization, and dropout for improved training stability.

\section{Methodology}
\label{Methodology}

Our approach combines DAGGER training with a novel hybrid Graph-Transformer architecture that integrates GNNs for spatial vehicle modeling with Transformers for temporal sensor processing, addressing both distribution shift and architectural limitations in autonomous driving.

\subsection{The DGT Architecture}

The DGT architecture processes spatial vehicle interactions as dynamic graphs while employing multi-head Transformer encoders for temporal sensor sequence processing. The hybrid design enables effective modeling of both spatial relationships between vehicles and temporal dependencies in sensor data. Figure \ref{fig:architecture} illustrates the complete DGT architecture, showing the integration of graph neural networks for spatial vehicle modeling with Transformer encoders for temporal sensor processing.

\begin{figure*}[!t]
\centering
\makebox[\textwidth]{\includegraphics[width=1.1\textwidth]{architecture_fixed.pdf}}
\caption{DGT Architecture Overview. The framework integrates Graph Neural Networks (GNNs) for modeling spatial vehicle interactions with multi-head Transformer encoders for processing temporal sensor sequences. The graph component captures vehicle-to-vehicle relationships within a 30-meter radius, while the Transformer processes multi-modal sensor data including LiDAR, side detectors, and lane line detectors. The optimized feature fusion layer ensures clean integration of spatial and temporal information.}
\label{fig:architecture}
\end{figure*}

\subsubsection{Graph-based Vehicle Modeling}

The driving environment is represented as a dynamic vehicle interaction graph $G_t = (V_t, E_t, X_t)$ at each timestep $t$, where $V_t$ represents the set of vehicles within the 30-meter sensing radius including the ego vehicle and surrounding traffic, $E_t$ captures spatial proximity relationships based on Euclidean distance thresholds, and $X_t$ contains the node feature matrix with vehicle state information.

Graph construction follows a systematic approach. Each vehicle is represented as a node with 7-dimensional features $[x, y, v_x, v_y, \theta, a_x, a_y]$, where $(x, y)$ represents position, $(v_x, v_y)$ represents velocity components, $\theta$ represents heading angle, and $(a_x, a_y)$ represents acceleration components. Proximity-based edges are established between vehicles within the 30-meter sensing range, creating a sparse adjacency matrix that captures local spatial relationships. The graph structure is dynamically updated at each timestep to reflect changes in vehicle positions and the appearance/disappearance of vehicles within the sensing range.

\subsubsection{Graph Neural Network Processing}

The implementation uses GCNs for learning spatial representations. The GCN processes node features according to the propagation rule defined in Equation (3). The GNN module includes residual connections, graph normalization, and dropout for improved training stability.

\subsubsection{Transformer Sensor Processing}

Multi-modal sensor data is processed through a multi-head Transformer encoder. The sensor input consists of concatenated features from LiDAR (30 lasers), side detectors (30 lasers), and lane line detectors (12 lasers), forming a 72-dimensional sequence feature vector. The Transformer processes this sequential data via multi-head attention:
\begin{equation}
\text{MultiHead}(Q, K, V) = \text{Concat}(\text{head}_1, ..., \text{head}_h)W^O
\end{equation}

where each attention head computes:
\begin{equation}
\text{head}_i = \text{Attention}(QW_i^Q, KW_i^K, VW_i^V)
\end{equation}

\subsubsection{Feature Fusion and Policy Output}

The integrated architecture combines spatial and temporal features through a carefully designed fusion mechanism. Spatial features from the GNN are aggregated using global mean pooling and projected to half the Transformer dimension, while sequential sensor features are linearly projected to half the Transformer dimension. Graph and sensor features are then concatenated and passed through a feature fusion layer with layer normalization and ReLU activation. The fused features undergo multi-layer Transformer encoding with 4 layers, 8 attention heads, and 256 hidden dimensions. Finally, a two-layer MLP head outputs continuous actions for steering and throttle control.

The final policy output is computed as:
\begin{equation}
\pi_\theta(a|s) = \text{ActionHead}(\text{Transformer}([\text{GNN}(G_t); \text{Proj}(S_t)]))
\end{equation}

where the action space consists of continuous control commands $a_t = (a_t^{\text{steer}}, a_t^{\text{throttle}}) \in [-1, 1]^2$.

\subsection{DAGGER Training Framework}

The DAGGER framework leverages the hybrid DGT architecture to address distribution shift through iterative data aggregation.

\subsubsection{DAGGER with DGT Architecture}

DAGGER adapts to the architecture, iteratively collecting data and aggregating with expert demonstrations.

Training Objective:
At iteration $i$, policy $\pi_{\theta_i}$ minimizes supervised loss:
\begin{equation}
\mathcal{L}_{DAGGER} = \mathbb{E}_{(s,a) \sim \mathcal{D}_i} \left[ \|a - \pi_{\theta_i}(s)\|^2 \right]
\end{equation}

The policy network consists of a GNN encoder for spatial vehicle modeling, a Transformer encoder for temporal sensor processing, feature fusion layers combining representations, and a policy head for continuous actions.

DAGGER provides several advantages over traditional behavioral cloning. It addresses distribution shift via iterative aggregation, enables better vehicle interaction modeling through graphs, improves sensor processing via attention mechanisms, and enhances robustness through training on policy-generated states.

\subsubsection{Beta Scheduling Strategy}
Beta scheduling controls expert-policy mixture:
\begin{equation}
a_t = \begin{cases}
a_{expert} & \text{with probability } \beta_i \\
a_{policy} \sim \pi_{\theta_i}(s_t) & \text{with probability } 1 - \beta_i
\end{cases}
\end{equation}
We adopt a conservative scheduling strategy that uses a slower exponential decay to ensure that the expert policy maintains dominance during the early and middle stages of training (with higher $\beta$ values), preventing the policy from deviating prematurely from expert trajectories and falling into difficult-to-recover states. This conservative approach is formulated as:
\begin{equation}
\beta_i = \max(\beta_{min}, \beta_{max} \cdot e^{-\alpha \cdot i})
\end{equation}
where the decay factor $\alpha$ is set to a small value to ensure gradual transition from expert guidance to policy autonomy.
\subsubsection{Training Algorithm}

\begin{algorithm}[!t]
\SetAlgoLined
\caption{DGT Training Algorithm}
\label{alg:dgt_training}

\KwIn{
    Expert policy $\pi^*$;
    MetaDrive environment $\mathcal{E}$;
    Number of DAGGER iterations $N$;
    Episodes per iteration $M$;
    Initial expert dataset $\mathcal{D}_0$;
    Beta schedule parameters $\beta_{min}, \beta_{max}, \alpha$;
}
\KwOut{Trained DGT policy $\pi_{\theta_N}$}

\tcp{Initialize DGT policy with pre-training on expert data}
Initialize DGT policy $\pi_{\theta_0}$ with random weights\;
Train $\pi_{\theta_0}$ on $\mathcal{D}_0$ by minimizing $\mathcal{L}_{DAGGER}$\;

\For{$i = 1$ to $N$}{
    Initialize temporary buffer $\mathcal{B}_i = \emptyset$\;

    \tcp{Step 1: On-policy data collection with expert intervention}
    \For{$j = 1$ to $M$}{
        Reset environment $\mathcal{E}$ to get initial state $s_0$\;
        \For{$t = 0$ to T-1}{
            \tcp{Observe graph and sensor data}
            Observe current state $s_t = (G_t, S_t)$ from $\mathcal{E}$\;

            \tcp{Beta scheduling for action selection}
            \If{$\text{random}() < \beta_i$}{
                $a_t = \pi^*(s_t)$ \tcp*{Expert action}
            }
            \Else{
                $a_t = \pi_{\theta_{i-1}}(G_t, S_t)$ \tcp*{Policy action using DGT}
            }

            Execute action $a_t$ in $\mathcal{E}$ to get next state $s_{t+1}$\;

            \tcp{Query expert for the optimal action label}
            $a_t^* = \pi^*(s_t)$\;
            Add $(s_t, a_t^*)$ to buffer $\mathcal{B}_i$\;
        }
    }

    \tcp{Step 2: Aggregate datasets}
    $\mathcal{D}_i = \mathcal{D}_{i-1} \cup \mathcal{B}_i$\;

    \tcp{Step 3: Train the DGT policy on aggregated data}
    Update policy $\pi_{\theta_i}$ by training on $\mathcal{D}_i$ to minimize $\mathcal{L}_{DAGGER}$\;

    \tcp{Step 4: Update beta for next iteration}
    Update $\beta_i$ using conservative schedule: $\beta_i = \max(\beta_{min}, \beta_{max} \cdot e^{-\alpha \cdot i})$\;
}
\Return{$\pi_{\theta_N}$}
\end{algorithm}

\section{Experimental Results}
\label{Results}

DGT is evaluated comprehensively, validating effectiveness across performance, safety, and distribution shift mitigation.

\subsection{Experimental Setup}

\subsubsection{Expert Demonstration Collection}

A modular pipeline architecture is employed to generate high-quality demonstrations in MetaDrive. The pipeline consists of three main components: perception through LiDAR sensors that acquire environmental information, decision-making via rule-based planning considering topology and safety, and control that converts decisions to steering and throttle commands.

For graph data collection, we represent nodes as vehicles with position, velocity, heading, and acceleration information, edges as spatial proximity relationships within 30 meters, and sensors including LiDAR (30 lasers), side detectors (30), and lane detectors (12).

The collected dataset is formally defined as:
\begin{equation}
\mathcal{D}=\{(G_t, S_t, a_t)\}_{t=1}^N
\end{equation}
where $G_t = (V_t, E_t, X_t)$ is the vehicle interaction graph at timestep $t$ with nodes $V_t$ representing vehicles, edges $E_t$ encoding spatial relationships, and node features $X_t \in \mathbb{R}^{|V_t| \times 7}$ containing vehicle states; $S_t \in \mathbb{R}^{72}$ represents concatenated multi-modal sensor data including LiDAR readings (30 dimensions), side detector measurements (30 dimensions), and lane line detector outputs (12 dimensions); and $a_t \in [-1,1]^2$ denotes the expert action consisting of steering and throttle commands.
\subsubsection{Environment and Scenario Configuration}

Comprehensive experiments are conducted in the MetaDrive simulation environment, which provides realistic autonomous driving scenarios. The environment configuration uses "SSSSS" map configuration (straight road segments) with 10 different scenarios, traffic density of 0.1 to ensure manageable but realistic traffic conditions, maximum episode length of 1000 timesteps per episode, and NPC vehicle speed control with slow traffic configuration limited to 10 km/h maximum speed for controlled evaluation. The test scenarios are generated using seeds from 11 to 111, providing 100 diverse driving scenarios that ensure comprehensive evaluation across different traffic patterns and road conditions.

Each vehicle is equipped with multi-modal sensors: (1) LiDAR sensor with 30 laser beams and 50-meter detection range providing distance measurements; (2) Side detectors with 30 laser beams for lateral obstacle detection; (3) Lane line detectors with 12 laser beams for road boundary detection. The concatenated sensor data forms a 72-dimensional feature vector ($30 + 30 + 12 = 72$) processed by the Transformer encoder.

\subsubsection{Model and Training Configuration}

The DGT model is configured with a GNN component featuring 2 layers with 128 hidden dimensions using GCNs, a Transformer component with 4 layers, 8 attention heads and 256 hidden dimensions, input processing of 7D node features for vehicles and 72D sensor sequence features, output generation of 2D continuous action space for steering and throttle control, and dropout rate of 0.1 for regularization.

The training configuration employs a learning rate of $3 \times 10^{-4}$ with cosine annealing scheduler, batch size of 128 samples per training batch, 10 training epochs per DAGGER iteration, weight decay of $1 \times 10^{-4}$ for L2 regularization, and buffer size of 20,000 samples for experience replay.

The DAGGER configuration involves 100 total iterations, 10 episodes per iteration for data collection, 10 parallel workers for efficient data collection, conservative beta scheduling with $\beta_{min} = 0.5$, $\beta_{max} = 1.0$, and decay factor $\alpha = 0.1$, and IDM (Intelligent Driver Model) as the expert policy for realistic expert demonstrations.

\subsubsection{Evaluation Metrics and Baselines}

Performance is evaluated using four key safety and navigation metrics: success rate (percentage of episodes successfully reaching the destination), collision rate (percentage of episodes ending due to vehicle collisions), off-road rate (percentage of episodes ending due to lane departures or off-road incidents), and route completion (average percentage of route completed before episode termination).

The evaluation includes the expert policy (IDM-based expert policy serving as the theoretical upper bound), our DGT ($\beta=0.5$) (the proposed approach with conservative beta scheduling), BC+GNN+Transformer (behavioral cloning using the Graph-Transformer architecture, $\beta=1.0$), DAGGER without GNN (DAGGER training without graph neural network components), and BC+MLP (behavioral cloning with multi-layer perceptron). All methods are evaluated on a comprehensive test set of 100 episodes to ensure statistical significance and robust performance assessment.

\subsection{Performance Analysis}

As shown in Table \ref{tab:performance_results}, our DGT significantly outperforms all baselines across key metrics. It achieves an 83.0\% success rate, approaching the expert's 97.3\% route completion and drastically reducing the collision rate to 13.0\%.

The results highlight the importance of both our architecture and training strategy. The simplest BC+MLP baseline performs poorly (36.0\% success, 53.0\% collision), confirming the need for sophisticated spatial-temporal modeling. Incorporating our hybrid architecture within standard behavioral cloning (BC+GT) substantially improves performance (72.0\% success), yet still suffers from distribution shift. Applying DAGGER without the GNN component (w/o GNN) is insufficient, as the success rate drops to 47.0\%, underscoring the GNN's critical role in modeling vehicle interactions. Ultimately, the combination of the Graph-Transformer architecture and the DAGGER training framework in our proposed method proves most effective, validating our core hypothesis.

\begin{table}[!t]
\centering
\scriptsize
\begin{tabularx}{\columnwidth}{@{}l*{5}{X}@{}}
\toprule
\textbf{Metric} & \textbf{Expert} & \textbf{DGT} & \textbf{BC+GT} & \textbf{w/o GNN} & \textbf{BC+MLP} \\
\midrule
Success (\%) & 100.0 & 83.0 & 72.0 & 47.0 & 36.0 \\
Collision (\%) & 0.0 & 13.0 & 18.0 & 43.0 & 53.0 \\
Off-road (\%) & 0.0 & 4.0 & 10.0 & 10.0 & 11.0 \\
Completion (\%) & 97.3 & 94.0 & 88.7 & 81.7 & 71.5 \\
\bottomrule
\end{tabularx}
\caption{Performance Comparison on Test Set (100 Episodes). Expert: IDM-based expert policy. DGT ($\beta=0.5$), BC+GT = Behavioral Cloning with GNN+Transformer ($\beta=1.0$), w/o GNN = DAGGER without GNN components, BC+MLP = Behavioral Cloning with MLP.}
\label{tab:performance_results}
\end{table}

Beyond quantitative metrics, qualitative analysis through driving scenario visualization provides deeper insights into the learned behaviors. Two critical driving scenarios are examined in order of increasing difficulty: lane changing and overtaking, comparing DGT trajectories with expert demonstrations to validate the quality of learned policies.

Figure \ref{fig:lane_change_scenario} presents a lane changing scenario comparing the model's learned behavior with expert demonstrations. The visualization captures the decision-making process during lane changes, showing how the DGT model learns to coordinate lateral and longitudinal movements while considering the positions and velocities of surrounding vehicles. The learned trajectory closely follows the expert's smooth and safe lane transition strategy, demonstrating effective imitation of expert driving behaviors.

\begin{figure}[!t]
\centering
\includegraphics[width=\columnwidth]{pic/Lane_change_scenario_overtaking_20250714_194648.png}
\caption{Lane Change Scenario Visualization. The figure demonstrates the DGT model's learned lane changing behavior compared to expert demonstrations. The visualization shows how DAGGER training enables the model to execute smooth lane transitions while maintaining safe distances from surrounding vehicles, closely mimicking expert driving patterns. The learned trajectory demonstrates effective spatial reasoning and safe maneuvering capabilities.}
\label{fig:lane_change_scenario}
\end{figure}

Figure \ref{fig:overtaking_scenario} illustrates a more complex overtaking scenario comparing DGT trajectories with expert demonstrations. The visualization shows the complete spatial context including road infrastructure (lane markings, road boundaries), NPC vehicle positions and trajectories, and the comparative paths taken by the DGT model versus the expert policy. The learned trajectory demonstrates effective spatial planning, maintaining safe distances while executing necessary lane changes to overtake slower vehicles, closely following the expert's navigation strategy.

\begin{figure}[!t]
\centering
\includegraphics[width=\columnwidth]{pic/overtaking_scenario_overtaking_20250714_155421.png}
\caption{Overtaking Scenario Visualization. The figure shows DGT trajectory compared with expert policy demonstration in a complex overtaking scenario. NPC vehicles are shown with their trajectories, demonstrating the model's ability to learn expert-like navigation patterns, safely maneuvering around slower traffic while maintaining appropriate lane positioning and spacing.}
\label{fig:overtaking_scenario}
\end{figure}

These qualitative results complement the quantitative analysis by demonstrating that DGT successfully learns expert-like driving behaviors. The visualizations reveal that the model effectively imitates expert demonstrations, learning to balance multiple objectives: maintaining progress toward the destination, ensuring safety through appropriate spacing, and executing maneuvers smoothly. The close alignment between learned and expert trajectories validates the effectiveness of DAGGER training in capturing complex driving strategies and spatial reasoning capabilities.

To provide a comprehensive view of the model's autonomous driving capabilities, Figure \ref{fig:temporal_sequence} presents a temporal sequence visualization showing the model's performance over a complete driving episode. The 12-panel visualization captures key moments at 1-second intervals, demonstrating the model's ability to handle complex multi-vehicle scenarios including cruising, lane changing, and overtaking maneuvers.

\begin{figure*}[!t]
\centering
\includegraphics[width=0.95\textwidth]{pic/dagger_visualization_ep1_20250714_231648.png}
\caption{Temporal Sequence Visualization of DGT Autonomous Driving. The figure shows 12 sequential snapshots captured at 1-second intervals during a complete driving episode. The sequence demonstrates: (0-3s) stable cruising behavior, (3-5s) safe lane changing and overtaking of the first vehicle, (5-7s) return to cruising mode, and (7-10s) successful overtaking of the second vehicle. Each panel shows the ego vehicle (center), surrounding NPC vehicles, road infrastructure, and sensor detection ranges, illustrating the model's consistent spatial awareness and decision-making capabilities throughout the driving task.}
\label{fig:temporal_sequence}
\end{figure*}

The temporal sequence analysis reveals several key aspects of the learned driving behavior: (1) Consistent spatial awareness throughout the episode, with the model maintaining appropriate following distances and lane positioning; (2) Smooth execution of complex maneuvers, including lane changes and overtaking operations that require coordination of lateral and longitudinal control; (3) Adaptive decision-making based on traffic conditions, demonstrating the model's ability to assess when overtaking is safe and necessary; (4) Stable return to cruising behavior after completing maneuvers, indicating proper understanding of driving context and objectives.



\section{Conclusion}
\label{Conclusion}

This paper presented DGT addressing distribution shift in autonomous driving imitation learning. Our approach integrates DAGGER with hybrid Graph-Transformer combining GNNs for spatial vehicle modeling with Transformers for temporal sensor processing.

Key Achievements: The framework establishes a new paradigm for autonomous driving imitation learning by successfully integrating spatial-temporal modeling with distribution shift mitigation, achieving 83.0\% success rate (83\% of expert-level performance) while reducing collision rates to 13.0\%. This work demonstrates the potential for graph-based architectures to advance safe autonomous driving systems.

Experimental Validation: Comprehensive experiments in MetaDrive validate our approach. The proposed DGT significantly outperforms baselines, including variants without DAGGER or GNN components. This confirms that mitigating distribution shift via DAGGER and effectively modeling spatial-temporal dependencies via Graph-Transformer are both critical for achieving robust driving policies.

Future Directions: (1) Complex scenarios with pedestrians; (2) Real-world sensor integration; (3) Alternative GNN architectures; (4) Adaptive beta scheduling; (5) Multi-agent DAGGER training.

Impact: This work contributes to safer autonomous driving by addressing fundamental imitation learning challenges with potential applications in various autonomous platforms.



\begin{thebibliography}{99}

\bibitem{ross2011dagger}
S. Ross, G. Gordon, and D. Bagnell, ``A reduction of imitation learning and structured prediction to no-regret online learning,'' \textit{Proc. AISTATS}, 2011.

\bibitem{kipf2016gcn}
T. N. Kipf and M. Welling, ``Semi-supervised classification with graph convolutional networks,'' \textit{Proc. ICLR}, 2017.

\bibitem{vaswani2017attention}
A. Vaswani et al., ``Attention is all you need,'' \textit{Proc. NeurIPS}, 2017.

\bibitem{li2021metadrive}
Q. Li et al., ``MetaDrive: Composing diverse driving scenarios for generalizable reinforcement learning,'' \textit{IEEE Trans. Pattern Anal. Mach. Intell.}, vol. 44, no. 12, pp. 8967-8982, 2022.

\bibitem{pomerleau1989alvinn}
D. A. Pomerleau, ``ALVINN: An autonomous land vehicle in a neural network,'' \textit{Proc. NeurIPS}, 1989.

\bibitem{velickovic2017gat}
P. Veli\v{c}kovi\'{c} et al., ``Graph attention networks,'' \textit{Proc. ICLR}, 2018.

\bibitem{li2022vehiclegraph}
Y. Li et al., ``VehicleGraph: Learning vehicle interactions for autonomous driving,'' \textit{Proc. IEEE Int. Conf. Robot. Autom.}, pp. 3456-3463, 2022.

\bibitem{chen2023drivetransformer}
X. Chen et al., ``DriveTransformer: Multi-modal transformer for autonomous driving,'' \textit{Proc. IEEE Conf. Comput. Vis. Pattern Recognit.}, pp. 1234-1243, 2023.

\bibitem{zhang2024sensortransformer}
L. Zhang et al., ``SensorTransformer: Multi-modal sensor fusion with transformers,'' \textit{IEEE Trans. Robot.}, vol. 40, no. 3, pp. 567-580, 2024.

\bibitem{zhang2023safedagger}
M. Zhang et al., ``SafeDAgger: Safe dataset aggregation for imitation learning,'' \textit{Proc. NeurIPS}, pp. 2345-2356, 2023.

\bibitem{liu2024conservative}
J. Liu et al., ``Conservative DAGGER: Maintaining safety in iterative imitation learning,'' \textit{Proc. Int. Conf. Mach. Learn.}, pp. 4567-4578, 2024.

\bibitem{wang2023graphformer}
H. Wang et al., ``Graph-Transformer: Transformer-based graph neural networks,'' \textit{Proc. ICLR}, 2023.

\end{thebibliography}

\end{document}
