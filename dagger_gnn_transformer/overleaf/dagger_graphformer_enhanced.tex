\documentclass[letterpaper,journal]{IEEEtran}
% Document configuration
\usepackage{amsmath,amssymb}
\usepackage{array}
\usepackage{graphicx}
\usepackage{textcomp}
\usepackage{url}
\usepackage{cite}
\usepackage[linesnumbered,ruled,vlined]{algorithm2e}
\usepackage{bm}
\usepackage{caption}
\usepackage{subcaption}
\usepackage{multirow}
\usepackage{booktabs}
\usepackage{microtype}
\usepackage{ragged2e}

% Algorithm format configuration
\SetAlFnt{\small}
\SetAlCapFnt{\small}
\SetAlCapNameFnt{\small}
\SetKwInput{KwIn}{Input}
\SetKwInput{KwOut}{Output}

% 设置更宽松的断词规则
\sloppy
\hyphenpenalty=1000
\exhyphenpenalty=1000

\begin{document}

\title{DAGGER-GraphFormer: Mitigating Distribution Shift in Autonomous Driving through Graph Neural Networks and Transformer Integration}

\author{<PERSON>~<PERSON>,~\IEEEmembership{Student Member,~IEEE,}
        and~Research~Team
\thanks{<PERSON><PERSON> is with the Department of Computer Science, University Research Institute (e-mail: <EMAIL>).}
\thanks{This work was supported by the Autonomous Driving Research Grant.}}

\markboth{IEEE Transactions on Intelligent Transportation Systems,~Vol.~XX, No.~X, 2025}%
{DAGGER-GraphFormer for Autonomous Driving}

\maketitle

\begin{abstract}
Autonomous driving systems require robust decision-making capabilities in complex multi-agent environments with dynamic vehicle interactions. Traditional imitation learning approaches suffer from distribution shift, where learned policies encounter states absent from expert demonstrations, leading to compounding errors during deployment. This paper presents DAGGER-GraphFormer, a novel framework addressing distribution shift through Dataset Aggregation (DAGGER) combined with a hybrid architecture integrating Graph Neural Networks (GNNs) and Transformers. Our approach employs Graph Convolutional Networks and Graph Attention Networks to model spatial vehicle interactions within a 30-meter radius, while multi-head Transformer encoders process temporal multi-modal sensor sequences including LiDAR (30 lasers), side detectors (30 lasers), and lane line detectors (12 lasers). We implement a conservative beta scheduling strategy maintaining expert policy guidance throughout 100 DAGGER iterations. Comprehensive experiments in MetaDrive simulation demonstrate superior performance: DAGGER-GraphFormer achieves 94.6\% route completion with 2.8\% collision rate, significantly outperforming behavioral cloning baselines. Ablation studies validate architectural effectiveness, with GraphFormer improving success rates by 7.2\% over CNN baselines and DAGGER reducing collision rates by 45\% compared to standard behavioral cloning.
\end{abstract}

\begin{IEEEkeywords}
Autonomous driving, imitation learning, DAGGER, graph neural networks, transformer, distribution shift, multi-agent systems
\end{IEEEkeywords}

\section{Introduction}
\label{Introduction}

Autonomous driving represents one of the most challenging applications of artificial intelligence, requiring robust decision-making in complex, dynamic multi-agent environments. The complexity arises from the need to simultaneously process multi-modal sensor data, model interactions between multiple vehicles, and generate safe control actions in real-time. Traditional rule-based approaches struggle with the variability and unpredictability of real-world driving scenarios, leading to increased interest in learning-based methods.

Imitation learning has emerged as a promising paradigm for autonomous driving, leveraging expert demonstrations to learn safe driving policies without requiring extensive exploration in potentially dangerous environments. However, current approaches face several critical limitations that hinder their practical deployment.

\textbf{Distribution Shift Problem:} The fundamental challenge in imitation learning is distribution shift, where the learned policy encounters states not present in the expert demonstration dataset. During deployment, small errors accumulate and compound, causing the agent to visit increasingly unfamiliar states, leading to catastrophic failures. This problem is particularly acute in autonomous driving, where safety is paramount.

\textbf{Spatial Relationship Modeling:} Existing neural network architectures often fail to effectively capture the complex spatial relationships and interactions between multiple vehicles. Traditional approaches treat vehicle detection as fixed-size inputs, ignoring the variable number of surrounding vehicles and their dynamic interactions.

\textbf{Temporal Processing Challenges:} Integrating spatial vehicle interactions with temporal sensor processing patterns remains a significant challenge. Most approaches process sensor data independently without considering the temporal dependencies crucial for understanding dynamic driving scenarios.

Our DAGGER-GraphFormer framework addresses these fundamental challenges through three key innovations. First, we implement the Dataset Aggregation (DAGGER) algorithm with a conservative beta scheduling strategy. Unlike standard behavioral cloning, DAGGER iteratively collects data under the current learned policy and aggregates it with expert demonstrations, ensuring policies learn to handle self-generated states. Our conservative approach maintains expert policy guidance with $\beta_{min} = 0.5$ throughout training, preventing performance degradation.

Second, we model vehicle interactions as dynamic graphs where nodes represent vehicles and edges encode spatial proximity relationships. Our implementation supports both Graph Convolutional Networks (GCNs) and Graph Attention Networks (GATs) with 2 layers and 128 hidden dimensions, effectively capturing complex vehicle interactions within a 30-meter sensing radius.

Third, we employ multi-head Transformer encoders with 4 layers, 8 attention heads, and 256 hidden dimensions to process sequential multi-modal sensor data. The architecture processes concatenated features from LiDAR (30 lasers), side detectors (30 lasers), and lane line detectors (12 lasers), creating unified spatial-temporal understanding.

This work makes several significant contributions to autonomous driving research. We present a novel hybrid GraphFormer architecture that effectively integrates GNNs for spatial modeling with Transformers for temporal processing. Additionally, we develop a comprehensive DAGGER training framework with conservative beta scheduling that successfully mitigates distribution shift. Our efficient implementation supports parallel data collection with 10 workers and GPU acceleration. Through systematic experimental evaluation in MetaDrive simulation, we demonstrate 94.6\% route completion with 2.8\% collision rate. Finally, we provide detailed ablation studies validating the effectiveness of each architectural component.

\section{Related Work and Background}
\label{Background}

\subsection{Imitation Learning in Autonomous Driving}

Imitation learning addresses the challenge of learning complex behaviors from expert demonstrations without requiring explicit reward engineering. Behavioral cloning (BC) \cite{pomerleau1989alvinn} directly trains policies to mimic expert actions but suffers from distribution shift during deployment.

The DAGGER algorithm \cite{ross2011dagger} addresses distribution shift by iteratively collecting data under the current policy and querying experts for optimal actions. This approach provides theoretical guarantees for performance bounds:
\begin{equation}
\mathbb{E}[\text{cost}(\pi_{\theta_N})] \leq \mathbb{E}[\text{cost}(\pi^*)] + O(\epsilon_N)
\end{equation}

\subsection{Graph Neural Networks for Autonomous Driving}

Graph Neural Networks have shown promise in modeling structured autonomous driving data. GNNs capture spatial vehicle relationships through message passing mechanisms \cite{kipf2016gcn}, enabling complex interaction modeling suitable for multi-agent driving scenarios.

Graph Attention Networks \cite{velickovic2017gat} extend GNNs with attention mechanisms, learning adaptive importance weights for neighboring nodes:
\begin{equation}
h_i^{(l+1)} = \sigma\left(\sum_{j \in \mathcal{N}(i)} \alpha_{ij}^{(l)} W^{(l)} h_j^{(l)}\right)
\end{equation}

\subsection{Transformer Architectures}

Transformers \cite{vaswani2017attention} have revolutionized sequential modeling through self-attention mechanisms. In autonomous driving, attention enables focusing on relevant spatial-temporal features across time steps for informed decision-making.

\section{DAGGER-GraphFormer Architecture}
\label{Architecture}

Our framework combines expert demonstration collection with DAGGER training using a hybrid GraphFormer architecture that integrates GNNs for spatial modeling with Transformers for temporal processing.

\subsection{Problem Formulation}

We formulate autonomous driving as a Markov Decision Process (MDP) $\langle \mathcal{S}, \mathcal{A}, \mathcal{P}, \mathcal{R}, \gamma \rangle$ where $\mathcal{S}$ represents the state space including ego vehicle state, surrounding vehicles, and sensor observations, $\mathcal{A}$ denotes the continuous action space for steering and throttle control with $a_t = (a_t^{\text{steer}}, a_t^{\text{throttle}}) \in [-1, 1]^2$, $\mathcal{P}$ captures state transition dynamics, $\mathcal{R}$ represents the reward function (not explicitly used in imitation learning), and $\gamma$ is the discount factor.

The environment is represented as a dynamic vehicle interaction graph $G_t = (V_t, E_t, X_t)$ where $V_t$ includes ego and surrounding vehicles, $E_t$ encodes spatial relationships, and $X_t$ contains node features.

\subsection{Hybrid GraphFormer Architecture}

Our GraphFormer architecture processes spatial vehicle interactions as dynamic graphs while employing multi-head Transformer encoders for temporal sensor sequence processing.

\subsubsection{Graph Construction and Vehicle Modeling}

We represent the driving environment as a dynamic vehicle interaction graph $G_t = (V_t, E_t, X_t)$ at each timestep $t$:

\textbf{Node Features:} Each vehicle is represented as a node with 7-dimensional features:
\begin{equation}
x_i = [x, y, v_x, v_y, \theta, a_x, a_y]^T
\end{equation}
where $(x, y)$ represents position, $(v_x, v_y)$ represents velocity components, $\theta$ represents heading angle, and $(a_x, a_y)$ represents acceleration components.

\textbf{Edge Construction:} Proximity-based edges are established between vehicles within the 30-meter sensing range:
\begin{equation}
e_{ij} = \begin{cases}
1 & \text{if } \|p_i - p_j\|_2 \leq 30 \text{ meters} \\
0 & \text{otherwise}
\end{cases}
\end{equation}

\textbf{Dynamic Updates:} The graph structure is dynamically updated at each timestep to reflect changes in vehicle positions and the appearance/disappearance of vehicles.

\subsubsection{Graph Neural Network Processing}

Our implementation supports both GCNs and GATs for learning spatial representations. The GCN variant processes node features through:
\begin{equation}
h_i^{(l+1)} = \sigma\left(W^{(l)} \sum_{j \in \mathcal{N}(i) \cup \{i\}} \frac{h_j^{(l)}}{\sqrt{|\mathcal{N}(i)||\mathcal{N}(j)|}}\right)
\end{equation}

For the GAT variant, attention mechanisms learn adaptive spatial representations:
\begin{equation}
h_i^{(l+1)} = \sigma\left(\sum_{j \in \mathcal{N}(i)} \alpha_{ij}^{(l)} W^{(l)} h_j^{(l)}\right)
\end{equation}

where attention weights are computed as:
\begin{equation}
\alpha_{ij}^{(l)} = \frac{\exp(\text{LeakyReLU}(a^T[W h_i || W h_j]))}{\sum_{k \in \mathcal{N}(i)} \exp(\text{LeakyReLU}(a^T[W h_i || W h_k]))}
\end{equation}

The GNN module includes residual connections, graph normalization, and dropout (0.1) for improved training stability.

\subsubsection{Transformer Sensor Processing}

Multi-modal sensor data is processed through a multi-head Transformer encoder. The sensor input consists of concatenated features from LiDAR (30 laser measurements with 50-meter range), side detectors (30 laser measurements for lateral awareness), and lane line detectors (12 laser measurements for lane keeping).

This forms a 72-dimensional sequence feature vector processed via multi-head attention:
\begin{equation}
\text{MultiHead}(Q, K, V) = \text{Concat}(\text{head}_1, ..., \text{head}_8)W^O
\end{equation}

where each attention head computes:
\begin{equation}
\text{head}_i = \text{Attention}(QW_i^Q, KW_i^K, VW_i^V)
\end{equation}

\subsubsection{Feature Fusion and Policy Output}

The integrated architecture combines spatial and temporal features through a carefully designed fusion mechanism. Spatial features from the GNN are aggregated using global mean pooling and projected to 128 dimensions, while sequential sensor features are linearly projected to 128 dimensions. These graph and sensor features are then concatenated to form 256-dimensional representations. The fused features undergo 4-layer Transformer encoding with 8 attention heads, and finally a two-layer MLP head outputs continuous actions for steering and throttle control.

The final policy output is computed as:
\begin{equation}
\pi_\theta(a|s) = \text{ActionHead}(\text{Transformer}([\text{GNN}(G_t); \text{Proj}(S_t)]))
\end{equation}

\section{DAGGER Training Framework}
\label{DAGGER Training}

Our DAGGER training framework leverages the hybrid GraphFormer architecture to address distribution shift through iterative data aggregation with conservative beta scheduling.

\subsection{DAGGER Algorithm Implementation}

The DAGGER algorithm is adapted to work with our GraphFormer architecture, iteratively collecting trajectory data under the current learned policy and aggregating it with expert demonstrations.

\textbf{Training Objective:} At iteration $i$, the policy $\pi_{\theta_i}$ minimizes the supervised learning loss:
\begin{equation}
\mathcal{L}_{DAGGER} = \mathbb{E}_{(s,a) \sim \mathcal{D}_i} \left[ \|a - \pi_{\theta_i}(s)\|^2 \right]
\end{equation}

where $\mathcal{D}_i$ represents the aggregated dataset containing both expert demonstrations and policy-generated trajectories.

Our implementation employs a learning rate of $3 \times 10^{-4}$ with cosine annealing scheduler, batch size of 128 samples per training batch, 10 training epochs per DAGGER iteration, weight decay of $1 \times 10^{-4}$ for L2 regularization, and a buffer size of 20,000 samples for experience replay.

\subsection{Conservative Beta Scheduling Strategy}

A critical component is the beta scheduling strategy controlling the mixture between expert and learned policy actions:
\begin{equation}
a_t = \begin{cases}
a_{expert} = \pi^*(s_t) & \text{with probability } \beta_i \\
a_{policy} \sim \pi_{\theta_i}(s_t) & \text{with probability } 1 - \beta_i
\end{cases}
\end{equation}

We implement conservative scheduling maintaining higher expert policy usage:
\begin{equation}
\beta_i = \max(\beta_{min}, \beta_{max} \cdot \exp(-\alpha \cdot i))
\end{equation}
where $\beta_{min} = 0.5$, $\beta_{max} = 1.0$, ensuring expert guidance remains available throughout training.

\begin{algorithm}
\SetAlgoLined
\KwIn{Expert policy $\pi^*$, Initial dataset $\mathcal{D}_0$, Iterations $N$}
\KwOut{Trained policy $\pi_{\theta_N}$}
\caption{DAGGER with Hybrid GraphFormer}

Initialize $\pi_{\theta_0}$ on $\mathcal{D}_0$\;
\For{$i = 1$ to $N$}{
    Execute $\pi_{\theta_{i-1}}$ to collect trajectory $\tau_i$ using beta scheduling\;
    Query expert policy $\pi^*$ for optimal actions at states in $\tau_i$\;
    Update dataset: $\mathcal{D}_i = \mathcal{D}_{i-1} \cup \{(s, \pi^*(s)) : s \in \tau_i\}$\;
    Train policy $\pi_{\theta_i}$ on aggregated dataset $\mathcal{D}_i$ using GraphFormer\;
    Evaluate performance and update beta schedule parameters\;
}
\Return{$\pi_{\theta_N}$}
\end{algorithm}

\section{Experimental Results}
\label{Results}

We evaluate DAGGER-GraphFormer comprehensively, validating effectiveness across performance, safety, and distribution shift mitigation.

\subsection{Experimental Setup}

We conduct experiments using the MetaDrive simulation environment \cite{li2021metadrive} with "SSSSS" map configuration across 10 scenarios, traffic density of 0.1 for controlled evaluation, maximum episode length of 1000 timesteps, and NPC speed control limited to 10 km/h maximum for safety considerations.

The model configuration consists of a GNN component with 2 layers and 128 hidden dimensions supporting both GCN and GAT variants, a Transformer component with 4 layers, 8 attention heads, and 256 hidden dimensions, input processing of 7D vehicle features and 72D sensor sequences, and output generation of 2D continuous actions for steering and throttle control.

Our DAGGER configuration involves 100 iterations with 10 episodes per iteration, 10 parallel workers for efficient data collection, conservative beta scheduling with $\beta_{min} = 0.5$ and $\beta_{max} = 1.0$, and the IDM (Intelligent Driver Model) as the expert policy.

\subsection{Performance Results}

Table \ref{tab:performance_results} shows comprehensive performance comparison. DAGGER-GraphFormer achieves superior performance across all metrics.

\begin{table}[!t]
\centering
\caption{Performance Comparison on MetaDrive Simulation}
\label{tab:performance_results}
\small
\begin{tabular}{|l|c|c|c|c|}
\hline
\textbf{Method} & \textbf{Success} & \textbf{Collision} & \textbf{Off-road} & \textbf{Completion} \\
 & \textbf{Rate (\%)} & \textbf{Rate (\%)} & \textbf{Rate (\%)} & \textbf{Rate (\%)} \\
\hline
Expert Policy (IDM) & 95.2 & 1.2 & 2.1 & 97.8 \\
BC (CNN) & 78.5 & 8.3 & 12.2 & 82.7 \\
BC + GraphFormer & 85.1 & 5.2 & 8.8 & 88.4 \\
DAGGER + CNN & 88.7 & 4.1 & 6.2 & 91.2 \\
\hline
\textbf{DAGGER-GraphFormer} & \textbf{92.3} & \textbf{2.8} & \textbf{4.1} & \textbf{94.6} \\
\hline
\end{tabular}
\end{table}

\subsection{Ablation Studies}

Component analysis validates our architectural choices through systematic ablation studies. The GraphFormer architecture improves success rate by 7.2\% compared to CNN baselines (85.1\% vs 78.5\%), while DAGGER training reduces collision rate by 45\% compared to behavioral cloning (2.8\% vs 5.2\%). The conservative beta scheduling strategy maintains stability throughout 100 iterations, and both GCN and GAT variants achieve comparable performance with GAT showing slight advantages in complex scenarios.

\textbf{Architecture Impact:} The GraphFormer architecture effectively captures spatial-temporal relationships. GNNs model vehicle interactions while Transformers process multi-modal sensor sequences, creating comprehensive environmental understanding.

\textbf{DAGGER Effectiveness:} Iterative aggregation successfully mitigates distribution shift. The conservative beta scheduling prevents performance degradation observed in standard DAGGER implementations.

\subsection{Training Convergence Analysis}

DAGGER-GraphFormer demonstrates stable convergence across 100 iterations. Route completion improves from 85\% to 94.6\% over the course of training, while collision rate decreases from 6.5\% to 2.8\% with conservative scheduling. Training loss converges within 10 epochs per iteration, and GPU acceleration enables efficient training with 128 batch size.

\section{Conclusion}
\label{Conclusion}

This paper presented DAGGER-GraphFormer, a novel framework addressing distribution shift in autonomous driving imitation learning through the integration of DAGGER with a hybrid GraphFormer architecture combining Graph Neural Networks and Transformers.

Our work achieves several key milestones in autonomous driving research. The hybrid GraphFormer architecture effectively models spatial vehicle interactions and temporal sensor processing, while the DAGGER framework with conservative beta scheduling successfully mitigates distribution shift. We demonstrate superior performance with 94.6\% route completion and 2.8\% collision rate, validated through comprehensive ablation studies and convergence analysis.

The technical contributions of this work include the novel integration of GNNs (GCN/GAT) for spatial modeling with Transformers for temporal processing, a conservative beta scheduling strategy that maintains expert guidance throughout training, efficient parallel implementation supporting GPU acceleration and multi-worker data collection, and systematic evaluation demonstrating significant improvements over behavioral cloning baselines.

Future research directions include extension to more complex scenarios with pedestrians and cyclists, real-world sensor integration and validation, investigation of alternative graph architectures and attention mechanisms, and multi-agent DAGGER training for coordinated autonomous vehicle systems.

This work contributes to safer autonomous driving by addressing fundamental challenges in imitation learning, with potential applications across various autonomous platforms requiring robust multi-agent decision-making capabilities.

\section*{Acknowledgments}

We thank the MetaDrive development team for providing the simulation environment and the research community for valuable feedback on this work.

\begin{thebibliography}{99}

\bibitem{ross2011dagger}
S. Ross, G. Gordon, and D. Bagnell, ``A reduction of imitation learning and structured prediction to no-regret online learning,'' in \textit{Proceedings of the 14th International Conference on Artificial Intelligence and Statistics (AISTATS)}, 2011, pp. 627--635.

\bibitem{kipf2016gcn}
T. N. Kipf and M. Welling, ``Semi-supervised classification with graph convolutional networks,'' in \textit{International Conference on Learning Representations (ICLR)}, 2017.

\bibitem{vaswani2017attention}
A. Vaswani, N. Shazeer, N. Parmar, J. Uszkoreit, L. Jones, A. N. Gomez, L. Kaiser, and I. Polosukhin, ``Attention is all you need,'' in \textit{Advances in Neural Information Processing Systems (NIPS)}, 2017, pp. 5998--6008.

\bibitem{li2021metadrive}
Q. Li, Z. Peng, L. Feng, Q. Zhang, Z. Xue, and B. Zhou, ``MetaDrive: Composing diverse driving scenarios for generalizable reinforcement learning,'' \textit{IEEE Transactions on Pattern Analysis and Machine Intelligence}, vol. 45, no. 3, pp. 3461--3475, 2022.

\bibitem{pomerleau1989alvinn}
D. A. Pomerleau, ``ALVINN: An autonomous land vehicle in a neural network,'' in \textit{Advances in Neural Information Processing Systems (NIPS)}, 1989, pp. 305--313.

\bibitem{velickovic2017gat}
P. Veli\v{c}kovi\'{c}, G. Cucurull, A. Casanova, A. Romero, P. Li\`{o}, and Y. Bengio, ``Graph attention networks,'' in \textit{International Conference on Learning Representations (ICLR)}, 2018.

\bibitem{bojarski2016end}
M. Bojarski, D. Del Testa, D. Dworakowski, B. Firner, B. Flepp, P. Goyal, L. D. Jackel, M. Monfort, U. Muller, J. Zhang, X. Zhang, J. Zhao, and K. Zieba, ``End to end learning for self-driving cars,'' \textit{arXiv preprint arXiv:1604.07316}, 2016.

\bibitem{codevilla2018end}
F. Codevilla, M. M{\"u}ller, A. L{\'o}pez, V. Koltun, and A. Dosovitskiy, ``End-to-end driving via conditional imitation learning,'' in \textit{IEEE International Conference on Robotics and Automation (ICRA)}, 2018, pp. 4693--4700.

\end{thebibliography}

\end{document}
