# 论文公式详解 (Formulas Explanation)

本文档详细解释了 `DAGGER-GraphFormer` 论文中使用的所有数学公式.

---

### 第二节: 背景与相关工作 (Background and Related Work)

#### 公式 (1): 动作空间 (Action Space)
$$
a_t = (a_t^{\text{steer}}, a_t^{\text{throttle}}) \in [-1, 1]^2
$$
- **含义**: 定义了智能体 (ego vehicle) 可以执行的动作.
- **符号**:
    - $a_t$: 在时间步 $t$ 采取的完整动作.
    - $a_t^{\text{steer}}$: 车辆的转向控制, 数值范围从-1 (左转) 到 1 (右转).
    - $a_t^{\text{throttle}}$: 车辆的油门/刹车控制, 数值范围从-1 (最大刹车) 到 1 (最大油门).
- **作用**: 明确了策略网络需要输出的控制指令是两个在 $[-1, 1]$ 区间内的连续值.

#### 公式 (2): DAGGER 算法的理论性能保证
$$
\mathbb{E}[\text{cost}(\pi_{\theta_N})] \leq \mathbb{E}[\text{cost}(\pi^*)] + O(\epsilon_N)
$$
- **含义**: 这是 DAGGER 算法的核心理论. 它表明, 经过N轮迭代后, 学习到的策略 $\pi_{\theta_N}$ 的预期代价 (可以理解为犯错的程度) 有一个上限.
- **符号**:
    - $\pi_{\theta_N}$: 经过 $N$ 轮训练后最终得到的策略.
    - $\pi^*$: 完美的专家策略.
    - $\mathbb{E}[\text{cost}(.)]$: 执行某个策略时期望付出的代价.
    - $O(\epsilon_N)$: 一个与训练误差 $\epsilon_N$ 相关的项.
- **作用**: 从理论上保证了只要我们能把在聚合数据集上的监督学习任务做得足够好 (即 $\epsilon_N$ 足够小), 那么我们学习到的策略的性能就能接近专家的性能.

#### 公式 (3): 图神经网络 (GNN) 的通用消息传递框架
$$
h_v^{(l+1)} = \text{UPDATE}^{(l)}\left(h_v^{(l)}, \text{AGGREGATE}^{(l)}\left(\{h_u^{(l)} : u \in \mathcal{N}(v)\}\right)\right)
$$
- **含义**: 描述了 GNN 中节点信息更新的通用过程. 一个节点的新特征是由它自己当前的特征和它所有邻居的特征聚合而来的信息共同决定的.
- **符号**:
    - $h_v^{(l)}$: 节点 $v$ 在第 $l$ 层的特征向量 (或称隐藏状态).
    - $\mathcal{N}(v)$: 节点 $v$ 的邻居节点集合.
    - `AGGREGATE`: 一个聚合函数, 负责将所有邻居节点 $u$ 的信息 $h_u^{(l)}$ 汇总成一条消息.
    - `UPDATE`: 一个更新函数, 负责将节点 $v$ 自身的旧信息 $h_v^{(l)}$ 和来自邻居的聚合信息结合起来, 生成新的特征 $h_v^{(l+1)}$

#### 公式 (4) & (5): GNN 聚合与更新的具体实现
$$
\text{UPDATE}^{(l)}(h_v, m_v) = \text{MLP}^{(l)}([h_v; m_v])
$$
$$
\text{AGGREGATE}^{(l)}(\{h_u^{(l)} : u \in \mathcal{N}(v)\}) = \frac{1}{|\mathcal{N}(v)|} \sum_{u \in \mathcal{N}(v)} h_u^{(l)}
$$
- **含义**: 这是对通用 GNN 框架的一种具体实现.
- **公式 (5)** 定义了 `AGGREGATE` 函数为 **平均池化 (Mean Pooling)**, 即简单地将所有邻居节点的特征向量取平均.
- **公式 (4)** 定义了 `UPDATE` 函数: 首先将节点自身的特征 $h_v$ 和聚合后的邻居消息 $m_v$ **拼接 (concatenate, 记为 $[;]$)** 在一起, 然后将这个更长的向量送入一个**多层感知机 (MLP)** 进行变换, 得到最终更新后的节点特征.

---

### 第三节: DAGGER-GraphFormer 架构 (Architecture)

#### 公式 (6): 数据集定义
$$
\mathcal{D}=\{(G_t, S_t, a_t)\}_{t=1}^N
$$
- **含义**: 定义了用于训练的数据集 $\mathcal{D}$ 的结构.
- **符号**:
    - $G_t$: 在时间步 $t$ 的车辆交互图.
    - $S_t$: 在时间步 $t$ 的多模态传感器数据 (72维向量).
    - $a_t$: 在时间步 $t$ 专家给出的正确动作.
- **作用**: 明确了模型的输入 ($G_t$, $S_t$) 和监督学习的目标 ($a_t$).

#### 公式 (7): 图卷积网络 (GCN) 更新法则
$$
h_i^{(l+1)} = \sigma\left(W^{(l)} \sum_{j \in \mathcal{N}(i) \cup \{i\}} \frac{h_j^{(l)}}{\sqrt{|\mathcal{N}(i)||\mathcal{N}(j)|}}\right)
$$
- **含义**: 这是 GCN 层的具体数学形式. 它通过对中心节点及其所有邻居节点的特征进行加权求和来更新节点表示.
- **符号**:
    - $\sigma$: 非线性激活函数 (如 ReLU).
    - $W^{(l)}$: 第 $l$ 层的一个可学习的权重矩阵.
    - $\mathcal{N}(i) \cup \{i\}$: 节点 $i$ 的邻居以及它自身.
    - $\sqrt{|\mathcal{N}(i)||\mathcal{N}(j)|}$: 一个归一化项, 用于防止节点度数大的节点在聚合时产生过大的数值, 保持训练稳定.

#### 公式 (8) & (9): Transformer 的多头注意力机制
$$
\text{MultiHead}(Q, K, V) = \text{Concat}(\text{head}_1, ..., \text{head}_h)W^O
$$
$$
\text{head}_i = \text{Attention}(QW_i^Q, KW_i^K, VW_i^V)
$$
- **含义**: 这是 Transformer 架构的核心. 它允许模型在处理序列数据 (如传感器时间序列) 时, 同时从 $h$ 个不同的"子空间"关注信息.
- **符号**:
    - $Q, K, V$: 查询 (Query), 键 (Key), 值 (Value) 矩阵, 是输入序列的三种不同线性变换.
    - $W_i^Q, W_i^K, W_i^V$: 第 $i$ 个注意力头的投影矩阵.
    - $\text{head}_i$: 第 $i$ 个头的输出.
    - `Concat`: 将 $h$ 个头的输出拼接起来.
    - $W^O$: 最终的输出投影矩阵.
- **作用**: 增强了模型捕捉序列中复杂依赖关系的能力.

#### 公式 (10): 最终策略输出
$$
\pi_\theta(a|s) = \text{ActionHead}(\text{Transformer}([\text{GNN}(G_t); \text{Proj}(S_t)]))
$$
- **含义**: 概括了整个 DAGGER-GraphFormer 的前向传播流程.
- **流程**:
    1. 图数据 $G_t$ 输入 `GNN` 模块, 得到空间特征.
    2. 传感器数据 $S_t$ 经过一个线性投影层 `Proj`.
    3. 将 GNN 的输出和投影后的传感器特征**拼接** ($[;]$).
    4. 将拼接后的时空特征送入 `Transformer` 编码器.
    5. Transformer 的最终输出送入一个 `ActionHead` (一个MLP), 生成最终的动作 $a$.

---

### 第四节: DAGGER 训练框架 (Training Framework)

#### 公式 (11): DAGGER 训练目标 (损失函数)
$$
\mathcal{L}_{DAGGER} = \mathbb{E}_{(s,a) \sim \mathcal{D}_i} \left[ \|a - \pi_{\theta_i}(s)\|^2 \right]
$$
- **含义**: 这是策略网络的损失函数, 即**均方误差 (Mean Squared Error, MSE)**.
- **符号**:
    - $\mathcal{L}_{DAGGER}$: DAGGER 的损失.
    - $(s,a) \sim \mathcal{D}_i$: 从第 $i$ 轮迭代的聚合数据集 $\mathcal{D}_i$ 中采样一个 (状态, 动作) 对.
    - $\pi_{\theta_i}(s)$: 当前策略网络对状态 $s$ 输出的动作.
    - $a$: 专家给出的标准答案动作.
- **作用**: 通过最小化这个损失, 使得策略网络的输出尽可能地模仿专家的行为.

#### 公式 (12): Beta 调度策略的动作选择
$$
a_t = \begin{cases} a_{expert} & \text{with probability } \beta_i \\ a_{policy} \sim \pi_{\theta_i}(s_t) & \text{with probability } 1 - \beta_i \end{cases}
$$
- **含义**: 在数据收集阶段, 决定是使用专家策略还是当前学习的策略来执行动作.
- **符号**:
    - $\beta_i$: 在第 $i$ 轮迭代时, 选择专家策略的概率.
- **作用**: 这是 DAGGER 算法的核心机制. 通过混合专家和学习策略, 使得数据集能够包含学习策略所遇到的状态, 从而缓解分布偏移.

#### 公式 (13): Beta 值的保守调度策略
$$
\beta_i = \max(\beta_{min}, \beta_{max} \cdot \exp(-\alpha \cdot i))
$$
- **含义**: 定义了 $\beta_i$ 如何随着迭代次数 $i$ 的增加而衰减.
- **符号**:
    - $i$: 当前的迭代轮数.
    - $\beta_{max}$: 初始的 $\beta$ 值 (例如 1.0).
    - $\beta_{min}$: $\beta$ 的最小值 (例如 0.5).
    - $\alpha$: 衰减率.
- **作用**: 采用指数衰减的方式, 使得在训练初期更多地依赖专家 (探索性弱), 随着模型变强, 逐渐增加模型自己决策的比例. `max` 函数确保了 $\beta$ 不会低于一个设定的下限, 保证了训练后期仍有足够的专家指导, 这就是"保守"的体现.
