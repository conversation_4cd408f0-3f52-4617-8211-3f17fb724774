# 使用的指标

📊 Total Episodes: 100
🎯 Success Rate: 100.0%
🛣️ Out of Road Rate: 0.0%
💥 Collision Rate: 0.0%
🗺️ Average Route Completion: 97.3%
🏆 Average Reward: 181.95


# 测试集map=SS start_seed = 11,num_scenarios=100

# expert

📊 Total Episodes: 100
🎯 Success Rate: 100.0%
🛣️ Out of Road Rate: 0.0%
💥 Collision Rate: 0.0%
👣 Average Steps: 228.7
🗺️ Average Route Completion: 97.3%
🏆 Average Reward: 181.95
📏 Average Distance: 165.3m

# DAG

📊 Total Episodes: 100

🎯 Success Rate: 83.0%
🛣️ Out of Road Rate: 4.0%
💥 Collision Rate: 13.0%
👣 Average Steps: 211.1
🗺️ Average Route Completion: 94.0%
🏆 Average Reward: 168.24
📏 Average Distance: 154.7m

# 去掉GNN

saved_model/dagger_without_gnn_20250713_124046/best_model_by_completion.pth

   总测试episodes: 100
   成功率: 47.00%
   碰撞率: 43.00%
   出路率: 10.00%

平均路线完成度: 81.74%

# beta = 1. 退化成BC+GNN+Transformer

📊 Total Episodes: 100
🎯 Success Rate: 72.0%
🛣️ Out of Road Rate: 10.0%
💥 Collision Rate: 18.0%
👣 Average Steps: 205.2
🗺️ Average Route Completion: 88.7%
🏆 Average Reward: 157.53
📏 Average Distance: 146.0m

# BC+MLP
