# idm专家上限(seed11-21)

📊 Total Episodes: 50
🎯 Success Rate: 100.0%
🛣️ Out of Road Rate: 0.0%
💥 Collision Rate: 0.0%
⏰ Timeout Rate: 0.0%
👣 Average Steps: 463.3
🗺️ Average Route Completion: 98.7%
🏆 Average Reward: 359.09
📏 Average Distance: 334.8m

# 云端beta =1 退化为行为克隆

训练集表现:

📊 Total Episodes: 50
🎯 Success Rate: 40.0%
🛣️ Out of Road Rate: 30.0%
💥 Collision Rate: 30.0%
⏰ Timeout Rate: 0.0%
👣 Average Steps: 350.1
🗺️ Average Route Completion: 74.5%
🏆 Average Reward: 269.25
📏 Average Distance: 257.4m

🎉 Model test finished successfully!

测试集表现:

📊 Total Episodes: 10
🎯 Success Rate: 30.0%
🛣️ Out of Road Rate: 20.0%
💥 Collision Rate: 50.0%
⏰ Timeout Rate: 0.0%
👣 Average Steps: 334.7
🗺️ Average Route Completion: 68.7%
🏆 Average Reward: 238.31
📏 Average Distance: 229.2m

🎉 Model test finished successfully!

# 云端beta =0.5 的Dagger

## 训练集(1-11):

📊 Total Episodes: 50

🎯 Success Rate: 70.0%
🛣️ Out of Road Rate: 20.0%
💥 Collision Rate: 10.0%
⏰ Timeout Rate: 0.0%
👣 Average Steps: 413.6
🗺️ Average Route Completion: 83.9%
🏆 Average Reward: 309.48
📏 Average Distance: 291.7m

🎉 Model test finished successfully!

## 测试集(seed11-21):

📊 Total Episodes: 10
🎯 Success Rate: 50.0%
🛣️ Out of Road Rate: 10.0%
💥 Collision Rate: 40.0%
⏰ Timeout Rate: 0.0%
👣 Average Steps: 399.1
🗺️ Average Route Completion: 86.8%
🏆 Average Reward: 315.37
📏 Average Distance: 300.2m

map=SS 10,100

📊 Total Episodes: 100
🎯 Success Rate: 83.0%
🛣️ Out of Road Rate: 4.0%
💥 Collision Rate: 13.0%
👣 Average Steps: 211.1
🗺️ Average Route Completion: 94.0%
🏆 Average Reward: 168.24
📏 Average Distance: 154.7m


"num_scenarios": 50,      # 增加场景数量，支持更多不同的seed

"traffic_density": 0.1,    # 与main.py保持一致

"start_seed": 1,           # 与main.py保持一致

"map": "SSS",	

📊 Total Episodes: 100
🎯 Success Rate: 65.0%
🛣️ Out of Road Rate: 14.0%
💥 Collision Rate: 21.0%
👣 Average Steps: 268.8
🗺️ Average Route Completion: 88.0%
🏆 Average Reward: 208.23
📏 Average Distance: 195.5m





# without gnn

测试集(seed11-21):

🎯 基础成功指标:
   总测试episodes: 10
   成功率: 20.00%
   碰撞率: 70.00%
   出路率: 10.00%
   超时率: 0.00%

🛣️ 路线完成度:
   平均完成度: 58.11% (±22.70%)
   最高完成度: 98.84%
   最低完成度: 32.84%

⏱️ 时间与步数:
   平均步数: 261.9 (±102.8)
   步数范围: 177 - 456
   平均episode时间: 0.89s (±0.34s)
   总测试时间: 8.94s (0.1分钟)

🚗 速度表现:
   平均速度: 27.58 km/h (±3.27)
   速度范围: 0.92 - 33.09 km/h

🏆 奖励表现:
   平均总奖励: 210.38 (±103.70)
   平均步奖励: 0.7797

🎮 驾驶行为:
   转向方差: 0.000841
   油门方差: 0.095544
   动作平滑度: 0.021339

📏 行驶统计:
   平均行驶距离: 204.07m
   总行驶距离: 2040.72m
   平均燃油效率: 223.32 m/s

🛡️ 安全表现:
   平均安全违规: 0.00次/episode
   总安全违规: 0次
   平均险情: 39.50次/episode
   总险情: 395次
   平均车道变换: 4.50次/episode
   总车道变换: 45次

⭐ 综合评分: 36.03/100

训练集(1-11):

🎯 基础成功指标:
   总测试episodes: 10
   成功率: 30.00%
   碰撞率: 40.00%
   出路率: 0.00%
   超时率: 30.00%

🛣️ 路线完成度:
   平均完成度: 67.54% (±33.20%)
   最高完成度: 98.67%
   最低完成度: 13.87%

⏱️ 时间与步数:
   平均步数: 610.1 (±396.6)
   步数范围: 82 - 1000
   平均episode时间: 1.91s (±1.21s)
   总测试时间: 19.09s (0.3分钟)

🚗 速度表现:
   平均速度: 16.66 km/h (±6.39)
   速度范围: 0.93 - 32.72 km/h

🏆 奖励表现:
   平均总奖励: 232.03 (±117.38)
   平均步奖励: 0.4673

🎮 驾驶行为:
   转向方差: 0.027567
   油门方差: 0.174275
   动作平滑度: 0.198604

📏 行驶统计:
   平均行驶距离: 222.07m
   总行驶距离: 2220.71m
   平均燃油效率: 139.16 m/s

🛡️ 安全表现:
   平均安全违规: 0.00次/episode
   总安全违规: 0次
   平均险情: 15.60次/episode
   总险情: 156次
   平均车道变换: 5.10次/episode
   总车道变换: 51次

⭐ 综合评分: 47.04/100
