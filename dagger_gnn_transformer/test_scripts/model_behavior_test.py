#!/usr/bin/env python3
"""
模型行为详细测试脚本
测试GraphTransformerModel在不同输入条件下的行为和输出特性
使用模拟数据，不依赖MetaDrive环境
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from torch_geometric.data import Data, Batch
from typing import Dict, List, Tuple, Optional
import json

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dagger_gnn_transformer.models.graph_transformer_model import GraphTransformerModel

class ModelBehaviorTester:
    """模型行为测试器"""
    
    def __init__(self, model_path: Optional[str] = None, config: Dict = None):
        """
        初始化测试器
        
        Args:
            model_path: 模型路径，如果为None则创建新模型
            config: 模型配置字典
        """
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 使用提供的配置或创建默认配置
        if config is None:
            self.config = {
                'hidden_dim': 128,
                'transformer_dim': 256,
                'num_layers': 2,
                'num_transformer_layers': 4,
                'num_heads': 8,
                'dropout': 0.1,
                'sequence_dim': 72,
                'ego_node_dim': 7,
                'npc_node_dim': 7,
                'action_dim': 2,
                'gnn_type': 'GCN'
            }
        else:
            self.config = config
        
        # 创建模型
        self.model = GraphTransformerModel(self.config).to(self.device)
        
        # 如果提供了模型路径，加载模型
        if model_path is not None:
            if os.path.exists(model_path):
                checkpoint = torch.load(model_path, map_location=self.device)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                print(f"✅ 已加载模型: {model_path}")
            else:
                print(f"⚠️ 模型路径不存在: {model_path}，使用随机初始化模型")
        
        self.model.eval()
        print(f"🚀 模型行为测试器初始化完成 (设备: {self.device})")
    
    def create_mock_data(self, batch_size: int = 1, num_nodes: int = 5, 
                        num_edges: int = 10, seed: int = None) -> Dict[str, torch.Tensor]:
        """
        创建模拟输入数据
        
        Args:
            batch_size: 批次大小
            num_nodes: 每个图的节点数
            num_edges: 每个图的边数
            seed: 随机种子，用于重现结果
            
        Returns:
            模拟的输入数据
        """
        if seed is not None:
            torch.manual_seed(seed)
            np.random.seed(seed)
        
        data_list = []
        
        for _ in range(batch_size):
            # 创建节点特征 [num_nodes, node_dim]
            node_features = torch.randn(num_nodes, self.config['ego_node_dim'])
            
            # 创建边索引 [2, num_edges]
            if num_edges > 0:
                edge_index = torch.randint(0, num_nodes, (2, num_edges), dtype=torch.long)
            else:
                edge_index = torch.empty((2, 0), dtype=torch.long)
            
            data_list.append(Data(x=node_features, edge_index=edge_index))
        
        # 创建图批次
        graph_batch = Batch.from_data_list(data_list).to(self.device)
        
        # 创建序列特征 [batch_size, sequence_dim]
        sequence_features = torch.randn(batch_size, self.config['sequence_dim']).to(self.device)
        
        # 创建边界距离特征（如果模型需要）
        dist_to_boundaries = torch.randn(batch_size, 2).to(self.device)
        
        return {
            'graph_batch': graph_batch,
            'sequence_features': sequence_features,
            'dist_to_boundaries': dist_to_boundaries
        }
    
    def test_output_range(self, num_samples: int = 100) -> Dict:
        """
        测试模型输出范围
        
        Args:
            num_samples: 测试样本数量
            
        Returns:
            输出范围统计
        """
        print("\n📊 测试模型输出范围...")
        
        steering_values = []
        throttle_values = []
        
        for i in range(num_samples):
            # 创建随机输入
            batch_data = self.create_mock_data(batch_size=1, seed=i)
            
            # 模型推理
            with torch.no_grad():
                output = self.model(batch_data)
                action = output['action_pred'].cpu().numpy()[0]
            
            steering_values.append(action[0])  # 转向
            throttle_values.append(action[1])  # 油门/刹车
        
        # 计算统计信息
        steering_stats = {
            'min': float(np.min(steering_values)),
            'max': float(np.max(steering_values)),
            'mean': float(np.mean(steering_values)),
            'std': float(np.std(steering_values)),
            'abs_mean': float(np.mean(np.abs(steering_values))),
            'percentiles': {
                '5': float(np.percentile(steering_values, 5)),
                '25': float(np.percentile(steering_values, 25)),
                '50': float(np.percentile(steering_values, 50)),
                '75': float(np.percentile(steering_values, 75)),
                '95': float(np.percentile(steering_values, 95))
            }
        }
        
        throttle_stats = {
            'min': float(np.min(throttle_values)),
            'max': float(np.max(throttle_values)),
            'mean': float(np.mean(throttle_values)),
            'std': float(np.std(throttle_values)),
            'abs_mean': float(np.mean(np.abs(throttle_values))),
            'percentiles': {
                '5': float(np.percentile(throttle_values, 5)),
                '25': float(np.percentile(throttle_values, 25)),
                '50': float(np.percentile(throttle_values, 50)),
                '75': float(np.percentile(throttle_values, 75)),
                '95': float(np.percentile(throttle_values, 95))
            }
        }
        
        results = {
            'steering': steering_stats,
            'throttle': throttle_stats,
            'samples': num_samples
        }
        
        # 打印结果
        print(f"  转向 (steering) 范围: [{steering_stats['min']:.4f}, {steering_stats['max']:.4f}]")
        print(f"  转向均值: {steering_stats['mean']:.4f} ± {steering_stats['std']:.4f}")
        print(f"  油门/刹车 (throttle) 范围: [{throttle_stats['min']:.4f}, {throttle_stats['max']:.4f}]")
        print(f"  油门/刹车均值: {throttle_stats['mean']:.4f} ± {throttle_stats['std']:.4f}")
        
        return results

    def test_input_sensitivity(self, num_tests: int = 50) -> Dict:
        """
        测试模型对输入变化的敏感性

        Args:
            num_tests: 测试次数

        Returns:
            敏感性测试结果
        """
        print("\n🔍 测试模型输入敏感性...")

        # 创建基准输入
        base_data = self.create_mock_data(batch_size=1, seed=42)

        with torch.no_grad():
            base_output = self.model(base_data)['action_pred'].cpu().numpy()[0]

        # 测试序列特征敏感性
        sequence_sensitivity = []
        for i in range(num_tests):
            # 复制基准数据并添加噪声
            test_data = {
                'graph_batch': base_data['graph_batch'],
                'sequence_features': base_data['sequence_features'] + torch.randn_like(base_data['sequence_features']) * 0.1,
                'dist_to_boundaries': base_data['dist_to_boundaries']
            }

            with torch.no_grad():
                test_output = self.model(test_data)['action_pred'].cpu().numpy()[0]

            # 计算输出差异
            diff = np.linalg.norm(test_output - base_output)
            sequence_sensitivity.append(diff)

        # 测试图结构敏感性
        graph_sensitivity = []
        for i in range(num_tests):
            # 创建稍微不同的图结构
            test_graph_data = self.create_mock_data(batch_size=1, seed=42+i)
            test_data = {
                'graph_batch': test_graph_data['graph_batch'],
                'sequence_features': base_data['sequence_features'],
                'dist_to_boundaries': base_data['dist_to_boundaries']
            }

            with torch.no_grad():
                test_output = self.model(test_data)['action_pred'].cpu().numpy()[0]

            # 计算输出差异
            diff = np.linalg.norm(test_output - base_output)
            graph_sensitivity.append(diff)

        results = {
            'sequence_sensitivity': {
                'mean': float(np.mean(sequence_sensitivity)),
                'std': float(np.std(sequence_sensitivity)),
                'max': float(np.max(sequence_sensitivity)),
                'min': float(np.min(sequence_sensitivity))
            },
            'graph_sensitivity': {
                'mean': float(np.mean(graph_sensitivity)),
                'std': float(np.std(graph_sensitivity)),
                'max': float(np.max(graph_sensitivity)),
                'min': float(np.min(graph_sensitivity))
            }
        }

        print(f"  序列特征敏感性: {results['sequence_sensitivity']['mean']:.6f} ± {results['sequence_sensitivity']['std']:.6f}")
        print(f"  图结构敏感性: {results['graph_sensitivity']['mean']:.6f} ± {results['graph_sensitivity']['std']:.6f}")

        return results

    def test_consistency(self, num_runs: int = 10) -> Dict:
        """
        测试模型输出一致性（相同输入多次推理）

        Args:
            num_runs: 运行次数

        Returns:
            一致性测试结果
        """
        print("\n🔄 测试模型输出一致性...")

        # 创建固定输入
        test_data = self.create_mock_data(batch_size=1, seed=42)

        outputs = []
        for i in range(num_runs):
            with torch.no_grad():
                output = self.model(test_data)['action_pred'].cpu().numpy()[0]
                outputs.append(output)

        outputs = np.array(outputs)

        # 计算一致性指标
        steering_std = np.std(outputs[:, 0])
        throttle_std = np.std(outputs[:, 1])
        max_diff = np.max(np.linalg.norm(outputs - outputs[0], axis=1))

        results = {
            'steering_std': float(steering_std),
            'throttle_std': float(throttle_std),
            'max_output_diff': float(max_diff),
            'is_deterministic': bool(max_diff < 1e-6)
        }

        print(f"  转向标准差: {steering_std:.8f}")
        print(f"  油门/刹车标准差: {throttle_std:.8f}")
        print(f"  最大输出差异: {max_diff:.8f}")
        print(f"  是否确定性: {'是' if results['is_deterministic'] else '否'}")

        return results

    def test_extreme_inputs(self) -> Dict:
        """
        测试极端输入条件下的模型行为

        Returns:
            极端输入测试结果
        """
        print("\n⚡ 测试极端输入条件...")

        results = {}

        # 1. 零输入测试
        print("  测试零输入...")
        zero_data = self.create_mock_data(batch_size=1, seed=42)
        zero_data['sequence_features'].fill_(0)
        zero_data['graph_batch'].x.fill_(0)

        with torch.no_grad():
            zero_output = self.model(zero_data)['action_pred'].cpu().numpy()[0]

        results['zero_input'] = {
            'steering': float(zero_output[0]),
            'throttle': float(zero_output[1])
        }

        # 2. 大值输入测试
        print("  测试大值输入...")
        large_data = self.create_mock_data(batch_size=1, seed=42)
        large_data['sequence_features'].fill_(100.0)
        large_data['graph_batch'].x.fill_(100.0)

        with torch.no_grad():
            large_output = self.model(large_data)['action_pred'].cpu().numpy()[0]

        results['large_input'] = {
            'steering': float(large_output[0]),
            'throttle': float(large_output[1])
        }

        # 3. 负值输入测试
        print("  测试负值输入...")
        negative_data = self.create_mock_data(batch_size=1, seed=42)
        negative_data['sequence_features'].fill_(-10.0)
        negative_data['graph_batch'].x.fill_(-10.0)

        with torch.no_grad():
            negative_output = self.model(negative_data)['action_pred'].cpu().numpy()[0]

        results['negative_input'] = {
            'steering': float(negative_output[0]),
            'throttle': float(negative_output[1])
        }

        # 4. 单节点图测试
        print("  测试单节点图...")
        single_node_data = self.create_mock_data(batch_size=1, num_nodes=1, num_edges=0, seed=42)

        with torch.no_grad():
            single_output = self.model(single_node_data)['action_pred'].cpu().numpy()[0]

        results['single_node'] = {
            'steering': float(single_output[0]),
            'throttle': float(single_output[1])
        }

        # 打印结果
        for test_name, output in results.items():
            print(f"    {test_name}: 转向={output['steering']:.4f}, 油门/刹车={output['throttle']:.4f}")

        return results

    def generate_behavior_visualizations(self, save_dir: str = "test_results") -> None:
        """
        生成行为测试可视化图表

        Args:
            save_dir: 保存目录
        """
        print(f"\n📈 生成行为测试可视化图表到 {save_dir}...")

        os.makedirs(save_dir, exist_ok=True)

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 1. 输出分布图
        print("  生成输出分布图...")
        output_stats = self.test_output_range(num_samples=200)

        # 收集数据用于绘图
        steering_values = []
        throttle_values = []

        for i in range(200):
            batch_data = self.create_mock_data(batch_size=1, seed=i)
            with torch.no_grad():
                output = self.model(batch_data)
                action = output['action_pred'].cpu().numpy()[0]
            steering_values.append(action[0])
            throttle_values.append(action[1])

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 转向分布
        ax1.hist(steering_values, bins=30, alpha=0.7, color='blue', edgecolor='black')
        ax1.axvline(np.mean(steering_values), color='red', linestyle='--',
                   label=f'Mean: {np.mean(steering_values):.3f}')
        ax1.set_xlabel('Steering Value')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Steering Output Distribution')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 油门/刹车分布
        ax2.hist(throttle_values, bins=30, alpha=0.7, color='green', edgecolor='black')
        ax2.axvline(np.mean(throttle_values), color='red', linestyle='--',
                   label=f'Mean: {np.mean(throttle_values):.3f}')
        ax2.set_xlabel('Throttle/Brake Value')
        ax2.set_ylabel('Frequency')
        ax2.set_title('Throttle/Brake Output Distribution')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{save_dir}/output_distribution.png", dpi=300, bbox_inches='tight')
        plt.close()

        # 2. 动作空间散点图
        print("  生成动作空间散点图...")
        fig, ax = plt.subplots(1, 1, figsize=(10, 8))

        scatter = ax.scatter(steering_values, throttle_values, alpha=0.6, s=20)
        ax.set_xlabel('Steering')
        ax.set_ylabel('Throttle/Brake')
        ax.set_title('Action Space Distribution')
        ax.grid(True, alpha=0.3)

        # 添加边界线
        ax.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        ax.axvline(x=0, color='k', linestyle='-', alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{save_dir}/action_space.png", dpi=300, bbox_inches='tight')
        plt.close()

        print(f"  ✅ 行为测试可视化图表已保存到 {save_dir}/")

    def run_full_behavior_test(self, save_dir: str = "test_results") -> Dict:
        """
        运行完整的行为测试套件

        Args:
            save_dir: 结果保存目录

        Returns:
            完整测试结果
        """
        print("\n🧪 开始完整的模型行为测试套件")
        print("="*80)

        # 打印配置信息
        print("🔧 模型配置:")
        for key, value in self.config.items():
            print(f"  {key}: {value}")
        print("="*80)

        all_results = {}

        # 1. 输出范围测试
        all_results['output_range'] = self.test_output_range(num_samples=100)

        # 2. 输入敏感性测试
        all_results['input_sensitivity'] = self.test_input_sensitivity(num_tests=50)

        # 3. 一致性测试
        all_results['consistency'] = self.test_consistency(num_runs=10)

        # 4. 极端输入测试
        all_results['extreme_inputs'] = self.test_extreme_inputs()

        # 5. 生成可视化
        self.generate_behavior_visualizations(save_dir)

        # 6. 保存结果
        os.makedirs(save_dir, exist_ok=True)
        with open(f"{save_dir}/behavior_results.json", 'w', encoding='utf-8') as f:
            json.dump(all_results, f, indent=2, ensure_ascii=False)

        print(f"\n📄 行为测试结果已保存到: {save_dir}/behavior_results.json")

        # 7. 打印总结
        self.print_behavior_summary(all_results)

        return all_results

    def print_behavior_summary(self, results: Dict) -> None:
        """打印行为测试总结"""
        print("\n" + "="*80)
        print("📊 行为测试总结报告")
        print("="*80)

        # 输出范围总结
        if 'output_range' in results:
            output_range = results['output_range']
            print("🎯 输出范围:")
            print(f"  转向范围: [{output_range['steering']['min']:.4f}, {output_range['steering']['max']:.4f}]")
            print(f"  油门/刹车范围: [{output_range['throttle']['min']:.4f}, {output_range['throttle']['max']:.4f}]")

        # 一致性总结
        if 'consistency' in results:
            consistency = results['consistency']
            print("🔄 输出一致性:")
            print(f"  确定性: {'是' if consistency['is_deterministic'] else '否'}")
            print(f"  最大差异: {consistency['max_output_diff']:.8f}")

        # 敏感性总结
        if 'input_sensitivity' in results:
            sensitivity = results['input_sensitivity']
            print("🔍 输入敏感性:")
            print(f"  序列特征敏感性: {sensitivity['sequence_sensitivity']['mean']:.6f}")
            print(f"  图结构敏感性: {sensitivity['graph_sensitivity']['mean']:.6f}")

        print("="*80)
        print("✅ 行为测试完成！")


def main():
    """主函数"""
    print("🚀 GraphTransformerModel 行为测试")
    print("="*80)

    # 模型路径（可选）
    model_path = "/home/<USER>/rl/RL-IL/dagger_gnn_transformer/dagger_model/best_model.pth"

    # 创建配置（与训练时保持一致）
    config = {
        'hidden_dim': 128,
        'transformer_dim': 256,
        'num_layers': 2,
        'num_transformer_layers': 4,
        'num_heads': 8,
        'dropout': 0.1,
        'sequence_dim': 72,
        'ego_node_dim': 7,
        'npc_node_dim': 7,
        'action_dim': 2,
        'gnn_type': 'GCN'
    }

    # 创建测试器
    tester = ModelBehaviorTester(model_path=model_path, config=config)

    # 运行完整测试套件
    results = tester.run_full_behavior_test()

    return results


if __name__ == "__main__":
    try:
        results = main()
        print("\n🎉 所有行为测试完成！")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
