#!/usr/bin/env python3
"""
模型复杂度详细测试脚本
测试GraphTransformerModel的计算复杂度、内存使用、推理时间等指标
使用模拟数据，不依赖MetaDrive环境
"""

import os
import sys
import time
import torch
import torch.nn as nn
import numpy as np
import psutil
import json
from pathlib import Path
from torch_geometric.data import Data, Batch
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import seaborn as sns

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dagger_gnn_transformer.models.graph_transformer_model import GraphTransformerModel

class ModelComplexityTester:
    """模型复杂度测试器"""
    
    def __init__(self, config: Dict):
        """
        初始化测试器
        
        Args:
            config: 模型配置字典
        """
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建模型
        self.model = GraphTransformerModel(config).to(self.device)
        self.model.eval()
        
        # 测试结果存储
        self.test_results = {
            'config': config,
            'device': str(self.device),
            'model_parameters': self.count_parameters(),
            'model_size_mb': self.get_model_size(),
            'inference_times': [],
            'memory_usage': [],
            'throughput_tests': [],
            'batch_size_tests': [],
            'graph_size_tests': []
        }
        
        print(f"🚀 模型复杂度测试器初始化完成")
        print(f"📱 设备: {self.device}")
        print(f"🔢 模型参数数量: {self.test_results['model_parameters']:,}")
        print(f"💾 模型大小: {self.test_results['model_size_mb']:.2f} MB")
    
    def count_parameters(self) -> int:
        """计算模型参数数量"""
        return sum(p.numel() for p in self.model.parameters())
    
    def get_model_size(self) -> float:
        """计算模型大小（MB）"""
        param_size = 0
        buffer_size = 0
        
        for param in self.model.parameters():
            param_size += param.nelement() * param.element_size()
        
        for buffer in self.model.buffers():
            buffer_size += buffer.nelement() * buffer.element_size()
        
        size_mb = (param_size + buffer_size) / 1024 / 1024
        return size_mb
    
    def create_mock_data(self, batch_size: int = 1, num_nodes: int = 5, 
                        num_edges: int = 10) -> Dict[str, torch.Tensor]:
        """
        创建模拟输入数据
        
        Args:
            batch_size: 批次大小
            num_nodes: 每个图的节点数
            num_edges: 每个图的边数
            
        Returns:
            模拟的输入数据
        """
        data_list = []
        
        for _ in range(batch_size):
            # 创建节点特征 [num_nodes, node_dim]
            node_features = torch.randn(num_nodes, self.config['ego_node_dim'])
            
            # 创建边索引 [2, num_edges]
            if num_edges > 0:
                edge_index = torch.randint(0, num_nodes, (2, num_edges), dtype=torch.long)
            else:
                edge_index = torch.empty((2, 0), dtype=torch.long)
            
            data_list.append(Data(x=node_features, edge_index=edge_index))
        
        # 创建图批次
        graph_batch = Batch.from_data_list(data_list).to(self.device)
        
        # 创建序列特征 [batch_size, sequence_dim]
        sequence_features = torch.randn(batch_size, self.config['sequence_dim']).to(self.device)
        
        # 创建边界距离特征（如果模型需要）
        dist_to_boundaries = torch.randn(batch_size, 2).to(self.device)
        
        return {
            'graph_batch': graph_batch,
            'sequence_features': sequence_features,
            'dist_to_boundaries': dist_to_boundaries
        }
    
    def measure_inference_time(self, batch_data: Dict[str, torch.Tensor], 
                             num_runs: int = 100) -> Tuple[float, float, float]:
        """
        测量推理时间
        
        Args:
            batch_data: 输入数据
            num_runs: 运行次数
            
        Returns:
            (平均时间, 最小时间, 最大时间) 单位：毫秒
        """
        times = []
        
        # 预热
        for _ in range(10):
            with torch.no_grad():
                _ = self.model(batch_data)
        
        # 同步GPU
        if self.device.type == 'cuda':
            torch.cuda.synchronize()
        
        # 正式测试
        for _ in range(num_runs):
            start_time = time.perf_counter()
            
            with torch.no_grad():
                _ = self.model(batch_data)
            
            if self.device.type == 'cuda':
                torch.cuda.synchronize()
            
            end_time = time.perf_counter()
            times.append((end_time - start_time) * 1000)  # 转换为毫秒
        
        return np.mean(times), np.min(times), np.max(times)
    
    def measure_memory_usage(self, batch_data: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        测量内存使用情况
        
        Args:
            batch_data: 输入数据
            
        Returns:
            内存使用信息字典
        """
        # 清理GPU缓存
        if self.device.type == 'cuda':
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()
        
        # 记录初始内存
        process = psutil.Process()
        initial_ram = process.memory_info().rss / 1024 / 1024  # MB
        
        if self.device.type == 'cuda':
            initial_gpu = torch.cuda.memory_allocated() / 1024 / 1024  # MB
        
        # 执行推理
        with torch.no_grad():
            output = self.model(batch_data)
        
        # 记录峰值内存
        final_ram = process.memory_info().rss / 1024 / 1024  # MB
        
        memory_info = {
            'ram_usage_mb': final_ram - initial_ram,
            'ram_total_mb': final_ram
        }
        
        if self.device.type == 'cuda':
            peak_gpu = torch.cuda.max_memory_allocated() / 1024 / 1024  # MB
            current_gpu = torch.cuda.memory_allocated() / 1024 / 1024  # MB
            memory_info.update({
                'gpu_peak_mb': peak_gpu,
                'gpu_current_mb': current_gpu,
                'gpu_usage_mb': current_gpu - initial_gpu
            })
        
        return memory_info

    def test_batch_size_scaling(self, batch_sizes: List[int] = [1, 2, 4, 8, 16, 32]) -> List[Dict]:
        """
        测试不同批次大小的性能

        Args:
            batch_sizes: 要测试的批次大小列表

        Returns:
            测试结果列表
        """
        print("\n🔄 测试批次大小缩放性能...")
        results = []

        for batch_size in batch_sizes:
            print(f"  测试批次大小: {batch_size}")

            try:
                # 创建测试数据
                batch_data = self.create_mock_data(batch_size=batch_size, num_nodes=5, num_edges=10)

                # 测量推理时间
                avg_time, min_time, max_time = self.measure_inference_time(batch_data, num_runs=50)

                # 测量内存使用
                memory_info = self.measure_memory_usage(batch_data)

                result = {
                    'batch_size': batch_size,
                    'avg_time_ms': avg_time,
                    'min_time_ms': min_time,
                    'max_time_ms': max_time,
                    'time_per_sample_ms': avg_time / batch_size,
                    'throughput_samples_per_sec': 1000 * batch_size / avg_time,
                    'memory_info': memory_info
                }

                results.append(result)
                print(f"    平均时间: {avg_time:.2f}ms, 每样本: {result['time_per_sample_ms']:.2f}ms")

            except Exception as e:
                print(f"    ❌ 批次大小 {batch_size} 测试失败: {e}")
                results.append({
                    'batch_size': batch_size,
                    'error': str(e)
                })

        self.test_results['batch_size_tests'] = results
        return results

    def test_graph_size_scaling(self, node_counts: List[int] = [1, 3, 5, 10, 15, 20]) -> List[Dict]:
        """
        测试不同图大小的性能

        Args:
            node_counts: 要测试的节点数量列表

        Returns:
            测试结果列表
        """
        print("\n📊 测试图大小缩放性能...")
        results = []

        for num_nodes in node_counts:
            # 边数大约是节点数的2倍（稠密图）
            num_edges = max(0, num_nodes * 2)
            print(f"  测试图大小: {num_nodes}节点, {num_edges}边")

            try:
                # 创建测试数据
                batch_data = self.create_mock_data(batch_size=4, num_nodes=num_nodes, num_edges=num_edges)

                # 测量推理时间
                avg_time, min_time, max_time = self.measure_inference_time(batch_data, num_runs=50)

                # 测量内存使用
                memory_info = self.measure_memory_usage(batch_data)

                result = {
                    'num_nodes': num_nodes,
                    'num_edges': num_edges,
                    'avg_time_ms': avg_time,
                    'min_time_ms': min_time,
                    'max_time_ms': max_time,
                    'memory_info': memory_info
                }

                results.append(result)
                print(f"    平均时间: {avg_time:.2f}ms")

            except Exception as e:
                print(f"    ❌ 图大小 {num_nodes} 测试失败: {e}")
                results.append({
                    'num_nodes': num_nodes,
                    'num_edges': num_edges,
                    'error': str(e)
                })

        self.test_results['graph_size_tests'] = results
        return results

    def test_throughput(self, duration_seconds: int = 10) -> Dict:
        """
        测试模型吞吐量

        Args:
            duration_seconds: 测试持续时间（秒）

        Returns:
            吞吐量测试结果
        """
        print(f"\n⚡ 测试模型吞吐量 ({duration_seconds}秒)...")

        batch_data = self.create_mock_data(batch_size=8, num_nodes=5, num_edges=10)

        start_time = time.time()
        count = 0

        while time.time() - start_time < duration_seconds:
            with torch.no_grad():
                _ = self.model(batch_data)
            count += 1

        elapsed_time = time.time() - start_time
        throughput = count / elapsed_time
        samples_per_sec = throughput * 8  # batch_size = 8

        result = {
            'duration_seconds': elapsed_time,
            'total_inferences': count,
            'inferences_per_second': throughput,
            'samples_per_second': samples_per_sec
        }

        self.test_results['throughput_tests'].append(result)
        print(f"  推理次数: {count}, 推理/秒: {throughput:.2f}, 样本/秒: {samples_per_sec:.2f}")

        return result

    def analyze_model_layers(self) -> Dict:
        """
        分析模型各层的参数数量和计算复杂度

        Returns:
            层级分析结果
        """
        print("\n🔍 分析模型层级结构...")

        layer_analysis = {}
        total_params = 0

        for name, module in self.model.named_modules():
            if len(list(module.children())) == 0:  # 叶子模块
                num_params = sum(p.numel() for p in module.parameters())
                if num_params > 0:
                    layer_analysis[name] = {
                        'type': type(module).__name__,
                        'parameters': num_params,
                        'percentage': 0  # 稍后计算
                    }
                    total_params += num_params

        # 计算百分比
        for layer_info in layer_analysis.values():
            layer_info['percentage'] = (layer_info['parameters'] / total_params) * 100

        # 按参数数量排序
        sorted_layers = dict(sorted(layer_analysis.items(),
                                  key=lambda x: x[1]['parameters'], reverse=True))

        self.test_results['layer_analysis'] = sorted_layers

        # 打印前10个最大的层
        print("  前10个参数最多的层:")
        for i, (name, info) in enumerate(list(sorted_layers.items())[:10]):
            print(f"    {i+1:2d}. {name:40s} | {info['type']:15s} | {info['parameters']:8,} ({info['percentage']:5.1f}%)")

        return sorted_layers

    def generate_visualizations(self, save_dir: str = "test_results") -> None:
        """
        生成可视化图表

        Args:
            save_dir: 保存目录
        """
        print(f"\n📈 生成可视化图表到 {save_dir}...")

        os.makedirs(save_dir, exist_ok=True)

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        # 1. 批次大小 vs 推理时间
        if self.test_results['batch_size_tests']:
            batch_results = [r for r in self.test_results['batch_size_tests'] if 'error' not in r]
            if batch_results:
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

                batch_sizes = [r['batch_size'] for r in batch_results]
                avg_times = [r['avg_time_ms'] for r in batch_results]
                time_per_sample = [r['time_per_sample_ms'] for r in batch_results]

                ax1.plot(batch_sizes, avg_times, 'bo-', linewidth=2, markersize=8)
                ax1.set_xlabel('Batch Size')
                ax1.set_ylabel('Average Inference Time (ms)')
                ax1.set_title('Batch Size vs Total Inference Time')
                ax1.grid(True, alpha=0.3)

                ax2.plot(batch_sizes, time_per_sample, 'ro-', linewidth=2, markersize=8)
                ax2.set_xlabel('Batch Size')
                ax2.set_ylabel('Time per Sample (ms)')
                ax2.set_title('Batch Size vs Time per Sample')
                ax2.grid(True, alpha=0.3)

                plt.tight_layout()
                plt.savefig(f"{save_dir}/batch_size_analysis.png", dpi=300, bbox_inches='tight')
                plt.close()

        # 2. 图大小 vs 推理时间
        if self.test_results['graph_size_tests']:
            graph_results = [r for r in self.test_results['graph_size_tests'] if 'error' not in r]
            if graph_results:
                fig, ax = plt.subplots(1, 1, figsize=(10, 6))

                node_counts = [r['num_nodes'] for r in graph_results]
                avg_times = [r['avg_time_ms'] for r in graph_results]

                ax.plot(node_counts, avg_times, 'go-', linewidth=2, markersize=8)
                ax.set_xlabel('Number of Nodes')
                ax.set_ylabel('Average Inference Time (ms)')
                ax.set_title('Graph Size vs Inference Time')
                ax.grid(True, alpha=0.3)

                plt.tight_layout()
                plt.savefig(f"{save_dir}/graph_size_analysis.png", dpi=300, bbox_inches='tight')
                plt.close()

        # 3. 层级参数分布
        if 'layer_analysis' in self.test_results:
            layer_data = self.test_results['layer_analysis']
            top_layers = dict(list(layer_data.items())[:10])

            fig, ax = plt.subplots(1, 1, figsize=(12, 8))

            names = [name.split('.')[-1] for name in top_layers.keys()]
            params = [info['parameters'] for info in top_layers.values()]

            bars = ax.barh(range(len(names)), params)
            ax.set_yticks(range(len(names)))
            ax.set_yticklabels(names)
            ax.set_xlabel('Number of Parameters')
            ax.set_title('Top 10 Layers by Parameter Count')

            # 添加数值标签
            for i, (bar, param) in enumerate(zip(bars, params)):
                ax.text(bar.get_width() + max(params) * 0.01, bar.get_y() + bar.get_height()/2,
                       f'{param:,}', va='center', fontsize=9)

            plt.tight_layout()
            plt.savefig(f"{save_dir}/layer_parameters.png", dpi=300, bbox_inches='tight')
            plt.close()

        print(f"  ✅ 可视化图表已保存到 {save_dir}/")

    def print_detailed_config(self) -> None:
        """打印详细的模型配置信息"""
        print("\n" + "="*80)
        print("🔧 详细模型配置信息")
        print("="*80)

        config_items = [
            ("模型类型", "GraphTransformerModel"),
            ("设备", str(self.device)),
            ("节点特征维度", self.config.get('ego_node_dim', 'N/A')),
            ("序列特征维度", self.config.get('sequence_dim', 'N/A')),
            ("隐藏层维度", self.config.get('hidden_dim', 'N/A')),
            ("Transformer维度", self.config.get('transformer_dim', 'N/A')),
            ("注意力头数", self.config.get('num_heads', 'N/A')),
            ("Transformer层数", self.config.get('num_transformer_layers', 'N/A')),
            ("GNN层数", self.config.get('num_layers', 'N/A')),
            ("GNN类型", self.config.get('gnn_type', 'N/A')),
            ("Dropout率", self.config.get('dropout', 'N/A')),
            ("动作维度", self.config.get('action_dim', 'N/A')),
            ("总参数数量", f"{self.test_results['model_parameters']:,}"),
            ("模型大小", f"{self.test_results['model_size_mb']:.2f} MB")
        ]

        for key, value in config_items:
            print(f"{key:20s}: {value}")

        print("="*80)

    def save_results(self, save_path: str = "test_results/complexity_results.json") -> None:
        """
        保存测试结果到JSON文件

        Args:
            save_path: 保存路径
        """
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # 添加时间戳
        self.test_results['timestamp'] = time.strftime("%Y-%m-%d %H:%M:%S")

        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)

        print(f"📄 测试结果已保存到: {save_path}")

    def run_full_test_suite(self, save_dir: str = "test_results") -> Dict:
        """
        运行完整的测试套件

        Args:
            save_dir: 结果保存目录

        Returns:
            完整测试结果
        """
        print("\n🧪 开始完整的模型复杂度测试套件")
        print("="*80)

        # 打印配置信息
        self.print_detailed_config()

        # 1. 基础推理时间测试
        print("\n⏱️ 基础推理时间测试...")
        basic_data = self.create_mock_data(batch_size=1, num_nodes=5, num_edges=10)
        avg_time, min_time, max_time = self.measure_inference_time(basic_data, num_runs=100)
        self.test_results['basic_inference'] = {
            'avg_time_ms': avg_time,
            'min_time_ms': min_time,
            'max_time_ms': max_time
        }
        print(f"  平均推理时间: {avg_time:.2f}ms (范围: {min_time:.2f}-{max_time:.2f}ms)")

        # 2. 基础内存使用测试
        print("\n💾 基础内存使用测试...")
        memory_info = self.measure_memory_usage(basic_data)
        self.test_results['basic_memory'] = memory_info
        print(f"  RAM使用: {memory_info.get('ram_usage_mb', 0):.2f}MB")
        if 'gpu_usage_mb' in memory_info:
            print(f"  GPU使用: {memory_info['gpu_usage_mb']:.2f}MB")

        # 3. 层级分析
        self.analyze_model_layers()

        # 4. 批次大小缩放测试
        self.test_batch_size_scaling()

        # 5. 图大小缩放测试
        self.test_graph_size_scaling()

        # 6. 吞吐量测试
        self.test_throughput(duration_seconds=5)

        # 7. 生成可视化
        self.generate_visualizations(save_dir)

        # 8. 保存结果
        self.save_results(f"{save_dir}/complexity_results.json")

        # 9. 打印总结
        self.print_test_summary()

        return self.test_results

    def print_test_summary(self) -> None:
        """打印测试总结"""
        print("\n" + "="*80)
        print("📊 测试总结报告")
        print("="*80)

        # 基础性能
        if 'basic_inference' in self.test_results:
            basic = self.test_results['basic_inference']
            print(f"🚀 基础推理性能:")
            print(f"   平均时间: {basic['avg_time_ms']:.2f}ms")
            print(f"   推理速度: {1000/basic['avg_time_ms']:.1f} FPS")

        # 内存使用
        if 'basic_memory' in self.test_results:
            memory = self.test_results['basic_memory']
            print(f"💾 内存使用:")
            print(f"   模型大小: {self.test_results['model_size_mb']:.2f}MB")
            print(f"   运行时RAM: {memory.get('ram_usage_mb', 0):.2f}MB")
            if 'gpu_usage_mb' in memory:
                print(f"   运行时GPU: {memory['gpu_usage_mb']:.2f}MB")

        # 缩放性能
        batch_tests = self.test_results.get('batch_size_tests', [])
        valid_batch_tests = [t for t in batch_tests if 'error' not in t]
        if valid_batch_tests:
            best_throughput = max(t['throughput_samples_per_sec'] for t in valid_batch_tests)
            print(f"⚡ 最佳吞吐量: {best_throughput:.1f} samples/sec")

        # 参数统计
        print(f"🔢 模型复杂度:")
        print(f"   总参数: {self.test_results['model_parameters']:,}")
        print(f"   模型大小: {self.test_results['model_size_mb']:.2f}MB")

        print("="*80)
        print("✅ 复杂度测试完成！")


def create_default_config() -> Dict:
    """创建默认的模型配置"""
    return {
        'hidden_dim': 128,
        'transformer_dim': 256,
        'num_layers': 2,
        'num_transformer_layers': 4,
        'num_heads': 8,
        'dropout': 0.1,
        'sequence_dim': 72,
        'ego_node_dim': 7,
        'npc_node_dim': 7,
        'action_dim': 2,
        'gnn_type': 'GCN'
    }


def main():
    """主函数"""
    print("🚀 GraphTransformerModel 复杂度测试")
    print("="*80)

    # 创建配置
    config = create_default_config()

    # 创建测试器
    tester = ModelComplexityTester(config)

    # 运行完整测试套件
    results = tester.run_full_test_suite()

    return results


if __name__ == "__main__":
    try:
        results = main()
        print("\n🎉 所有测试完成！")
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
