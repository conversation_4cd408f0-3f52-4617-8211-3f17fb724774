#!/usr/bin/env python3
"""
运行所有模型测试的主脚本
包括复杂度测试和行为测试
"""

import os
import sys
import time
import json
from pathlib import Path
from typing import Dict, Any

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model_complexity_test import ModelComplexityTester
from model_behavior_test import ModelBehaviorTester

def print_system_info():
    """打印系统信息"""
    import torch
    import psutil
    
    print("🖥️ 系统信息")
    print("="*60)
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {'是' if torch.cuda.is_available() else '否'}")
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            print(f"  GPU {i}: {torch.cuda.get_device_name(i)}")
    
    # 内存信息
    memory = psutil.virtual_memory()
    print(f"系统内存: {memory.total / (1024**3):.1f} GB")
    print(f"可用内存: {memory.available / (1024**3):.1f} GB")
    print("="*60)

def create_test_config() -> Dict:
    """创建测试配置"""
    return {
        'hidden_dim': 128,
        'transformer_dim': 256,
        'num_layers': 2,
        'num_transformer_layers': 4,
        'num_heads': 8,
        'dropout': 0.1,
        'sequence_dim': 72,
        'ego_node_dim': 7,
        'npc_node_dim': 7,
        'action_dim': 2,
        'gnn_type': 'GCN'
    }

def run_complexity_tests(config: Dict, save_dir: str) -> Dict:
    """
    运行复杂度测试
    
    Args:
        config: 模型配置
        save_dir: 保存目录
        
    Returns:
        复杂度测试结果
    """
    print("\n" + "="*80)
    print("🔬 开始模型复杂度测试")
    print("="*80)
    
    complexity_dir = os.path.join(save_dir, "complexity")
    os.makedirs(complexity_dir, exist_ok=True)
    
    try:
        tester = ModelComplexityTester(config)
        results = tester.run_full_test_suite(complexity_dir)
        
        print("✅ 复杂度测试完成")
        return results
        
    except Exception as e:
        print(f"❌ 复杂度测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

def run_behavior_tests(config: Dict, save_dir: str, model_path: str = None) -> Dict:
    """
    运行行为测试
    
    Args:
        config: 模型配置
        save_dir: 保存目录
        model_path: 模型路径（可选）
        
    Returns:
        行为测试结果
    """
    print("\n" + "="*80)
    print("🧠 开始模型行为测试")
    print("="*80)
    
    behavior_dir = os.path.join(save_dir, "behavior")
    os.makedirs(behavior_dir, exist_ok=True)
    
    try:
        tester = ModelBehaviorTester(model_path=model_path, config=config)
        results = tester.run_full_behavior_test(behavior_dir)
        
        print("✅ 行为测试完成")
        return results
        
    except Exception as e:
        print(f"❌ 行为测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {"error": str(e)}

def generate_comprehensive_report(complexity_results: Dict, behavior_results: Dict, 
                                config: Dict, save_dir: str) -> None:
    """
    生成综合测试报告
    
    Args:
        complexity_results: 复杂度测试结果
        behavior_results: 行为测试结果
        config: 模型配置
        save_dir: 保存目录
    """
    print("\n📋 生成综合测试报告...")
    
    # 创建综合报告
    comprehensive_report = {
        "test_info": {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "config": config
        },
        "complexity_results": complexity_results,
        "behavior_results": behavior_results
    }
    
    # 保存JSON报告
    report_path = os.path.join(save_dir, "comprehensive_report.json")
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(comprehensive_report, f, indent=2, ensure_ascii=False)
    
    # 生成Markdown报告
    markdown_path = os.path.join(save_dir, "test_report.md")
    generate_markdown_report(comprehensive_report, markdown_path)
    
    print(f"📄 综合报告已保存:")
    print(f"  JSON格式: {report_path}")
    print(f"  Markdown格式: {markdown_path}")

def generate_markdown_report(report: Dict, output_path: str) -> None:
    """生成Markdown格式的测试报告"""
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("# GraphTransformerModel 测试报告\n\n")
        
        # 基本信息
        f.write("## 基本信息\n\n")
        f.write(f"- **测试时间**: {report['test_info']['timestamp']}\n")
        f.write(f"- **模型类型**: GraphTransformerModel\n\n")
        
        # 模型配置
        f.write("## 模型配置\n\n")
        config = report['test_info']['config']
        for key, value in config.items():
            f.write(f"- **{key}**: {value}\n")
        f.write("\n")
        
        # 复杂度测试结果
        complexity = report.get('complexity_results', {})
        if complexity and 'error' not in complexity:
            f.write("## 复杂度测试结果\n\n")
            
            # 基本指标
            f.write("### 基本指标\n\n")
            f.write(f"- **总参数数量**: {complexity.get('model_parameters', 'N/A'):,}\n")
            f.write(f"- **模型大小**: {complexity.get('model_size_mb', 'N/A'):.2f} MB\n")
            
            if 'basic_inference' in complexity:
                basic = complexity['basic_inference']
                f.write(f"- **平均推理时间**: {basic.get('avg_time_ms', 'N/A'):.2f} ms\n")
                f.write(f"- **推理速度**: {1000/basic.get('avg_time_ms', 1):.1f} FPS\n")
            
            if 'basic_memory' in complexity:
                memory = complexity['basic_memory']
                f.write(f"- **运行时内存**: {memory.get('ram_usage_mb', 'N/A'):.2f} MB (RAM)\n")
                if 'gpu_usage_mb' in memory:
                    f.write(f"- **GPU内存**: {memory['gpu_usage_mb']:.2f} MB\n")
            f.write("\n")
            
            # 缩放性能
            if 'batch_size_tests' in complexity:
                f.write("### 批次大小缩放性能\n\n")
                batch_tests = [t for t in complexity['batch_size_tests'] if 'error' not in t]
                if batch_tests:
                    f.write("| 批次大小 | 平均时间(ms) | 每样本时间(ms) | 吞吐量(samples/s) |\n")
                    f.write("|---------|-------------|---------------|------------------|\n")
                    for test in batch_tests:
                        f.write(f"| {test['batch_size']} | {test['avg_time_ms']:.2f} | "
                               f"{test['time_per_sample_ms']:.2f} | {test['throughput_samples_per_sec']:.1f} |\n")
                f.write("\n")
        
        # 行为测试结果
        behavior = report.get('behavior_results', {})
        if behavior and 'error' not in behavior:
            f.write("## 行为测试结果\n\n")
            
            # 输出范围
            if 'output_range' in behavior:
                output_range = behavior['output_range']
                f.write("### 输出范围\n\n")
                
                steering = output_range['steering']
                throttle = output_range['throttle']
                
                f.write("#### 转向 (Steering)\n")
                f.write(f"- **范围**: [{steering['min']:.4f}, {steering['max']:.4f}]\n")
                f.write(f"- **均值**: {steering['mean']:.4f} ± {steering['std']:.4f}\n")
                f.write(f"- **中位数**: {steering['percentiles']['50']:.4f}\n\n")
                
                f.write("#### 油门/刹车 (Throttle/Brake)\n")
                f.write(f"- **范围**: [{throttle['min']:.4f}, {throttle['max']:.4f}]\n")
                f.write(f"- **均值**: {throttle['mean']:.4f} ± {throttle['std']:.4f}\n")
                f.write(f"- **中位数**: {throttle['percentiles']['50']:.4f}\n\n")
            
            # 一致性
            if 'consistency' in behavior:
                consistency = behavior['consistency']
                f.write("### 输出一致性\n\n")
                f.write(f"- **确定性**: {'是' if consistency['is_deterministic'] else '否'}\n")
                f.write(f"- **最大输出差异**: {consistency['max_output_diff']:.8f}\n\n")
            
            # 敏感性
            if 'input_sensitivity' in behavior:
                sensitivity = behavior['input_sensitivity']
                f.write("### 输入敏感性\n\n")
                f.write(f"- **序列特征敏感性**: {sensitivity['sequence_sensitivity']['mean']:.6f}\n")
                f.write(f"- **图结构敏感性**: {sensitivity['graph_sensitivity']['mean']:.6f}\n\n")
        
        f.write("---\n")
        f.write("*报告由自动化测试脚本生成*\n")

def main():
    """主函数"""
    print("🚀 GraphTransformerModel 综合测试套件")
    print("="*80)
    
    # 打印系统信息
    print_system_info()
    
    # 创建配置
    config = create_test_config()
    
    # 设置保存目录
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    save_dir = f"test_results_{timestamp}"
    os.makedirs(save_dir, exist_ok=True)
    
    print(f"\n📁 测试结果将保存到: {save_dir}")
    
    # 模型路径（如果存在的话）
    model_path = "/home/<USER>/rl/RL-IL/dagger_gnn_transformer/dagger_model/best_model.pth"
    if not os.path.exists(model_path):
        print(f"⚠️ 模型文件不存在: {model_path}")
        print("将使用随机初始化的模型进行测试")
        model_path = None
    
    # 运行测试
    start_time = time.time()
    
    # 1. 复杂度测试
    complexity_results = run_complexity_tests(config, save_dir)
    
    # 2. 行为测试
    behavior_results = run_behavior_tests(config, save_dir, model_path)
    
    # 3. 生成综合报告
    generate_comprehensive_report(complexity_results, behavior_results, config, save_dir)
    
    # 计算总时间
    total_time = time.time() - start_time
    
    print("\n" + "="*80)
    print("🎉 所有测试完成！")
    print("="*80)
    print(f"⏱️ 总耗时: {total_time:.1f} 秒")
    print(f"📁 结果保存在: {save_dir}")
    print("="*80)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
