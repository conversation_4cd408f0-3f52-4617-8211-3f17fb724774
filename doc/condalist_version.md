(cat) hui@hui:~$ conda list --revisions
2024-12-10 21:04:58  (rev 0)
    +_libgcc_mutex-0.1 (defaults/linux-64)
    +_openmp_mutex-5.1 (defaults/linux-64)
    +ca-certificates-2024.11.26 (defaults/linux-64)
    +ld_impl_linux-64-2.40 (defaults/linux-64)
    +libffi-3.4.4 (defaults/linux-64)
    +libgcc-ng-11.2.0 (defaults/linux-64)
    +libgomp-11.2.0 (defaults/linux-64)
    +libstdcxx-ng-11.2.0 (defaults/linux-64)
    +ncurses-6.4 (defaults/linux-64)
    +openssl-3.0.15 (defaults/linux-64)
    +pip-24.2 (defaults/linux-64)
    +python-3.9.20 (defaults/linux-64)
    +readline-8.2 (defaults/linux-64)
    +setuptools-75.1.0 (defaults/linux-64)
    +sqlite-3.45.3 (defaults/linux-64)
    +tk-8.6.14 (defaults/linux-64)
    +tzdata-2024b (defaults/noarch)
    +wheel-0.44.0 (defaults/linux-64)
    +xz-5.4.6 (defaults/linux-64)
    +zlib-1.2.13 (defaults/linux-64)

2024-12-10 23:54:48  (rev 1)
     _libgcc_mutex  {0.1 (defaults/linux-64) -> 0.1 (conda-forge/linux-64)}
     _openmp_mutex  {5.1 (defaults/linux-64) -> 4.5 (conda-forge/linux-64)}
     libgcc-ng  {11.2.0 (defaults/linux-64) -> 14.2.0 (conda-forge/linux-64)}
     libgomp  {11.2.0 (defaults/linux-64) -> 14.2.0 (conda-forge/linux-64)}
     libstdcxx-ng  {11.2.0 (defaults/linux-64) -> 14.2.0 (conda-forge/linux-64)}
     openssl  {3.0.15 (defaults/linux-64) -> 3.4.0 (conda-forge/linux-64)}
    +blas-1.0 (conda-forge/linux-64)
    +brotli-python-1.1.0 (conda-forge/linux-64)
    +bzip2-1.0.8 (conda-forge/linux-64)
    +certifi-2024.8.30 (conda-forge/noarch)
    +cffi-1.17.1 (conda-forge/linux-64)
    +charset-normalizer-3.4.0 (conda-forge/noarch)
    +cudatoolkit-11.6.2 (conda-forge/linux-64)
    +ffmpeg-4.3 (pytorch/linux-64)
    +freetype-2.10.4 (conda-forge/linux-64)
    +gmp-6.3.0 (conda-forge/linux-64)
    +gnutls-3.6.13 (conda-forge/linux-64)
    +h2-4.1.0 (conda-forge/noarch)
    +hpack-4.0.0 (conda-forge/noarch)
    +hyperframe-6.0.1 (conda-forge/noarch)
    +icu-73.2 (conda-forge/linux-64)
    +idna-3.10 (conda-forge/noarch)
    +intel-openmp-2023.1.0 (defaults/linux-64)
    +jbig-2.1 (conda-forge/linux-64)
    +jpeg-9e (conda-forge/linux-64)
    +lame-3.100 (conda-forge/linux-64)
    +lcms2-2.12 (conda-forge/linux-64)
    +lerc-2.2.1 (conda-forge/linux-64)
    +libblas-3.9.0 (conda-forge/linux-64)
    +libcblas-3.9.0 (conda-forge/linux-64)
    +libdeflate-1.7 (conda-forge/linux-64)
    +libgcc-14.2.0 (conda-forge/linux-64)
    +libgfortran-14.2.0 (conda-forge/linux-64)
    +libgfortran-ng-14.2.0 (conda-forge/linux-64)
    +libgfortran5-14.2.0 (conda-forge/linux-64)
    +libhwloc-2.11.2 (conda-forge/linux-64)
    +libiconv-1.17 (conda-forge/linux-64)
    +liblapack-3.9.0 (conda-forge/linux-64)
    +libpng-1.6.37 (conda-forge/linux-64)
    +libstdcxx-14.2.0 (conda-forge/linux-64)
    +libtiff-4.3.0 (conda-forge/linux-64)
    +libwebp-base-1.4.0 (conda-forge/linux-64)
    +libxml2-2.13.5 (defaults/linux-64)
    +lz4-c-1.9.3 (conda-forge/linux-64)
    +mkl-2023.1.0 (defaults/linux-64)
    +nettle-3.6 (conda-forge/linux-64)
    +numpy-2.0.2 (conda-forge/linux-64)
    +olefile-0.47 (conda-forge/noarch)
    +openh264-2.1.1 (conda-forge/linux-64)
    +openjpeg-2.4.0 (conda-forge/linux-64)
    +pillow-8.2.0 (conda-forge/linux-64)
    +pycparser-2.22 (conda-forge/noarch)
    +pysocks-1.7.1 (conda-forge/noarch)
    +python_abi-3.9 (conda-forge/linux-64)
    +pytorch-1.12.0 (pytorch/linux-64)
    +pytorch-mutex-1.0 (pytorch/noarch)
    +requests-2.32.3 (conda-forge/noarch)
    +tbb-2021.13.0 (conda-forge/linux-64)
    +torchaudio-0.12.0 (pytorch/linux-64)
    +torchvision-0.13.0 (pytorch/linux-64)
    +typing_extensions-4.12.2 (conda-forge/noarch)
    +urllib3-2.2.3 (conda-forge/noarch)
    +zstandard-0.19.0 (conda-forge/linux-64)
    +zstd-1.5.0 (conda-forge/linux-64)

2024-12-11 00:33:33  (rev 2)
     certifi  {2024.8.30 (conda-forge/noarch) -> 2024.8.30 (defaults/linux-64)}
    +cuda-cudart-12.4.127 (nvidia/linux-64)
    +cuda-nvrtc-12.4.127 (nvidia/linux-64)
    +cuda-python-12.6.2 (nvidia/linux-64)

2024-12-11 00:35:08  (rev 3)
     numpy  {2.0.2 (conda-forge/linux-64) -> 1.21.6 (defaults/linux-64)}
    +mkl-service-2.4.0 (defaults/linux-64)
    +mkl_fft-1.3.11 (defaults/linux-64)
    +mkl_random-1.2.8 (defaults/linux-64)
    +numpy-base-1.21.6 (defaults/linux-64)

2024-12-18 15:41:13  (rev 4)
     ca-certificates  {2024.11.26 (defaults/linux-64) -> 2024.12.14 (conda-forge/linux-64)}
     certifi  {2024.8.30 (defaults/linux-64) -> 2024.12.14 (conda-forge/noarch)}
    -numpy-base-1.21.6 (defaults/linux-64)
    -pillow-8.2.0 (conda-forge/linux-64)
    -pytorch-1.12.0 (pytorch/linux-64)
    -setuptools-75.1.0 (defaults/linux-64)
    +asttokens-3.0.0 (conda-forge/noarch)
    +comm-0.2.2 (conda-forge/noarch)
    +debugpy-1.8.11 (conda-forge/linux-64)
    +decorator-5.1.1 (conda-forge/noarch)
    +exceptiongroup-1.2.2 (conda-forge/noarch)
    +executing-2.1.0 (conda-forge/noarch)
    +importlib-metadata-8.5.0 (conda-forge/noarch)
    +ipykernel-6.29.5 (conda-forge/noarch)
    +ipython-8.18.1 (conda-forge/noarch)
    +jedi-0.19.2 (conda-forge/noarch)
    +jupyter_client-8.6.3 (conda-forge/noarch)
    +jupyter_core-5.7.2 (conda-forge/noarch)
    +keyutils-1.6.1 (conda-forge/linux-64)
    +krb5-1.21.3 (conda-forge/linux-64)
    +libedit-3.1.20191231 (conda-forge/linux-64)
    +libsodium-1.0.20 (conda-forge/linux-64)
    +matplotlib-inline-0.1.7 (conda-forge/noarch)
    +nest-asyncio-1.6.0 (conda-forge/noarch)
    +packaging-24.2 (conda-forge/noarch)
    +parso-0.8.4 (conda-forge/noarch)
    +pexpect-4.9.0 (conda-forge/noarch)
    +pickleshare-0.7.5 (conda-forge/noarch)
    +platformdirs-4.3.6 (conda-forge/noarch)
    +prompt-toolkit-3.0.48 (conda-forge/noarch)
    +psutil-6.1.0 (conda-forge/linux-64)
    +ptyprocess-0.7.0 (conda-forge/noarch)
    +pure_eval-0.2.3 (conda-forge/noarch)
    +pygments-2.18.0 (conda-forge/noarch)
    +python-dateutil-2.9.0.post0 (conda-forge/noarch)
    +pyzmq-26.2.0 (conda-forge/linux-64)
    +six-1.17.0 (conda-forge/noarch)
    +stack_data-0.6.3 (conda-forge/noarch)
    +tornado-6.4.2 (conda-forge/linux-64)
    +traitlets-5.14.3 (conda-forge/noarch)
    +wcwidth-0.2.13 (conda-forge/noarch)
    +zeromq-4.3.5 (conda-forge/linux-64)
    +zipp-3.21.0 (conda-forge/noarch)

2024-12-27 12:22:56  (rev 5)
     ffmpeg  {4.3 (pytorch/linux-64) -> 7.0.1 (conda-forge/linux-64)}
     freetype  {2.10.4 (conda-forge/linux-64) -> 2.12.1 (conda-forge/linux-64)}
     gnutls  {3.6.13 (conda-forge/linux-64) -> 3.7.9 (conda-forge/linux-64)}
     libpng  {1.6.37 (conda-forge/linux-64) -> 1.6.43 (conda-forge/linux-64)}
     nettle  {3.6 (conda-forge/linux-64) -> 3.9.1 (conda-forge/linux-64)}
     openh264  {2.1.1 (conda-forge/linux-64) -> 2.4.1 (conda-forge/linux-64)}
     zlib  {1.2.13 (defaults/linux-64) -> 1.2.13 (conda-forge/linux-64)}
    +aom-3.9.1 (conda-forge/linux-64)
    +cairo-1.18.0 (conda-forge/linux-64)
    +dav1d-1.2.1 (conda-forge/linux-64)
    +expat-2.6.4 (conda-forge/linux-64)
    +font-ttf-dejavu-sans-mono-2.37 (conda-forge/noarch)
    +font-ttf-inconsolata-3.000 (conda-forge/noarch)
    +font-ttf-source-code-pro-2.038 (conda-forge/noarch)
    +font-ttf-ubuntu-0.83 (conda-forge/noarch)
    +fontconfig-2.14.2 (conda-forge/linux-64)
    +fonts-conda-ecosystem-1 (conda-forge/noarch)
    +fonts-conda-forge-1 (conda-forge/noarch)
    +fribidi-1.0.10 (conda-forge/linux-64)
    +gettext-0.22.5 (conda-forge/linux-64)
    +gettext-tools-0.22.5 (conda-forge/linux-64)
    +graphite2-1.3.13 (conda-forge/linux-64)
    +harfbuzz-8.5.0 (conda-forge/linux-64)
    +libabseil-20240116.2 (conda-forge/linux-64)
    +libasprintf-0.22.5 (conda-forge/linux-64)
    +libasprintf-devel-0.22.5 (conda-forge/linux-64)
    +libass-0.17.1 (conda-forge/linux-64)
    +libdrm-2.4.124 (conda-forge/linux-64)
    +libexpat-2.6.4 (conda-forge/linux-64)
    +libgettextpo-0.22.5 (conda-forge/linux-64)
    +libgettextpo-devel-0.22.5 (conda-forge/linux-64)
    +libglib-2.80.2 (conda-forge/linux-64)
    +libidn2-2.3.7 (conda-forge/linux-64)
    +libopenvino-2024.1.0 (conda-forge/linux-64)
    +libopenvino-auto-batch-plugin-2024.1.0 (conda-forge/linux-64)
    +libopenvino-auto-plugin-2024.1.0 (conda-forge/linux-64)
    +libopenvino-hetero-plugin-2024.1.0 (conda-forge/linux-64)
    +libopenvino-intel-cpu-plugin-2024.1.0 (conda-forge/linux-64)
    +libopenvino-intel-gpu-plugin-2024.1.0 (conda-forge/linux-64)
    +libopenvino-intel-npu-plugin-2024.1.0 (conda-forge/linux-64)
    +libopenvino-ir-frontend-2024.1.0 (conda-forge/linux-64)
    +libopenvino-onnx-frontend-2024.1.0 (conda-forge/linux-64)
    +libopenvino-paddle-frontend-2024.1.0 (conda-forge/linux-64)
    +libopenvino-pytorch-frontend-2024.1.0 (conda-forge/linux-64)
    +libopenvino-tensorflow-frontend-2024.1.0 (conda-forge/linux-64)
    +libopenvino-tensorflow-lite-frontend-2024.1.0 (conda-forge/linux-64)
    +libopus-1.3.1 (conda-forge/linux-64)
    +libpciaccess-0.18 (conda-forge/linux-64)
    +libprotobuf-4.25.3 (conda-forge/linux-64)
    +libtasn1-4.19.0 (conda-forge/linux-64)
    +libunistring-0.9.10 (conda-forge/linux-64)
    +libuuid-2.38.1 (conda-forge/linux-64)
    +libva-2.21.0 (conda-forge/linux-64)
    +libvpx-1.14.1 (conda-forge/linux-64)
    +libxcb-1.15 (conda-forge/linux-64)
    +libzlib-1.2.13 (conda-forge/linux-64)
    +ocl-icd-2.3.2 (conda-forge/linux-64)
    +opencl-headers-2024.10.24 (conda-forge/linux-64)
    +p11-kit-0.24.1 (conda-forge/linux-64)
    +pcre2-10.43 (conda-forge/linux-64)
    +pixman-0.44.2 (conda-forge/linux-64)
    +pthread-stubs-0.4 (conda-forge/linux-64)
    +pugixml-1.14 (conda-forge/linux-64)
    +snappy-1.2.1 (conda-forge/linux-64)
    +svt-av1-2.1.0 (conda-forge/linux-64)
    +x264-1!164.3095 (conda-forge/linux-64)
    +x265-3.5 (conda-forge/linux-64)
    +xorg-fixesproto-5.0 (conda-forge/linux-64)
    +xorg-kbproto-1.0.7 (conda-forge/linux-64)
    +xorg-libice-1.1.2 (conda-forge/linux-64)
    +xorg-libsm-1.2.5 (conda-forge/linux-64)
    +xorg-libx11-1.8.9 (conda-forge/linux-64)
    +xorg-libxau-1.0.12 (conda-forge/linux-64)
    +xorg-libxdmcp-1.1.5 (conda-forge/linux-64)
    +xorg-libxext-1.3.4 (conda-forge/linux-64)
    +xorg-libxfixes-5.0.3 (conda-forge/linux-64)
    +xorg-libxrender-0.9.11 (conda-forge/linux-64)
    +xorg-renderproto-0.11.1 (conda-forge/linux-64)
    +xorg-xextproto-7.3.0 (conda-forge/linux-64)
    +xorg-xproto-7.0.31 (conda-forge/linux-64)

