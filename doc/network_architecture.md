# 网络架构图

```mermaid
graph TB
    subgraph Input
        I[观测空间输入] --> E[Embedding层<br/>Linear层]
    end

    subgraph Transformer
        E --> TB[Transformer Block]
        
        subgraph Transformer Block详情
            MHA[多头注意力层<br/>num_heads=8]
            N1[Layer Norm]
            FF[前馈网络<br/>dim=64]
            N2[Layer Norm]
            
            MHA --> N1
            N1 --> FF
            FF --> N2
        end
    end

    subgraph Output
        TB --> Split
        Split --> P[策略输出<br/>Linear(256) -> ReLU -> Linear(num_outputs)]
        Split --> V[价值输出<br/>Linear(256) -> ReLU -> Linear(1)]
    end

classDef block fill:#f9f,stroke:#333,stroke-width:4px
classDef transformer fill:#bbf,stroke:#333,stroke-width:2px
class TB transformer
```

## 网络参数配置
- attention_dim: 64 
- attention_num_heads: 8
- attention_num_transformer_units: 1
- attention_head_dim: 32
- attention_memory_inference: 50
- attention_memory_training: 50
- attention_position_wise_mlp_dim: 64

## 训练参数
- sequence_length: 20
- train_batch_size: 128 * 8
- learning_rate: 1e-5
- beta: 0.0
- grad_clip: 0.5
- gamma: 0.99
