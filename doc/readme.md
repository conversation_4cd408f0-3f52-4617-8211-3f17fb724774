1. 改了损失函(为均)误差,不管怎么训练基本保持在0.33
2. 保存的数据中 取消obs压缩,无用
3. 保存数据文件名修改
4. 收集数据取消了渲染速度)倍

#TODO
1. 看输入数据是否有问题,样本和标签


一条数据
{"type": "SampleBatch", 
"obs": [
    [0.2916666567325592, 0.2916666567325592, 0.5, 0.012345679104328156, 0.5, 0.5, 0.5, 0.0, 0.5, 0.550000011920929, 0.5, 0.0, 0.5, 0.5, 0.949999988079071, 0.5, 0.0, 0.5, 0.5, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0]
    ], 

"actions": [[-0.5021536350250244, -0.7715085744857788]], 

"unroll_id": [0]
}



发现同样的训练参数,数据量小(100)的loss有下降趋势,数据多了(10000),似乎一直维持在1.7
不对  小数据量后面直接炸了
![alt text](image-1.png)


4. 训练结果  前轮抽搐
1e-5
        model={
                    "fcnet_hiddens": [2560, 2560, 2560],  # 增加隐藏层和神经元数量
                    # "vf_share_layers": False,
                }
            
        )
        # config.rollouts(num_rollout_workers=2)
        # 配置评估参数
        config.evaluation(
            evaluation_num_workers=5,  # 影响训练速度,但是写大了爆显存
            evaluation_duration=5
        )
        # 配置资源
        config.resources(
            num_gpus=1,
            num_cpus_per_worker=4,
            num_gpus_per_worker=0.1,
        )


![alt text](image-3.png)




"""
1. 1w条数据 500次迭代后的模型,能跑起来了但是撞马路,不抖动.  应该是MSE损失
/home/<USER>/data/bc_models/BC_iteration_499_model.pth2024-12-18-17-56-19/checkpoint_000500

2. 1w条数据 1000次迭代后的模型,原始损失函数. 1e-3学习率  loss波动很大  刚开就寄了
/home/<USER>/data/bc_models/BC_iteration_2221_model.pth2024-12-18-20-28-49/checkpoint_002222

3. 1w条数据 2222次迭代后的模型,MSE损失函数. 1e-5学习率  loss正常,前轮抖动异常
/home/<USER>/data/bc_models/BC_iteration_2221_model.pth2024-12-18-20-46-52/checkpoint_002222

4. 1w  加了3个隐藏层,前轮抽搐见readme
/home/<USER>/data/bc_models/BC_iteration_2221_model.pth2024-12-21-21-28-18/checkpoint_002222


5. 1w 原始损失函数 固定数据 偶尔撞
![alt text](image-4.png) 
/home/<USER>/data/bc_models/BC_iteration_999_model.pth2024-12-22-00-23-09/checkpoint_001000


6. 1w 随机地图数据,跑的效果差,经常outroad
/home/<USER>/data/bc_models/BC_iteration_999_model.pth2024-12-22-00-48-49/checkpoint_001000
![alt text](image-5.png)


7. # 20w条固定地图数据,迭代1500次,车轮不抖 偶尔out_road
/home/<USER>/data/bc_models/BC_iteration_1499_model.pth2024-12-22-16-17-04/checkpoint_001500
![alt text](image-6.png)

# 模仿学习训练效果
1. 100轮数据  随机线路 29% 默认线路 0 /home/<USER>/data/bc_models/BC_iteration_1499_model.pth2024-12-26-16-59-22/checkpoint_001500
2. 1000 随机 13%  /home/<USER>/data/bc_models/BC_iteration_1499_model.pth2024-12-26-15-55-05/checkpoint_001500
3. 1000 随机 0%  /home/<USER>/data/bc_models/BC_iteration_1999_model.pth2024-12-26-17-47-30/checkpoint_002000
![alt text](image.png)
4. 200 13%  '/home/<USER>/data/bc_models/BC_iteration_1999_model.pth2024-12-26-21-50-54/checkpoint_002000
5. 200 44%   限速20 78% 限速10 96% /home/<USER>/data/bc_models/BC_iteration_1999_model.pth2024-12-26-22-25-38/checkpoint_002000
    加了这个参数 训练   *bc_logstd_coeff*    一个系数，鼓励更高的动作分布熵进行探索  tune.grid_search
                lr=1e-6,
                grad_clip=1,
                train_batch_size=self.train_batch_size,
                bc_logstd_coeff = 1,
![alt text](image-1.png)
6. 1000 25% /home/<USER>/data/bc_models/BC_iteration_399_model.pth2024-12-26-23-23-35/checkpoint_000400
7. 500  23% /home/<USER>/data/bc_models/BC_iteration_599_model.pth2024-12-26-23-46-29/checkpoint_000600**
8. 200  不限速success: 561, fail: 439, success rate: 0.561  限速20 100% /home/<USER>/data/bc_models/BC_iteration_3332_model.pth2024-12-27-15-49-52/checkpoint_003333  数据 /home/<USER>/rl/RL-IL/bc_data/200/output-2024-12-26_21-16-35_worker-0_0.json

9.300  /home/<USER>/data/bc_models/BC_iteration_5999_model.pth2024-12-29-22-41-49/checkpoint_006000 1000-6000都有  1000 大概30% 2000大概20%
![alt text](image-3.png)

10. 200 23% /home/<USER>/data/bc_models/BC_iteration_4999_model.pth2024-12-30-20-10-24/checkpoint_005000 数据 /home/<USER>/rl/RL-IL/bc_data/200/output-2024-12-26_21-16-35_worker-0_0.json
11. 200  42%   /home/<USER>/data/bc_models/BC_iteration_3332_model.pth2024-12-30-19-47-10/checkpoint_003333  数据 /home/<USER>/rl/RL-IL/bc_data/200/output-2024-12-26_21-16-35_worker-0_0.json
12. reward 200 26% /home/<USER>/data/bc_models/BC_iteration_3999_12-30-20-33/checkpoint_004000

![alt text](image-4.png)
13.  没有提升了,训练次数,训练数据增加都没用
14. 200 23% /home/<USER>/data/bc_models/BC_iteration_3499_model.pth2024-12-31-10-50-03/checkpoint_003500
15  200 success: 207, fail: 144, success rate: 0.5897  /home/<USER>/data/bc_models/BC_iteration_2999_model.pth2024-12-31-11-00-05/checkpoint_003000
16  200 半路刹车 跑不完测试  /home/<USER>/data/bc_models/BC_iteration_2499_model.pth2024-12-31-11-14-15/checkpoint_002500
17 取消rollout,bc_logstd_coeff = 2, 车轮抖 home/hui/rl/RL-IL/bc_models/BC_iteration_2999_model.pth2024-12-31-11-28-29/checkpoint_003000
18 取消rollout,bc_logstd_coeff = 1 15% 抖动情况好了很多/home/<USER>/data/bc_models/BC_iteration_2999_model.pth2024-12-31-11-37-20/checkpoint_003000
19 rollout=2,bc_logstd_coeff = 1 参数和15一样 但是28% /home/<USER>/data/bc_models/BC_iteration_2999_model.pth2024-12-31-11-46-40/checkpoint_003000
20 rollout=2,bc_logstd_coeff = 0.5    /home/<USER>/data/bc_models/BC_iteration_2999_model.pth2024-12-31-12-00-29/checkpoint_003000
21  [15] rl后的模型  77%  /home/<USER>/data/bc_models/checkpoint_003130


/home/<USER>/rl/RL-IL/ray_results/PPO_MetaDrive_Optimization/PPO_GymEnvWrapper_1f988_00000_0_2024-12-31_19-05-19/checkpoint_005500  success: 99, fail: 1, success rate: 0.99

# bc训练过拟合检查
200epsoid 
![alt text](image-2.png)


# dagger训练
1. /home/<USER>/rl/RL-IL/dagger_saved_models/dagger_model_1228_16-11
--------------------------------
| batch_size        | 2560     |
| bc/               |          |
|    batch          | 109      |
|    ent_loss       | -0.00262 |
|    entropy        | 2.62     |
|    epoch          | 9        |
|    l2_loss        | 0        |
|    l2_norm        | 128      |
|    loss           | 1.62     |
|    neglogp        | 1.62     |
|    prob_true_act  | 0.198    |
|    samples_so_far | 281600   |
| rollout/          |          |
|    return_max     | 56.1     |
|    return_mean    | 16       |
|    return_min     | 0.699    |
|    return_std     | 20.4     |
--------------------------------

2. /home/<USER>/rl/RL-IL/bc_data/200/output-2024-12-28_15-12-38_worker-0_0.json
/home/<USER>/rl/RL-IL/dagger_saved_models/dagger_model_1228_19-27
--------------------------------
| batch_size        | 2560     |
| bc/               |          |
|    batch          | 229      |
|    ent_loss       | -0.00216 |
|    entropy        | 2.16     |
|    epoch          | 9        |
|    l2_loss        | 0        |
|    l2_norm        | 128      |
|    loss           | 1.16     |
|    neglogp        | 1.16     |
|    prob_true_act  | 0.314    |
|    samples_so_far | 588800   |
| rollout/          |          |
|    return_max     | 58.2     |
|    return_mean    | 27.6     |
|    return_min     | 1.25     |
|    return_std     | 23.9     |
--------------------------------
[53:10, 13.87s/batch]

# 初始偏移位置可设置
![alt text](image-7.png)

## 模仿＋Rl效果不好的分析
1. 是否模仿学习outroad也学进去了? 影响rl
2. idm测也可以调参  模仿学习和强化学习参数是否一样?
3. 为什么不用ppo专家策略
4.是否需要增加网络层数?

# 保存数据
- 压缩省空间,但是dagger暂时不能读取压缩的

# 新增功能
- 收集数据时填写成功到达数 而不是step
- 只收集成功到达的数据
- 批量快速测试模仿学习效果脚本
- dagger联合metadrive训练
- dagger模型测试



# 模仿加入rl的方法
1. 
2. 把rl分成2个阶段

不是 restore
不是
Il产生的数据用于rl学习1
![alt text](image-5.png)



# rl的action替换为模仿
1. 没有统一环境配置

![alt text](image-6.png)


加入模仿学习
![alt text](image-7.png)


没有模仿学习
![alt text](image-8.png)


- 原版RL
- ![alt text](image-9.png)
- ![alt text](image-13.png)

- 刚开始使用模仿学习的action,当step总数达到阈值切换强化学习
- ![alt text](image-10.png)

- 逐渐使用模仿学习

- ![alt text](image-11.png)

- ![alt text](image-12.png)
  

# 改了奖励函数

![alt text](image-15.png)

```bash
python 
    def reward_function(self, vehicle_id: str):
        """前期BC引导,后期环境探索的混合奖励函数"""
        # 获取原始环境奖励
        original_reward, info = super().reward_function(vehicle_id)
        
        # 计算BC相似度 
        vehicle = self.agents[vehicle_id]
        obs = self.observations[vehicle_id].observe(vehicle)
        bc_action = self.model_wrapper.algo.compute_single_action(obs)
        current_action = vehicle.last_current_action[1]
        bc_similarity = -np.mean((current_action - bc_action) ** 2)

        
        # 阶段转换
        self.step_num_total += 1
        early_stage = self.step_num_total < self.bc_decay_steps
        # early_stage = bc_similarity < 
        # print(self.step_num_total)
        if early_stage:
            # 前期:主要使用BC奖励
            bc_weight = 1.0
            total_reward = 0.1 * original_reward + bc_similarity 
        else:
            # 后期:完全使用环境奖励
            bc_weight = 0
            total_reward = original_reward
            # if self.step_num_total % 333 == 1:
            #     print('not early_stage ')
            
        # 更新信息
        info.update({
            "original_reward": original_reward,
            "bc_similarity": bc_similarity,
            "bc_weight": bc_weight,
            "stage": "early" if early_stage else "late"
        })
        
        # print(total_reward)
        if total_reward > 0.3:
            # print(f'bc_similarity:{bc_similarity}')
            self.bc_decay_steps = 0
            print(total_reward)
        
        
        return total_reward, info

```

# 提高原始学习奖励权重好很多            total_reward =  original_reward + bc_similarity 
batchsize 1024
![alt text](image-16.png)


batchsize 4096 太慢了
![alt text](image-18.png)

batchsize 1024
1e-5
![alt text](image-19.png)

1e-4
![alt text](image-20.png)

纯RL 参数同上
![alt text](image-21.png)

加入模仿学习
![alt text](image-22.png)


刚开始只学模仿学习,并且更晚自学
![alt text](image-23.png)

