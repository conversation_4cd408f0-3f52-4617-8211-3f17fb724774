=== 全部属性列表 ===
CHASSIS_TO_WHEEL_AXIS: 0.2
DEFAULT_HEIGHT: 1.19
DEFAULT_LENGTH: 4.515
DEFAULT_WIDTH: 1.852
FRONT_WHEELBASE: 1.05234
GUARDRAIL: GU<PERSON><PERSON>AIL
HEIGHT: 1.19
LATERAL_TIRE_TO_CENTER: 0.815
LENGTH: 4.515
LIGHT_GREEN: TRAFFIC_LIGHT_GREEN
LIGHT_POSITION: (-0.67, 1.62, 0.05)
MASS: 1100
MATERIAL_COLOR_COEFF: 1.6
MATERIAL_METAL_COEFF: 0.1
MATERIAL_ROUGHNESS: 0.8
MATERIAL_SHININESS: 128
MAX_LENGTH: 10
MAX_RAND_INT: 65536
MAX_STEERING: 60
MAX_WIDTH: 2.5
REAR_WHEELBASE: 1.4166
SEMANTIC_LABEL: CAR
SPEED_BUMP: SPEED_BUMP
STEERING_INCREMENT: 0.05
STOP_SIGN: STOP_SIGN
SUSPENSION_LENGTH: 15
SUSPENSION_STIFFNESS: 40
TIRE_MODEL_CORRECT: 1
TIRE_RADIUS: 0.313
TIRE_TWO_SIDED: False
TIRE_WIDTH: 0.25
WIDTH: 1.852
config: {
    'max_steering': 40.0, 
    'max_brake_force': 98.19999694824219,
    'max_engine_force': 768.2000122070312, 
    'max_speed_km_h': 80.0, 
    'wheel_friction': 0.8999999761581421, 'vehicle_model': 'default', 'enable_reverse': False, 'show_navi_mark': True, 'show_dest_mark': True, 'show_line_to_dest': False, 'show_line_to_navi_mark': True, 'show_navigation_arrow': True, 'use_special_color': True, 'no_wheel_friction': False, 'image_source': 'rgb_camera', 'navigation_module': <class 'metadrive.component.navigation_module.node_network_navigation.NodeNetworkNavigation'>, 'spawn_lane_index': ('>', '>>', 1), 'destination': None, 'spawn_longitude': 5.0, 'spawn_lateral': 0.0, 'spawn_position_heading': None, 'spawn_velocity': None, 'spawn_velocity_car_frame': False, 'overtake_stat': False, 'random_color': False, 'width': None, 'length': None, 'height': None, 'mass': None, 'scale': None, 'top_down_width': None, 'top_down_length': None, 'lidar': {'num_lasers': 240, 'distance': 50, 'num_others': 0, 'gaussian_noise': 0.0, 'dropout_prob': 0.0, 'add_others_navi': False}, 'side_detector': {'num_lasers': 0, 'distance': 50, 'gaussian_noise': 0.0, 'dropout_prob': 0.0}, 'lane_line_detector': {'num_lasers': 0, 'distance': 20, 'gaussian_noise': 0.0, 'dropout_prob': 0.0}, 'show_lidar': False, 'show_side_detector': False, 'show_lane_line_detector': False, 'light': False, 'norm_pixel': True, 'random_agent_model': True}
coordinates_debug_np: None
current_action: (0.0, 0.0)
dist_to_left_side: 5.25
dist_to_right_side: 5.25
BulletRigidBodyNode VEHICLE (1 shapes) active mass=1100]
enable_reverse: False
energy_consumption: 0.0
engine: <metadrive.engine.base_engine.BaseEngine object at 0x7f0b0d5d77f0>
expert_takeover: False

heading: (1.0, 0.0)

height: 0.5950000286102295

last_action: (0.0, 0.0)
last_current_action: deque([(0.0, 0.0), (0.0, 0.0)], maxlen=2)
last_heading_dir: (1.0, 0.0)
last_position: [5.  3.5]
last_speed: 0.0
last_velocity: [0. 0.]
max_speed_km_h: 80.0
max_speed_m_s: 22.22222222222222
max_steering: 40.0
metadrive_type: VEHICLE
model_collection: {'ferra/vehicle.gltf': Scene}
name: cdbc1c81-b5e0-49a1-8a1a-1eb2964dfacb
navigation: <metadrive.component.navigation_module.node_network_navigation.NodeNetworkNavigation object at 0x7f0968c44e80>
need_show_coordinates: False
np_random: RandomState(MT19937)
on_broken_line: False
on_crosswalk: False
on_lane: True
on_white_continuous_line: False
on_yellow_continuous_line: False
origin: render/world_np/VEHICLE
out_of_route: False
overspeed: False
panda_color: (0.00784313725490196, 0.6196078431372549, 0.45098039215686275)
parse_light_status: <bound method MetaDriveType.parse_light_status of <class 'metadrive.component.vehicle.vehicle_type.DefaultVehicle'>>
path: ('ferra/vehicle.gltf', (1, 1, 1), (0, 0.075, 0.0), (0, 0, 0))
pitch: 0.0
position: (5.0, 3.5)
random_rename: <bound method BaseObject.random_rename of DefaultVehicle, ID:cdbc1c81-b5e0-49a1-8a1a-1eb2964dfacb>
random_seed: 40593
red_light: False
spawn_place: [5.  3.5]
speed: 0.0
speed_km_h: 0.0
static_nodes: []
steering: 0
top_down_length: 4.515
top_down_width: 1.852
use_render_pipeline: False
velocity: [0. 0.]
velocity_km_h: [0. 0.]