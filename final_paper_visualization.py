#!/usr/bin/env python3
"""
生成最终的论文可视化图片
"""

import sys
import os
sys.path.append('dagger_gnn_transformer')

from dagger_gnn_transformer.make_pic import create_paper_visualization

def generate_final_paper_figures():
    """生成最终的高质量论文图片"""
    
    print("🎯 生成最终论文可视化图片")
    print("📐 高分辨率: 1200x900 → 400x300 (每张小图)")
    print("🔤 大字体: 标题36px, 副标题24px, 信息24px")
    print("=" * 60)
    
    # 配置1: 6张图片，最佳质量和清晰度
    print("\n📸 配置1: 6张图片 (3x2布局) - 推荐用于论文")
    print("   特点: 图片大、字体清晰、布局美观")
    create_paper_visualization(
        num_episodes=2,  # 生成成功和失败案例
        vis_save_interval=3.0,  # 每3秒一张，确保关键时刻
        vis_max_images=6,
        output_dir="final_6pics_recommended"
    )
    
    # 配置2: 9张图片，详细展示
    print("\n📸 配置2: 9张图片 (3x3布局) - 详细分析用")
    print("   特点: 更多细节、完整过程")
    create_paper_visualization(
        num_episodes=1,
        vis_save_interval=2.0,  # 每2秒一张
        vis_max_images=9,
        output_dir="final_9pics_detailed"
    )
    
    # 配置3: 4张图片，重点展示
    print("\n📸 配置3: 4张图片 (2x2布局) - 重点时刻")
    print("   特点: 最大图片尺寸、突出关键时刻")
    create_paper_visualization(
        num_episodes=1,
        vis_save_interval=5.0,  # 每5秒一张，只保留关键时刻
        vis_max_images=4,
        output_dir="final_4pics_highlights"
    )
    
    print("\n🎉 最终论文图片生成完成！")
    print("\n📊 技术规格:")
    print("  ✅ 原始分辨率: 1200x900 (高清)")
    print("  ✅ 小图尺寸: 400x300 (清晰)")
    print("  ✅ 字体大小: 标题36px, 副标题24px")
    print("  ✅ 边框效果: 2px灰色边框")
    print("  ✅ 最后一帧: 确保包含终点画面")
    print("  ✅ 无时间戳: 干净的图片效果")
    
    print("\n📁 生成的目录:")
    print("  🌟 final_6pics_recommended/  (推荐用于论文)")
    print("  📋 final_9pics_detailed/     (详细分析用)")
    print("  🎯 final_4pics_highlights/   (重点展示用)")
    
    print("\n💡 论文使用建议:")
    print("  📄 主要展示: 使用6张图片版本")
    print("  🔍 详细分析: 使用9张图片版本")
    print("  🎯 重点说明: 使用4张图片版本")
    print("  📊 对比分析: 使用多个episode的图片")

if __name__ == "__main__":
    generate_final_paper_figures()
