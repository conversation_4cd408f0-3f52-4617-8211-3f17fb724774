import os
import json
import tempfile
import numpy as np
import torch
from torch.utils.data import DataLoader, Dataset
from metadrive.policy.idm_policy import IDMPolicy

from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.envs.gym_wrapper import createGymWrapper
from stable_baselines3 import PPO
from stable_baselines3.common.vec_env import DummyVecEnv
import gym  # 使用 gym 而不是 gymnasium
# imitation 库的相关导入
from imitation.algorithms import bc
from imitation.algorithms.dagger import SimpleDAggerTrainer
from imitation.data.wrappers import RolloutInfoWrapper
from imitation.util.util import make_vec_env

###############################################################################
# 1. 数据集定义
###############################################################################
class BCDataset(Dataset):
    def __init__(self, data_path):
        with open(data_path, 'r') as f:
            self.data = json.load(f)
        self.observations = self.data['obs']
        self.actions = self.data['actions']
    
    def __len__(self):
        return len(self.observations)
    
    def __getitem__(self, idx):
        observation = self.observations[idx]
        action = self.actions[idx]
        observation = torch.tensor(observation, dtype=torch.float32)
        action = torch.tensor(action, dtype=torch.long)  # DAgger/BC中常用离散动作
        return observation, action

###############################################################################
# 2. 环境创建
###############################################################################
def env_creator(env_config=None):
    config = MetaDriveEnv.default_config()
    config.update({
        "use_render": False,
        "manual_control": False,
        "discrete_action": True,  # 设置为离散动作空间
    })
    env = createGymWrapper(MetaDriveEnv)(config=config)
    assert isinstance(env.observation_space, gym.spaces.Box), "Observation space must be of type gym.spaces.Box"
    assert isinstance(env.action_space, gym.spaces.Discrete), "Action space must be of type gym.spaces.Discrete"
    return env

class CustomEnvWrapper(gym.Wrapper):
    def __init__(self, env):
        super().__init__(env)
        
    def reset(self, **kwargs):
        result = self.env.reset(**kwargs)
        if len(result) > 2:
            # 只返回 observation 和 info
            return result[0], result[2]
        return result

    def step(self, action):
        obs, reward, done, info = self.env.step(action)
        return obs, reward, done, info

def make_metadrive_env():
    config = MetaDriveEnv.default_config()
    config.update({
        "use_render": False,
        "manual_control": False,
        "discrete_action": True,
    })
    base_env = createGymWrapper(MetaDriveEnv)(config=config)
    # 使用自定义包装器包装环境
    wrapped_env = CustomEnvWrapper(base_env)
    venv = DummyVecEnv([lambda: wrapped_env])
    return venv

def dagger_training_example(data_path: str, total_steps: int = 8000):
    rng = np.random.default_rng(0)
    venv = make_metadrive_env()

    # dataset + dataloader
    dataset = BCDataset(data_path)
    dataloader = DataLoader(dataset, batch_size=64, shuffle=True)

    print(f'venv.observation_space:{venv.observation_space}')
    print(f'venv.action_space:{venv.action_space}')

    # 创建 BC 对象
    bc_trainer = bc.BC(
        observation_space=venv.observation_space,
        action_space=venv.action_space,
        demonstrations=dataloader,  # 从 dataloader 中读取数据
        rng=rng,
    )

    # 使用预训练的 PPO 模型作为专家策略
    expert_policy = PPO("MlpPolicy", venv, verbose=1)
    expert_policy.learn(total_timesteps=10000)  # 训练专家策略

    # 创建并执行 DAgger 训练
    with tempfile.TemporaryDirectory(prefix="dagger_example_") as tmpdir:
        dagger_trainer = SimpleDAggerTrainer(
            venv=venv,
            scratch_dir=tmpdir,
            bc_trainer=bc_trainer,
            expert_policy=expert_policy,
            rng=rng,
        )
        dagger_trainer.train(total_steps)

    print("DAgger training finished.")

if __name__ == "__main__":
    # 使用示例
    data_file = "/home/<USER>/rl/RL-IL/bc_data/1222_1612_stepnum_200000.json"  # 你自己的数据文件
    dagger_training_example(data_file, total_steps=8000)
    
    # 训练的模型能否继续用
    # 1. 加进来效果更差了 ?    更差
    # 2. 为什么要加进来  有什么用
    # 3. 收集,航向每次都改吗?  右边过不去,  0,1,2