import os
import json
import tempfile
import numpy as np
import torch
from torch.utils.data import DataLoader, Dataset
from metadrive.policy.idm_policy import IDMPolicy
from metadrive.envs.metadrive_env import MetaDriveEnv
from stable_baselines3.common.policies import BasePolicy
import  time
from stable_baselines3 import PPO
from stable_baselines3.common.vec_env import DummyVecEnv
import pathlib

import torch as th  # 添加这行导入语句


from imitation.algorithms import bc
from imitation.algorithms.dagger import SimpleDAggerTrainer
from gymnasium.envs.registration import register

device = torch.device('cpu')
def save_dagger_checkpoint(trainer: SimpleDAggerTrainer, save_path: str):
    """保存 DAgger 训练器的检查点"""
    save_path = pathlib.Path(save_path)
    save_path.mkdir(parents=True, exist_ok=True)
    
    # 保存策略
    policy_path = save_path / "policy.pt"
    th.save(trainer.policy.state_dict(), policy_path)
    
    # 保存训练器的其他必要信息
    metadata = {
        'round_num': trainer.round_num,
        # 添加其他需要保存的元数据
    }
    metadata_path = save_path / "metadata.pt"
    th.save(metadata, metadata_path)
    
    return policy_path, metadata_path

def register_metadrive():
    """注册 MetaDrive 环境"""
    try:
        register(
            id="metadrive-env-v0",
            entry_point="metadrive.envs.metadrive_env:MetaDriveEnv",
            max_episode_steps=1000,
        )
    except Exception as e:
        print(f"注册环境时出错: {e}")
class ExpertWrapper:
    def __init__(self, expert_policy, venv):
        self.expert_policy = expert_policy
        self.observation_space = venv.observation_space
        self.action_space = venv.action_space
        
    def predict(self, obs, state=None, episode_start=None, deterministic=None):
        # IDMPolicy 的 act 方法返回一个动作
        actions = self.expert_policy.act(obs)
        return actions, state
###############################################################################
# 1. 数据集定义（与 collect_data_from_dif_policy.py 采集的数据相同结构）
###############################################################################
class BCDataset(Dataset):
    def __init__(self, data_path):
        with open(data_path, 'r') as f:
            self.data = json.load(f)
        self.observations = self.data['obs']
        self.actions = self.data['actions']
    
    def __len__(self):
        return len(self.observations)
    
    def __getitem__(self, idx):
        observation = self.observations[idx]
        action = self.actions[idx]
        observation = torch.tensor(observation, dtype=torch.float32)
        action = torch.tensor(action, dtype=torch.long)  # 离散动作
        return observation, action

###############################################################################
# 2. 环境创建，参考 collect_data_from_dif_policy.py
###############################################################################

def make_metadrive_env(rng=None):
    """创建 MetaDrive 环境实例"""
    from metadrive.engine.engine_utils import close_engine
    import gymnasium as gym  # 使用 gymnasium 替代 gym
    
    # 关闭现有引擎
    close_engine()
    
    # 确保环境已注册
    register_metadrive()
    
    env_configs = {
        "use_render": False,
        "manual_control": False,
        "traffic_density": 0,
        "num_scenarios": 100,
        "start_seed": 1,
        'random_spawn_lane_index': True,
        'map': 'O',
    }
    
    try:
        env = gym.make("metadrive-env-v0", config=env_configs)
    except Exception as e:
        print(f"创建环境时出错: {e}")
        raise
    
    # 包装环境以适应新的 API
    def custom_step_wrapper(env):
        old_step = env.step
        def step(action):
            # 新版本 Gymnasium 返回 5 个值：obs, reward, terminated, truncated, info
            obs, reward, terminated, truncated, info = old_step(action)
            # 合并 terminated 和 truncated 为一个 done 信号
            done = terminated or truncated
            return obs, reward, done, False, info
        env.step = step
        return env
    
    env = custom_step_wrapper(env)
    
    # 创建向量化环境
    venv = DummyVecEnv([lambda: env])
    return venv

###############################################################################
# 3. DAgger 训练示例：使用 IDMPolicy 作为专家，BC 作为学生策略
class IDMExpertPolicy(BasePolicy):
    def __init__(self, policy, observation_space, action_space):
        super().__init__(observation_space=observation_space, 
                        action_space=action_space)
        self.policy = policy
        
    def forward(self, *args, **kwargs):
        pass
        
    def _predict(self, observation, state=None, episode_start=None, deterministic=None):
        """符合BasePolicy接口的predict方法"""
        try:
            # 获取原始环境对象
            env = self.policy.control_object
            while hasattr(env, 'env'):
                env = env.env
            
            # 确保环境正确初始化
            if not hasattr(env, 'navigation'):
                # 返回与动作空间匹配的默认动作
                default_action = torch.zeros(1, *self.action_space.shape, dtype=torch.float32)
                return default_action
                
            # 更新 IDMPolicy 的控制对象
            self.policy.control_object = env
            action = self.policy.act(observation)
            
            # 确保动作形状正确并转换为 torch.Tensor
            if isinstance(action, (int, float)):
                action = torch.tensor([[action, 0]], dtype=torch.float32)
            else:
                action = torch.tensor([action], dtype=torch.float32)
                
            return action
            
        except Exception as e:
            print(f"Error in _predict: {e}")
            # 返回与动作空间匹配的默认动作
            return torch.zeros(1, *self.action_space.shape, dtype=torch.float32)
def dagger_idm_example(data_path, total_steps,save_dir="saved_models"):
    os.makedirs(save_dir, exist_ok=True)
    # 载入示例数据集
    dataset = BCDataset(data_path)
    dataloader = DataLoader(dataset, batch_size=64, shuffle=True)

    # 创建训练环境
    venv = make_metadrive_env()
    rng = np.random.default_rng(0)

    # print(f'venv.action_space:{venv.action_space}')
    # print(f'venv.observation_space:{venv.observation_space}')
    """
    venv.action_space:Discrete(25)
    venv.observation_space:Box(-0.0, 1.0, (259,), float32)
    """
    
    # 使用 BC 对象加载数据集
    bc_trainer = bc.BC(
        observation_space=venv.observation_space,
        action_space=venv.action_space,
        demonstrations=dataloader,
        rng=rng,
    )
    # bc_trainer.policy.to(device)

    # 使用 IDMPolicy 作为专家策略
    venv.reset()
    idm_expert = IDMPolicy(venv.envs[0], random_seed=0)
    wrapped_expert = IDMExpertPolicy(idm_expert, venv.observation_space, venv.action_space)
    wrapped_expert.to(device)

    with tempfile.TemporaryDirectory(prefix="dagger_example_") as tmpdir:
        dagger_trainer = SimpleDAggerTrainer(
            venv=venv,
            scratch_dir=tmpdir,
            bc_trainer=bc_trainer,
            expert_policy=wrapped_expert,  # 使用包装后的专家策略
            rng=rng,
        )
        # dagger_trainer.policy = dagger_trainer.policy.to(device)
        dagger_trainer.train(total_steps)  
        cur_time = time.strftime("%m%d_%H-%M", time.localtime())
    save_path = os.path.join(save_dir, f"dagger_model_{cur_time}")
    save_dagger_checkpoint(dagger_trainer, save_path)

if __name__ == "__main__":
    data_file = "/home/<USER>/rl/RL-IL/bc_data/5/output-2024-12-27_16-04-26_worker-0_0.json"
    save_dir = "/home/<USER>/rl/RL-IL/dagger_saved_models"
    dagger_idm_example(data_file, total_steps=10,save_dir=save_dir)