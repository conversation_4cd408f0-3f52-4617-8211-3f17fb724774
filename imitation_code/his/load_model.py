import ray
from ray.rllib.algorithms.ppo import PPOConfig
from ray.tune.registry import register_env
import os
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.envs.gym_wrapper import createGymWrapper
import random
class MetaDriveEnvWrapper:
    def __init__(self, checkpoint_path,use_render=False,test_episode=0):
        self.checkpoint_path = checkpoint_path
        self.env = None
        self.algo = None
        self.total_reward = 0
        self.done = False
        self.obs = None
        self.test_episode = test_episode
        self.arr_num = 0
        self.fail_arr_num = 0
        self.use_render = use_render
    def env_creator(self, env_config):
        env_config = dict(
            # traffic_mode="hybrid",
            map="O",
            num_scenarios=1000,
            # start_seed=random.randint(10, 2**15),
            # use_AI_protector=False,
            accident_prob=0.0,
            use_render=self.use_render,
            # agent_observation=None,
            random_spawn_lane_index=True,
            manual_control=False,
            traffic_density=0.0,
            # use_lateral_reward=True
        )
        # env_config.update({
        #     'agent_configs':{
        #         'default_agent':{
        #             'max_speed_km_h': 22.222,
        #             "spawn_lane_index": ('>', '>>', 0)
        #             }
        #         }
        #     })
        # print(f'env_config: {env_config}')
        env = createGymWrapper(MetaDriveEnv)(config=env_config)
        # print(f'env_config:{env_config}')
        return env
    def setup(self):
        register_env('metadrive-env', self.env_creator)
        print('sadfsadfasdf')
        # ray.shutdown()
        # ray.init()
        config = (
            PPOConfig()
            # .environment('metadrive-env', disable_env_checking=True)
            .framework('torch')
        )
        print('dfgdsfgsfdg')

        if os.path.exists(self.checkpoint_path):
            print('hfgndfh')
            self.algo = config.build() # bug
            print('000')

            self.algo.restore(self.checkpoint_path)
            print('543643')
            ppo_config = PPOConfig().framework("torch").to_dict()
            print('134623')
            ppo_model_config = ppo_config.get("model", {})

        else:
            raise FileNotFoundError(f"Checkpoint path {self.checkpoint_path} not found.")

    def run(self):
        self.env = self.env_creator({})
        self.obs = self.env.reset()
        
        cur_eposide = 0
        step_num = 0
        all_eposide_num = 0
        while True:
            step_num += 1
            if cur_eposide >= self.test_episode:
                break
            if self.done:
                self.obs = self.env.reset()
                self.done = False
                # self.total_reward = 0

            action = self.algo.compute_single_action(self.obs)
            self.obs, reward, self.done, info = self.env.step(action)
            # print(info['velocity']*3)
            if info.get('out_of_road'):
                all_eposide_num += 1
                self.obs = self.env.reset()
                self.done = False
                cur_eposide += 1
                self.fail_arr_num += 1

                # step_num = 0
                print(f'success: {self.arr_num}, fail: {self.fail_arr_num}, success rate: {round(self.arr_num/(self.fail_arr_num + self.arr_num),4)}')
                continue
            if info.get('arrive_dest'):
                all_eposide_num += 1
                self.arr_num += 1
                cur_eposide += 1
                # step_num = 0
                print(f'success: {self.arr_num}, fail: {self.fail_arr_num}, success rate: {round(self.arr_num/(self.fail_arr_num + self.arr_num),4)}')
            
            self.total_reward += reward
            # print(f'total_reward: {self.total_reward},mean_eposide_reward: {self.total_reward/(all_eposide_num+1)}')
            
        self.env.close()

if __name__ == "__main__":
    checkpoint_path = '/home/<USER>/rl/RL-IL/model_checkpoints/checkpoint_000100'
    wrapper = MetaDriveEnvWrapper(checkpoint_path,use_render=False,test_episode=100)
    wrapper.setup()
    wrapper.run()
    
    #/home/<USER>/rl/RL-IL/bc_models/checkpoint_003130