# import numpy as np
# import gymnasium as gym
# from stable_baselines3 import PPO
# from stable_baselines3.common.evaluation import evaluate_policy
# from stable_baselines3.common.vec_env import DummyVecEnv
# from stable_baselines3.ppo import MlpPolicy

# from imitation.algorithms.adversarial.airl import AIRL
# from imitation.data import rollout
# from imitation.data.wrappers import RolloutInfoWrapper
# from imitation.rewards.reward_nets import BasicShapedRewardNet
# from imitation.util.networks import RunningNorm
# from imitation.util.util import make_vec_env


# from stable_baselines3.common.callbacks import BaseCallback

# # 自定义回调函数，渲染环境
# class RenderCallback(BaseCallback):
#     def __init__(self, eval_env, render_interval=40):
#         super(RenderCallback, self).__init__()
#         self.eval_env = eval_env
#         self.render_interval = render_interval

#     def _on_step(self):
#         if self.n_calls % self.render_interval == 0:  # 每 100 步渲染一次
#             obs, _ = self.eval_env.reset()  # 获取当前状态
#             self.eval_env.render()  # 渲染环境
#         return True

# rng = np.random.default_rng(0)

# env = gym.make("CartPole-v1", render_mode="rgb_array")
# # 创建回调实例
# render_callback = RenderCallback(env, render_interval=100)
# expert = PPO(policy=MlpPolicy, env=env)
# expert.learn(total_timesteps=20000,callback=render_callback)

# rollouts = rollout.rollout(
#     expert,
#     make_vec_env(
#         "CartPole-v1",
#         rng=rng,
#         n_envs=5,
#         post_wrappers=[lambda env, _: RolloutInfoWrapper(env)],
#         max_episode_steps=1000
#     ),
#     rollout.make_sample_until(min_timesteps=None, min_episodes=60),
#     rng=rng,
# )

# venv = make_vec_env("CartPole-v0", rng=rng,max_episode_steps=1000)
# learner = PPO(env=venv, policy=MlpPolicy)
# reward_net = BasicShapedRewardNet(
#     venv.observation_space,
#     venv.action_space,
#     normalize_input_layer=RunningNorm,
# )

# airl_trainer = AIRL(
#     demonstrations=rollouts,
#     demo_batch_size=1024,
#     gen_replay_buffer_capacity=2048,
#     n_disc_updates_per_round=4,
#     venv=venv,
#     gen_algo=learner,
#     reward_net=reward_net,
#     allow_variable_horizon=True,
#     init_tensorboard =True
# )
# airl_trainer.train(20000)

# # rewards, _ = evaluate_policy(learner, venv, 100, return_episode_rewards=True,render=True)
# # print("Rewards:", rewards)


# # 验证并渲染模型
# def test_and_render(model, env, num_episodes=100):
#     episode_rewards = []  # 用于存储每个episode的总奖励
#     for episode in range(num_episodes):
#         obs = env.reset()  # 重置环境
#         done = False
#         total_reward = 0  # 每个回合的累计奖励
#         for _ in range(1000):
#             action, _states = model.predict(obs)  # 使用模型来预测动作
#             try:
#                 # 尝试按 5 个返回值处理
#                 obs, reward, terminated, truncated, info = env.step(action)
#                 done = terminated or truncated
#             except ValueError:
#                 # 如果只返回了 4 个值，兼容旧环境
#                 obs, reward, done, info = env.step(action)
#             env.render("human")  # 渲染环境
#             total_reward += reward  # 累加奖励


#         episode_rewards.append(total_reward)  # 保存回合奖励

#         print(f"Episode {episode+1}/{num_episodes} - Total Reward: {total_reward}")

#     return episode_rewards

# # 调用 test_and_render 函数验证并渲染
# rewards = test_and_render(airl_trainer.gen_algo, venv, num_episodes=100)

# # 计算平均奖励并绘制奖励曲线
# average_reward = np.mean(rewards)
# print(f"Average Reward over {len(rewards)} episodes: {average_reward}")

# import numpy as np
# import matplotlib.pyplot as plt
# # 绘制奖励曲线
# plt.plot(rewards)
# plt.title("Episode Rewards")
# plt.xlabel("Episode")
# plt.ylabel("Total Reward")
# plt.show()

import numpy as np
import gymnasium as gym
from stable_baselines3 import PPO
from stable_baselines3.common.evaluation import evaluate_policy
from stable_baselines3.ppo import MlpPolicy

from imitation.algorithms.adversarial.airl import AIRL
from imitation.data import rollout
from imitation.data.wrappers import RolloutInfoWrapper
from imitation.policies.serialize import load_policy
from imitation.rewards.reward_nets import BasicShapedRewardNet
from imitation.util.networks import RunningNorm
from imitation.util.util import make_vec_env
from gymnasium.envs import register
import gymnasium as gym


def register_metadrive():
    register(
        id="metadrive-env-v0",  # 环境ID
        entry_point="metadrive.envs.metadrive_env:MetaDriveEnv",  # 环境类
        max_episode_steps=1000,
    )
register_metadrive()

SEED = 0

env = make_vec_env(
    "metadrive-env-v0",
    rng=np.random.default_rng(0),
    n_envs=8,
    post_wrappers=[lambda env, _: RolloutInfoWrapper(env)],  # to compute rollouts
)
expert = load_policy(
    "ppo-huggingface",
    organization="HumanCompatibleAI",
    env_name="metadrive-env-v0",
    venv=env,
)
rollouts = rollout.rollout(
    expert,
    env,
    rollout.make_sample_until(min_episodes=60),
    rng=np.random.default_rng(SEED),
)

learner = PPO(
    env=env,
    policy=MlpPolicy,
    batch_size=64,
    ent_coef=0.0,
    learning_rate=0.0005,
    gamma=0.95,
    clip_range=0.1,
    vf_coef=0.1,
    n_epochs=5,
    seed=SEED,
)
reward_net = BasicShapedRewardNet(
    observation_space=env.observation_space,
    action_space=env.action_space,
    normalize_input_layer=RunningNorm,
)
airl_trainer = AIRL(
    demonstrations=rollouts,
    demo_batch_size=2048,
    gen_replay_buffer_capacity=512,
    n_disc_updates_per_round=16,
    venv=env,
    gen_algo=learner,
    reward_net=reward_net,
)

env.seed(SEED)
learner_rewards_before_training, _ = evaluate_policy(
    learner, env, 100, return_episode_rewards=True,
)
airl_trainer.train(20000)  # Train for 2_000_000 steps to match expert.
env.seed(SEED)
learner_rewards_after_training, _ = evaluate_policy(
    learner, env, 100, return_episode_rewards=True,
)

print("mean reward after training:", np.mean(learner_rewards_after_training))
print("mean reward before training:", np.mean(learner_rewards_before_training))