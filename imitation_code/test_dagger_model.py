import torch
from metadrive.envs.metadrive_env import MetaDriveEnv
from stable_baselines3.common.policies import ActorCriticPolicy
import pathlib

def test_trained_policy(model_path: str, num_episodes=1000):
    """测试训练好的策略"""
    # 1. 创建测试环境
    env = MetaDriveEnv({
        "use_render": True,
        "manual_control": False,
        "traffic_density": 0,
        "map": "O",
        # "num_scenarios": 1000
    })
    # 创建一个常数学习率调度器
    def constant_fn(val):
        return lambda *args, **kwargs: val
    
    # 2. 加载训练好的策略
    policy = ActorCriticPolicy(
        observation_space=env.observation_space,
        action_space=env.action_space,
        net_arch=[dict(pi=[32, 32], vf=[32, 32])],
        activation_fn=torch.nn.ReLU,
        lr_schedule=constant_fn(0.0)  # 测试时学习率为0
    )
    # print(f'self.observation_space:{env.observation_space}')
    # print(f'self.action_space:{env.action_space}')
    
    # 3. 加载模型权重
    policy.load_state_dict(torch.load(f"{model_path}/policy.pt"))
    policy.eval()
    
    # 4. 开始测试
    successes = 0
    for episode in range(num_episodes):
        obs, _ = env.reset()
        done = False
        while not done:
            # 使用策略预测动作
            with torch.no_grad():
                action = policy.predict(obs)[0]
            # 执行动作
            obs, _, done, _, info = env.step(action)
            if done and info.get("arrive_dest", False):
                successes += 1
    
    success_rate = successes / num_episodes
    print(f"测试结果: 共测试 {num_episodes} 次，成功率 {success_rate:.2%}")
    env.close()
    return success_rate

if __name__ == "__main__":
    model_path = "/home/<USER>/rl/RL-IL/dagger_saved_models/dagger_model_1228_19-27"
    test_trained_policy(model_path)