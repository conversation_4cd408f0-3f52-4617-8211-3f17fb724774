import ray
from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.algorithms.bc import BCConfig

from ray.tune.registry import register_env
import os
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.envs.gym_wrapper import createGymWrapper


class ModelTester:
    def __init__(self, checkpoint_path):
        self.checkpoint_path = checkpoint_path
        self.env_name = 'metadrive-env'
        self.algo = None
        self.env = None

        self.register_environment()
        ray.shutdown()
        ray.init()
        self.configure_algorithm()

    def register_environment(self):
        def env_creator(env_config):
            config = {
                "traffic_mode": "hybrid",
                "map": "O",
                "use_AI_protector": False,
                "accident_prob": 0.0,
                "use_render": True,
                # "agent_observation": None,
                "manual_control": False,
                # "traffic_vehicle_config": {
                #     "show_navi_mark": False,
                #     "show_dest_mark": False,
                #     "enable_reverse": True,
                #     "show_lidar": False,
                #     "show_lane_line_detector": False,
                #     "show_side_detector": False,
                # },
                "traffic_density": 0.0,
                # "use_lateral_reward": True
            }
            return createGymWrapper(MetaDriveEnv)(config=config)

        register_env(self.env_name, env_creator)

    def configure_algorithm(self):
        config = (
            BCConfig()
            .environment(self.env_name, disable_env_checking=True)
            .framework('torch')
        )
        self.algo = config.build()
        self.load_checkpoint()

    def load_checkpoint(self):
        if os.path.exists(self.checkpoint_path):
            self.algo.restore(self.checkpoint_path)
            print(f"Model loaded from checkpoint: {self.checkpoint_path}")
        else:
            raise FileNotFoundError(f"Checkpoint path {self.checkpoint_path} not found.")

    def test_model(self):
        self.env = self.create_env()
        obs = self.env.reset()
        done = False

        while not done:
            action = self.algo.compute_single_action(obs)
            obs, reward, done, _ = self.env.step(action)
            print("reward:", reward)

        self.env.close()

    @staticmethod
    def create_env():
        config = {
            "traffic_mode": "hybrid",
            "map": "O",
            "use_AI_protector": False,
            "accident_prob": 0.0,
            "use_render": True,
            "agent_observation": None,
            "manual_control": False,
            "traffic_vehicle_config": {
                "show_navi_mark": False,
                "show_dest_mark": False,
                "enable_reverse": True,
                "show_lidar": False,
                "show_lane_line_detector": False,
                "show_side_detector": False,
            },
            "traffic_density": 0.0,
            "use_lateral_reward": True
        }
        return createGymWrapper(MetaDriveEnv)(config=config)


if __name__ == "__main__":
    checkpoint = '/home/<USER>/rl/RL-IL/bc_models/1BC_iteration_99_model.pth/checkpoint_000100'
    # '/home/<USER>/rl/RL-IL/bc_models/BC_iteration_99_model.pth/checkpoint_000100'
    tester = ModelTester(checkpoint)
    tester.test_model()
