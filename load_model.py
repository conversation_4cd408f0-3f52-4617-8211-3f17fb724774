import ray
from ray.rllib.algorithms.ppo import PPOConfig
from ray.tune.registry import register_env
import os
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.envs.gym_wrapper import createGymWrapper
import time

@ray.remote
class TestWorker:
    def __init__(self, checkpoint_path, worker_id, num_episodes, start_scenario_index, num_scenarios_per_worker):
        self.checkpoint_path = checkpoint_path
        self.worker_id = worker_id
        self.num_episodes = num_episodes
        self.use_render = False
        
        # 初始化场景索引控制
        self.start_scenario_index = start_scenario_index
        self.num_scenarios = num_scenarios_per_worker
        self.current_scenario_index = 0
        
        # 初始化指标
        self.arr_num = 0
        self.fail_arr_num = 0
        self.total_reward = 0
        self.episode_rewards = []
        self.tested_scenarios = []
        
        # 初始化环境相关属性
        self.env = None
        self.algo = None
        
        # 配置环境
        self.env_config = dict(
            start_seed=start_scenario_index,  # 设置起始种子
            num_scenarios=num_scenarios_per_worker,  # 设置场景数量
            use_render=self.use_render,
            random_spawn_lane_index=False,  # 关闭随机生成
            manual_control=False,
            traffic_density=0.0,
            random_agent_model=False,  # 关闭随机生成
            random_lane_width=False,  # 关闭随机生成
            random_lane_num=False,  # 关闭随机生成
            horizon=5555,  # 每个episode的最大步数
            agent_configs={
                'default_agent':{
                    'max_speed_km_h': 22.222,
                    "spawn_lane_index": ('>', '>>', 0)
                }
            }
        )

    def env_creator(self, env_config):
        env = createGymWrapper(MetaDriveEnv)(config=self.env_config)
        return env

    def safe_close(self):
        """安全关闭环境"""
        try:
            if hasattr(self, 'env') and self.env is not None:
                self.env.close()
                self.env = None
        except Exception as e:
            print(f"[Worker {self.worker_id}] Error closing environment: {str(e)}")

    def setup(self):
        """安全设置环境"""
        try:
            from metadrive.engine.engine_utils import close_engine
            close_engine()
            
            register_env('metadrive-env', self.env_creator)
            
            temp_env = self.env_creator({})
            config = (
                PPOConfig()
                .environment('metadrive-env', disable_env_checking=True)
                .framework('torch')
            )
            temp_env.close()
            
            self.algo = config.build()
            self.algo.restore(self.checkpoint_path)
            
            self.env = self.env_creator({})
            # 初始化第一个场景
            self.obs = self.env.reset()
            print(f"[Worker {self.worker_id}] Environment initialized with scenarios [{self.start_scenario_index}:{self.start_scenario_index + self.num_scenarios})")
            return True
        except Exception as e:
            print(f"[Worker {self.worker_id}] Setup failed: {str(e)}")
            self.safe_close()
            return False

    def safe_env_reset(self):
        """带超时保护的环境重置"""
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                start_time = time.time()
                obs = self.env.reset()
                print(f"[Worker {self.worker_id}] Reset successful after {time.time() - start_time:.2f}s (attempt {attempt + 1})")
                return obs
            except Exception as e:
                print(f"[Worker {self.worker_id}] Reset attempt {attempt + 1} failed: {str(e)}")
                if attempt == max_attempts - 1:
                    return None
                self.safe_close()
                if not self.setup():
                    return None
                time.sleep(1)  # 短暂等待后重试

    def run(self):
        try:
            if not self.setup():
                raise Exception("Environment setup failed")
            
            cur_episode = 0
            episode_reward = 0
            steps_in_episode = 0
            last_progress_time = time.time()
            
            while cur_episode < self.num_episodes:
                current_scenario = self.start_scenario_index + self.current_scenario_index
                current_time = time.time()
                
                # 检查是否停滞
                if current_time - last_progress_time > 30:  # 5分钟无进展则超时
                    print(f"[Worker {self.worker_id}] Timeout detected - no progress for 5 minutes")
                    break
                
                # 检查步数是否过多
                if steps_in_episode >= self.env_config['horizon']:
                    # print(f"[Worker {self.worker_id}] Episode {cur_episode + 1} exceeded max steps")
                    done = True
                else:
                    try:
                        action = self.algo.compute_single_action(
                            self.obs,
                            explore=False
                        )
                        obs, reward, done, info = self.env.step(action)
                        episode_reward += reward
                        steps_in_episode += 1
                    except Exception as e:
                        print(f"[Worker {self.worker_id}] Error during step: {str(e)}")
                        done = True
                
                if steps_in_episode % 100 == 0:  # 定期输出进度
                    # print(f"[Worker {self.worker_id}] Episode {cur_episode + 1} Step {steps_in_episode}")
                    last_progress_time = current_time
                
                if done:
                    if info.get('arrive_dest'):
                        self.arr_num += 1
                    else:
                        self.fail_arr_num += 1
                    
                    self.episode_rewards.append(episode_reward)
                    self.total_reward += episode_reward
                    self.tested_scenarios.append(current_scenario)
                    
                    print(f"[Worker {self.worker_id}] Episode {cur_episode + 1}/{self.num_episodes}")
                    print(f"[Worker {self.worker_id}] Testing scenario index: {current_scenario}")
                    print(f"[Worker {self.worker_id}] Episode reward: {episode_reward}")
                    print(f"[Worker {self.worker_id}] Steps taken: {steps_in_episode}")
                    
                    # 准备下一个场景
                    self.current_scenario_index += 1
                    if self.current_scenario_index >= self.num_scenarios:
                        break
                    
                    # 重置环境到下一个场景
                    self.obs = self.safe_env_reset()
                    if self.obs is None:
                        print(f"[Worker {self.worker_id}] Failed to reset environment, stopping")
                        break
                        
                    episode_reward = 0
                    steps_in_episode = 0
                    cur_episode += 1
                    last_progress_time = time.time()
                else:
                    self.obs = obs
            
            self.safe_close()
            print(f"[Worker {self.worker_id}] Completed testing {len(self.tested_scenarios)} scenarios")
            
            return {
                'success': self.arr_num,
                'fail': self.fail_arr_num,
                'total_reward': self.total_reward,
                'episode_rewards': self.episode_rewards,
                'episodes': len(self.tested_scenarios),
                'worker_id': self.worker_id,
                'tested_scenarios': self.tested_scenarios,
                'scenario_range': f"{self.start_scenario_index}-{self.start_scenario_index + self.num_scenarios - 1}"
            }
            
        except Exception as e:
            print(f"[Worker {self.worker_id}] Error: {str(e)}")
            self.safe_close()
            raise

def run_parallel_test(checkpoint_path, num_workers=4, total_scenarios=100, timeout=3600):
    """
    使用多个worker并行运行测试，每个worker负责不同的场景子集
    
    Args:
        checkpoint_path: 模型检查点路径
        num_workers: worker数量
        total_scenarios: 总场景数
        timeout: 整体执行超时时间（秒）
    """
    ray.shutdown()
    ray.init()
    
    start_time = time.time()
    
    # 计算每个worker需要处理的场景数
    num_scenarios_per_worker = total_scenarios // num_workers
    
    # 创建workers，为每个worker分配不同的场景范围
    workers = [
        TestWorker.remote(
            checkpoint_path=checkpoint_path,
            worker_id=i,
            num_episodes=num_scenarios_per_worker,
            start_scenario_index=i * num_scenarios_per_worker,
            num_scenarios_per_worker=num_scenarios_per_worker
        )
        for i in range(num_workers)
    ]
    
    # 并行执行测试，带超时控制
    try:
        results = ray.get([worker.run.remote() for worker in workers], timeout=timeout)
    except Exception as e:
        print(f"Error during parallel execution: {str(e)}")
        ray.shutdown()
        raise
    
    # 汇总结果
    total_episodes = sum(r['episodes'] for r in results)
    total_success = sum(r['success'] for r in results)
    total_fail = sum(r['fail'] for r in results)
    total_reward = sum(r['total_reward'] for r in results)
    all_episode_rewards = []
    all_tested_scenarios = []
    for r in results:
        all_episode_rewards.extend(r['episode_rewards'])
        all_tested_scenarios.extend(r['tested_scenarios'])
    
    # 检查场景覆盖情况
    tested_scenarios_set = set(all_tested_scenarios)
    missing_scenarios = set(range(total_scenarios)) - tested_scenarios_set
    
    # 打印最终结果
    print("\n=== Final Results ===")
    print(f"Total Episodes: {total_episodes}")
    print(f"Success Rate: {total_success/(total_success + total_fail):.3f}")
    print(f"Average Episode Reward: {total_reward/total_episodes:.3f}")
    print(f"Total Reward: {total_reward:.3f}")
    print(f"\nScenario Coverage: {len(tested_scenarios_set)}/{total_scenarios}")
    if missing_scenarios:
        print(f"Missing scenarios: {sorted(list(missing_scenarios))}")
    print("\nWorker Scenario Ranges:")
    for r in results:
        print(f"Worker {r['worker_id']}: {r['scenario_range']} (Tested {len(r['tested_scenarios'])} scenarios)")
    print(f"\nTotal execution time: {time.time() - start_time:.2f} seconds")
    print("========================")
    
    ray.shutdown()

if __name__ == "__main__":
    checkpoint_path = '/home/<USER>/data/bc_models/BC_iteration_9999_model.pth2025-03-07-23-22-18/checkpoint_010000'
    
    # 运行并行测试
    start_time = time.time()
    total_scenarios = 200  # 总场景数 
    num_workers = 5
    
    run_parallel_test(
        checkpoint_path=checkpoint_path,
        num_workers=num_workers,
        total_scenarios=total_scenarios,
        timeout=3600  # 1小时超时
    )
    
    end_time = time.time()
    print(f'Cost time: {end_time - start_time:.2f} seconds')
