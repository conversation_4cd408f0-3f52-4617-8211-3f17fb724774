import numpy as np
import gym
import torch
import torch.nn as nn
from torch.nn import functional as F
from ray.rllib.models.torch.torch_modelv2 import TorchModelV2
from ray.rllib.models.modelv2 import ModelV2
from ray.rllib.utils.annotations import override
from ray.rllib.utils.framework import try_import_torch
from ray.rllib.utils.typing import Dict, TensorType, List, ModelConfigDict
from ray.rllib.models.torch.misc import SlimFC

torch, nn = try_import_torch()

class MultiHeadAttention(nn.Module):
    def __init__(self, d_model, num_heads):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.head_dim = d_model // num_heads
        
        self.qkv = nn.Linear(d_model, 3 * d_model)
        self.proj = nn.Linear(d_model, d_model)
        
    def forward(self, x, mask=None):
        batch_size, seq_len, d_model = x.size()
        
        qkv = self.qkv(x)
        qkv = qkv.reshape(batch_size, seq_len, 3, self.num_heads, self.head_dim)
        qkv = qkv.permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]
        
        scores = torch.matmul(q, k.transpose(-2, -1)) / np.sqrt(self.head_dim)
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        attention = F.softmax(scores, dim=-1)
        
        x = torch.matmul(attention, v)
        x = x.transpose(1, 2).reshape(batch_size, seq_len, d_model)
        x = self.proj(x)
        return x

class TransformerBlock(nn.Module):
    def __init__(self, d_model, num_heads, ff_dim, dropout=0.1):
        super().__init__()
        self.attention = MultiHeadAttention(d_model, num_heads)
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        self.ff = nn.Sequential(
            nn.Linear(d_model, ff_dim),
            nn.ReLU(),
            nn.Linear(ff_dim, d_model)
        )
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x):
        att_output = self.attention(x)
        x = self.norm1(x + self.dropout(att_output))
        ff_output = self.ff(x)
        x = self.norm2(x + self.dropout(ff_output))
        return x

class CustomAttentionNet(TorchModelV2, nn.Module):
    def __init__(self, obs_space, action_space, num_outputs, model_config, name):
        TorchModelV2.__init__(self, obs_space, action_space, num_outputs, model_config, name)
        nn.Module.__init__(self)
        
        custom_config = model_config.get("custom_model_config", {})
        d_model = custom_config.get("attention_dim", 64)
        num_heads = custom_config.get("attention_num_heads", 1)
        num_layers = custom_config.get("attention_num_transformer_units", 1)
        ff_dim = custom_config.get("attention_position_wise_mlp_dim", 64)
        
        self.embedding = nn.Linear(int(np.product(obs_space.shape)), d_model)
        self.transformer_blocks = nn.ModuleList([
            TransformerBlock(d_model, num_heads, ff_dim)
            for _ in range(num_layers)
        ])
        self.out = nn.Sequential(
            nn.Linear(d_model, 256),
            nn.ReLU(),
            nn.Linear(256, num_outputs)
        )
        
        # Value head
        self.value_head = nn.Sequential(
            nn.Linear(d_model, 256),
            nn.ReLU(),
            nn.Linear(256, 1)
        )
        
    @override(ModelV2)
    def forward(self, input_dict: Dict[str, TensorType], state: List[TensorType],
               seq_lens: TensorType) -> (TensorType, List[TensorType]):
        x = input_dict["obs_flat"].float()
        x = self.embedding(x).unsqueeze(1)  # Add sequence dimension
        
        for block in self.transformer_blocks:
            x = block(x)
            
        x = x.squeeze(1)  # Remove sequence dimension
        logits = self.out(x)
        self._value = self.value_head(x).squeeze(1)
        
        return logits, state

    @override(ModelV2)
    def value_function(self) -> TensorType:
        return self._value
