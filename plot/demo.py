from metadrive import MetaDriveEnv
import tqdm
import logging

# Default, traffic density=0.1
simple_env = MetaDriveEnv(dict(num_scenarios=10, 
                               start_seed=0, 
                               log_level=logging.WARNING))
# traffic density=0.5
complex_env = MetaDriveEnv(dict(num_scenarios=10, 
                                start_seed=0, 
                                traffic_density=0.5,
                                log_level=50)) # 50 == logging.WARNING

def calculate_traffic_vehicles(env, name):
    num_v = 0
    for seed in range(10):
        env.reset(seed=seed)
        num_v += len(env.engine.traffic_manager.spawned_objects)
    print("There are averagely {} vehicles in {}".format(num_v/10, name))
    env.close()

calculate_traffic_vehicles(simple_env, "environment with 0.1 density")
calculate_traffic_vehicles(complex_env, "environment with 0.5 density")


from metadrive import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy
from IPython.display import Image, clear_output

env = MetaDriveEnv(dict(traffic_mode="trigger", map="O"))
env.reset(seed=0)
try:
    for i in range(600):
        o,r,d,_,_ = env.step([0,-0.2] if i < 100 or i> 150 else [0, 0.2])
        env.render(mode="topdown", 
                   scaling=2, 
                   camera_position=(100, 0), 
                   screen_size=(500, 500),
                   screen_record=True,
                   window=False,
                   text={"episode_step": env.engine.episode_step,
                         "mode": "Trigger"})
    env.top_down_renderer.generate_gif()
finally:
    env.close()
    clear_output()