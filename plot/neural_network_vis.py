import matplotlib.pyplot as plt
import numpy as np

# 定义每层神经元数量
layer_sizes = [2, 5, 5, 2]  
layer_positions = np.linspace(0, 4, len(layer_sizes))  # 减小层间距

# 创建新图像
plt.figure(figsize=(12, 8), facecolor='none')
plt.gca().set_aspect('equal')
plt.axis('off')
plt.gca().set_facecolor('none')

# 添加边界空间
margin = 1.0
plt.xlim(min(layer_positions) - margin, max(layer_positions) + margin)
plt.ylim(-3, 3)

# 计算最大层大小，用于确定垂直缩放
max_layer_size = max(layer_sizes)
vertical_spacing = 1.0  # 固定垂直间距

# 先画出连接线
for l1 in range(len(layer_sizes)-1):
    layer1_size = layer_sizes[l1]
    layer2_size = layer_sizes[l1+1]
    # 计算每层的起始位置，使其居中对齐
    start1 = -(layer1_size - 1) * vertical_spacing / 2
    start2 = -(layer2_size - 1) * vertical_spacing / 2
    for i in range(layer1_size):
        for j in range(layer2_size):
            y1 = start1 + i * vertical_spacing
            y2 = start2 + j * vertical_spacing
            # 计算圆边缘的起点和终点
            radius = 0.4
            angle1 = np.arctan2(y2 - y1, layer_positions[l1+1] - layer_positions[l1])
            angle2 = np.arctan2(y1 - y2, layer_positions[l1] - layer_positions[l1+1])
            start_x = layer_positions[l1] + radius * np.cos(angle1)
            start_y = y1 + radius * np.sin(angle1)
            end_x = layer_positions[l1+1] + radius * np.cos(angle2)
            end_y = y2 + radius * np.sin(angle2)
            plt.plot([start_x, end_x], [start_y, end_y], 'gray', alpha=1, zorder=1)


# 再画出节点,使其显示在连接线上层
for l, layer_size in enumerate(layer_sizes):
    start = -(layer_size - 1) * vertical_spacing / 2  # 计算这层的起始位置
    for i in range(layer_size):
        y = start + i * vertical_spacing
        # 白色填充圆圈
        circle = plt.Circle((layer_positions[l], y), 0.4, color='white', fill=True, zorder=2)
        plt.gca().add_patch(circle)
        # 黑色边框
        plt.gca().add_patch(plt.Circle((layer_positions[l], y), 0.4, color='black', fill=False, zorder=3))


# 保存图片,设置透明背景
plt.savefig('/home/<USER>/data/pic/neural_network.png', dpi=300, transparent=True, bbox_inches='tight', pad_inches=0)
