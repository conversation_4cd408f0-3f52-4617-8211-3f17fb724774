import ray
from ray.rllib.algorithms.ppo import PPOConfig
from ray.tune.registry import register_env
import os
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.envs.gym_wrapper import createGymWrapper
from metadrive.policy.idm_policy import IDMPolicy
from metadrive.policy.expert_policy import ExpertPolicy
import time
import random
import os
import cv2
class MetaDriveEnvWrapper:
    def __init__(self, checkpoint_path,use_render,test_episode):
        self.checkpoint_path = checkpoint_path
        self.env = None
        self.algo = None
        self.total_reward = 0
        self.done = False
        self.obs = None
        self.test_episode = test_episode
        self.arr_num = 0
        self.fail_arr_num = 0
        self.use_render = use_render
        self.save_path = "/home/<USER>/data/plot"
        os.makedirs(self.save_path, exist_ok=True)
    def env_creator(self, env_config):
        self.env_config = dict(
            # traffic_mode="hybrid",
            map="O",
            num_scenarios=100,
            start_seed=0,
            # use_AI_protector=False,
            accident_prob=0.0,
            use_render=self.use_render,
            # agent_observation=None,
            random_spawn_lane_index=False,
            manual_control=False,
            traffic_density=0.1,
            # use_AI_protector=True,
            # reactive_traffic = True,
            agent_policy=ExpertPolicy,

        )
        # env_config.update({
        #     'agent_configs':{
        #         'default_agent':{
        #             'max_speed_km_h': 22,
        #             "spawn_lane_index": ('>', '>>', 0)
        #             }
        #         }
        #     })
        env = createGymWrapper(MetaDriveEnv)(config=self.env_config)
        
        # print(f'env_config:{env_config}')
        return env
    def setup(self):
        register_env('metadrive-env', self.env_creator)
        ray.shutdown()
        ray.init()
        config = (
            PPOConfig()
            .environment('metadrive-env', disable_env_checking=True)
            .framework('torch')
        )

        if os.path.exists(self.checkpoint_path):
            self.algo = config.build()
            self.algo.restore(self.checkpoint_path)
            ppo_config = PPOConfig().framework("torch").to_dict()
            ppo_model_config = ppo_config.get("model", {})

        else:
            raise FileNotFoundError(f"Checkpoint path {self.checkpoint_path} not found.")

    def run(self):
        self.env = self.env_creator({})
        self.obs = self.env.reset()
        cur_eposide = 0
        step_num = 0
        all_eposide_num = 0
        while True:
            step_num += 1
            if cur_eposide >= self.test_episode:
                break
            if self.done:
                self.obs = self.env.reset()
                self.done = False
                # self.total_reward = 0

            action = self.algo.compute_single_action(self.obs)
            self.obs, reward, self.done, info = self.env.step(action)
            img = self.env.render(mode="top_down",
                        window=True,  # 显示窗口
                        screen_size=(1500, 1500))
            driver = "ExpertPolicy" if self.env_config.get("agent_policy",False) else "BC_Model"
            if info.get('out_of_road'):
                all_eposide_num += 1
                self.obs = self.env.reset()
                self.done = False
                cur_eposide += 1
                self.fail_arr_num += 1
                # 保存失败的episode图片
                img_bgr = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                timestamp = time.strftime('%Y-%m-%d-%H-%M-%S', time.localtime())
                cv2.imwrite(os.path.join(self.save_path, f"{driver}_seed_{info['env_seed']}_success_{timestamp}.png"),
                            img_bgr,
                            [cv2.IMWRITE_PNG_COMPRESSION, 0])
                continue
            if info.get('arrive_dest'):
                all_eposide_num += 1
                self.arr_num += 1
                cur_eposide += 1
                # 保存成功的episode图片
                img_bgr = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                cur_time =time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
                cv2.imwrite(os.path.join(self.save_path, f"{driver}_seed_{info['env_seed']}_success_{cur_time}.png"), img_bgr, [cv2.IMWRITE_PNG_COMPRESSION, 0])

                # step_num = 0     
            self.total_reward += reward
            # print(f'info:{info}')
        self.env.close()

if __name__ == "__main__":
    checkpoint_path = '/home/<USER>/data/bc_models/BC_iteration_2999_model.pth2024-12-31-11-00-05/checkpoint_003000'
    # '/home/<USER>/data/bc_models/BC_iteration_2999_model.pth2025-03-09-20-21-05/checkpoint_003000'
    # '/home/<USER>/data/bc_models/BC_iteration_2999_model.pth2024-12-31-11-00-05/checkpoint_003000'
    wrapper = MetaDriveEnvWrapper(checkpoint_path,use_render=False,test_episode=300)
    wrapper.setup()
    wrapper.run()
