import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import re

# PPO实验结果目录
ppo_dir = "/home/<USER>/data/真实场景bc引导与纯RL对比"

# 存储所有实验数据
all_data = []

# 遍历PPO目录下的所有文件夹
for folder in os.listdir(ppo_dir):
    # 仅处理PPO_GymEnvWrapper开头的文件夹
    if folder.startswith("PPO_GymEnvWrapper"):
        # 尝试从文件夹名中提取start_step值
        match = re.search(r'bc_rl_mix_and_pure_rl_start_step=(\d+)', folder)
        match = re.search(r'bc_rl_mix_and_pure_rl_start_step=(\d+)', folder)
        if match:
            start_step = int(match.group(1))
            
            # 读取progress.csv文件
            progress_file = os.path.join(ppo_dir, folder, "progress.csv")
            if os.path.exists(progress_file):
                try:
                    df = pd.read_csv(progress_file)
                    # 提取所需的列
                    data = {
                        'timesteps_total': df['timesteps_total'],
                        'episode_reward_mean': df['episode_reward_mean'],
                        'start_step': start_step
                    }
                    all_data.append(data)
                except Exception as e:
                    print(f"Error reading {progress_file}: {e}")

# 对数据按start_step排序
sorted_data = sorted(all_data, key=lambda x: x['start_step'])

# 计算需要多少行子图（每行2个子图）
n = len(sorted_data)
n_rows = (n + 1) // 2  # 向上取整

# 创建子图
fig, axs = plt.subplots(n_rows, 2, figsize=(20, 5*n_rows))
if n_rows == 1:
    axs = [axs]  # 如果只有一行，确保axs是二维的
axs = np.array(axs)  # 转换为numpy数组以便于索引

# 使用tab20颜色方案
colors = plt.cm.tab20(np.linspace(0, 1, 20))

# 在每个子图中绘制2条线
for i, data in enumerate(sorted_data):
    row = i // 2
    col = i % 2
    
    # 绘制当前线条
    axs[row, col].plot(data['timesteps_total'], data['episode_reward_mean'],
                      label=f'start_step={data["start_step"]}',
                      color=colors[i % 20],
                      linewidth=1.5)
    
    # 设置子图属性
    axs[row, col].set_xlabel('Training Steps')
    axs[row, col].set_ylabel('Mean Episode Reward')
    axs[row, col].grid(True)
    axs[row, col].legend()
    axs[row, col].set_title(f'Start Step = {data["start_step"]}')

# 如果子图数量为奇数，移除最后一个空白子图
if n % 2 == 1:
    fig.delaxes(axs[-1, -1])

# 调整布局
plt.tight_layout()
import time
# 保存图表
# plt.savefget_data_from_csvig(f'training_curves{time.strftime("%Y-%m-%d %H:%M", time.localtime())}.png', dpi=300, bbox_inches='tight')
plt.show()
