import graphviz

def create_network_diagram():
    # 创建有向图
    dot = graphviz.Digraph(comment='Transformer Network Architecture')
    dot.attr(rankdir='TB')
    
    # 设置全局样式
    dot.attr('graph', 
            splines='line',
            nodesep='0.6',
            ranksep='0.8')
    

    # 学术风格的淡雅配色
    colors = {
        'input': '#E6F3FF',      # 淡蓝色
        'embedding': '#D6EAF8',   # 稍深淡蓝
        'attention': '#FDEDEC',   # 淡粉色
        'head': '#FDF2E9',       # 淡橙色
        'ffn': '#EAF2E9',        # 淡绿色
        'output': '#F4ECF7',     # 淡紫色
        'edge': '#95A5A6'        # 优雅的灰色
    }
    
    # 设置统一的节点样式
    dot.attr('node', 
            shape='box',
            style='filled,rounded',
            fontname='Times-Roman',
            fontsize='15',
            height='0.8',
            width='1.5',
            margin='0.2,0.1')
    
    # Input Processing
    dot.node('input', 'Observation\nSpace Input', fillcolor=colors['input'])
    dot.node('embedding', 'Embedding Layer\n(obs_dim, 64)', fillcolor=colors['embedding'])
    dot.edge('input', 'embedding')
    
    # Multi-Head Attention Block
    with dot.subgraph(name='cluster_attention') as c:
        c.attr(label='Multi-Head Self-Attention', style='rounded', fillcolor=colors['attention'])
        
        # QKV变换
        c.node('qkv', 'QKV Transform\n(64→64)', fillcolor=colors['attention'])
        
        # 展示部分注意力头
        c.node('head1', 'Head 1\nQ•K→V', fillcolor=colors['head'])
        c.node('head2', 'Head 2\nQ•K→V', fillcolor=colors['head'])
        c.node('dots', '...', fillcolor=colors['head'])
        c.node('head8', 'Head 8\nQ•K→V', fillcolor=colors['head'])
        
        # Concatenation & Layer Norm
        c.node('concat', 'Concatenate', fillcolor=colors['attention'])
        c.node('project', 'Project\n(512→64)', fillcolor=colors['attention'])
        c.node('norm1', 'Layer Norm', fillcolor=colors['attention'])
        
        # 连接
        c.edge('qkv', 'head1')
        c.edge('qkv', 'head2')
        c.edge('qkv', 'dots')
        c.edge('qkv', 'head8')
        c.edge('head1', 'concat')
        c.edge('head2', 'concat')
        c.edge('dots', 'concat')
        c.edge('head8', 'concat')
        c.edge('concat', 'project')
        c.edge('project', 'norm1')
    
    # Feed Forward Network
    with dot.subgraph(name='cluster_ffn') as c:
        c.attr(label='Feed-Forward Network', style='rounded', fillcolor=colors['ffn'])
        c.node('ff1', 'Linear\n(64→256)', fillcolor=colors['ffn'])
        c.node('relu', 'ReLU', fillcolor=colors['ffn'])
        c.node('ff2', 'Linear\n(256→64)', fillcolor=colors['ffn'])
        c.node('norm2', 'Layer Norm', fillcolor=colors['ffn'])
        
        c.edge('ff1', 'relu')
        c.edge('relu', 'ff2')
        c.edge('ff2', 'norm2')
    
    # Output Heads
    dot.node('policy', 'Policy Head\n(64→action_dim)', fillcolor=colors['output'])
    dot.node('value', 'Value Head\n(64→1)', fillcolor=colors['output'])
    
    # 连接各个部分
    dot.edge('embedding', 'qkv')
    dot.edge('norm1', 'ff1')
    dot.edge('norm2', 'policy')
    dot.edge('norm2', 'value')
    
    # 保存为PDF和PNG格式
    dot.render('RL-IL/doc/network_architecture_viz', format='pdf', cleanup=True)
    dot.render('RL-IL/doc/network_architecture_viz', format='png', cleanup=True)

if __name__ == '__main__':
    create_network_diagram()
