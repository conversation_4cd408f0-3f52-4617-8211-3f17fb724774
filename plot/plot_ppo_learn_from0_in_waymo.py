import pandas as pd
import matplotlib.pyplot as plt
import os
import numpy as np

# 读取CSV文件目录
csv_dir = "/home/<USER>/data/0329画raw_scenes_100_多组图画阴影/csv"

# 存储所有数据的列表
all_data = []

# 读取所有CSV文件
for file in os.listdir(csv_dir):
    if file.endswith('.csv'):
        # 修改这一行：如果CSV文件已有标题行，直接读取而不指定names
        df = pd.read_csv(os.path.join(csv_dir, file))
        # 确保Step和Value列为数值类型
        df['Step'] = pd.to_numeric(df['Step'])
        df['Value'] = pd.to_numeric(df['Value'])
        all_data.append(df)

# 找到最小长度
min_length = min(len(df) for df in all_data)

# 截取所有数据到最小长度
truncated_values = []
for df in all_data:
    truncated_values.append(df['Value'].values[:min_length])

# 获取对应的step值
steps = all_data[0]['Step'].values[:min_length]

# 转换为numpy数组
values = np.array(truncated_values)

# 计算平均值和标准差
mean_values = np.mean(values, axis=0)
std_values = np.std(values, axis=0)

# 创建图表
plt.figure(figsize=(10, 6))

# 使用学术风格的淡色配色
light_color = '#8BBEE8'  # 浅蓝灰色
dark_color = '#4B77BE'   # 深蓝灰色

# 绘制阴影区域和平均线
plt.fill_between(steps, mean_values - std_values, mean_values + std_values, alpha=0.2, color=light_color)
plt.plot(steps, mean_values, color=dark_color, linewidth=2, label='Mean')

# 设置科学计数法格式的x轴
plt.ticklabel_format(style='sci', axis='x', scilimits=(0,0))

# 设置x轴范围，确保最右边显示1e6
max_step = 1e6
plt.xlim(0, max_step)

plt.xlabel('Steps')
plt.ylabel('Value')
plt.title('Route Completion Rate')
plt.grid(True, alpha=0.3)  # 降低网格线的不透明度
plt.legend()

# 保存图表
plt.savefig('learning_curve.png')
plt.close()
