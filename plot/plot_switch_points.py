import seaborn as sns
sns.set()
import matplotlib.pyplot as plt
import pandas as pd
import os
import glob

def get_data_from_progress(folder_path, switch_point):
    """从progress.csv文件中读取数据

    Args:
        folder_path (str): 包含progress.csv的文件夹路径
        switch_point (str): 切换点值,用作标签

    Returns:
        pd.DataFrame: 处理后的DataFrame
    """
    df = pd.read_csv(os.path.join(folder_path, 'progress.csv'))
    # 选择需要的列
    df = df[['timesteps_total', 'episode_reward_mean']]
    # 重命名列以匹配绘图需求
    df = df.rename(columns={
        'timesteps_total': 'Step',
        'episode_reward_mean': 'Value'
    })
    # 添加switch_point标签（使用百分比格式）
    percentage = int(float(switch_point) / 1e6 * 100)
    df['algo'] = f'{percentage}%'
    return df

def plot_curves(base_dir='/home/<USER>/data/0_10_50_100_150/'):
    """处理数据并绘制图表"""
    all_dfs = []
    
    # 获取所有文件夹
    folders = glob.glob(os.path.join(base_dir, '*'))
    
    # 按切换点分组收集数据
    for folder in folders:
        if 'bc_rl_mix_and_pure_rl_start_step=' in folder:
            # 从文件夹名称中提取切换点值
            switch_point = folder.split('bc_rl_mix_and_pure_rl_start_step=')[1].split('_')[0]
            df = get_data_from_progress(folder, switch_point)
            all_dfs.append(df)
    
    # 合并所有数据
    combined_df = pd.concat(all_dfs, ignore_index=True)
    
    # 计算每组数据的均值和标准差
    mean_std_df = combined_df.groupby(['Step', 'algo'])['Value'].agg(['mean', 'std']).reset_index()
    
    # 创建图形
    plt.figure(figsize=(12, 8), dpi=300)

    # 对algo值按照百分比进行升序排序
    unique_algos = sorted(mean_std_df['algo'].unique(), key=lambda x: float(x.rstrip('%')))

    # 在同一张图上绘制多个算法曲线
    for algo in unique_algos:
        algo_df = mean_std_df[mean_std_df['algo'] == algo]
        sns.lineplot(x="Step", y="mean", data=algo_df, label=algo, linewidth=2)
        plt.fill_between(
            algo_df['Step'],
            algo_df['mean'] - algo_df['std'],
            algo_df['mean'] + algo_df['std'],
            alpha=0.1
        )

    # 设置图表标签和字体大小
    plt.xlabel("Step", fontsize=20)
    plt.ylabel("Episode Reward Mean", fontsize=20)
    plt.legend(fontsize=8)

    # 调整布局
    plt.subplots_adjust(left=0.1, right=0.95, top=0.98, bottom=0.1)

    # 保存并显示图像
    plt.savefig('switch_points_comparison.png')
    plt.show()

if __name__ == "__main__":
    plot_curves()
