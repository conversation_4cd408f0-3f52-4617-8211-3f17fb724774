from metadrive.envs.scenario_env import ScenarioEnv
from metadrive.engine.asset_loader import AssetLoader
import cv2
import os
import time

class ScenarioVisualizer:
    def __init__(self, num_scenarios=100):
        self.save_path = "/home/<USER>/data/plot"
        os.makedirs(self.save_path, exist_ok=True)

        asset_path = AssetLoader.asset_path
        self.env_config = {
            "manual_control": True,
            "sequential_seed": True,
            "reactive_traffic": False,
            "use_render": True,
            # "data_directory": AssetLoader.file_path(asset_path, "waymo", unix_style=False),
            "num_scenarios": num_scenarios,
            "data_directory": "/home/<USER>/data/raw_scenes_500",
        }
        
        from metadrive.engine.engine_utils import close_engine
        close_engine()
        self.env = ScenarioEnv(self.env_config)
        
    def run(self):
        obs, _ = self.env.reset()
        try:
            while True:
                # 使用固定动作[油门, 方向盘]
                action = [0.0, 0.0]
                obs, reward, term, trunc, info = self.env.step(action)
                done = term or trunc
                
                # 渲染场景
                img = self.env.render(
                    mode="top_down",
                    window=True,
                    screen_size=(800, 800),
                    film_size=(2000, 2000)
                )
                
                # 保存图片
                if done:
                    img_bgr = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                    cur_time = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
                    cv2.imwrite(os.path.join(self.save_path, f"waymo_scene_{cur_time}.png"), 
                              img_bgr, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                    obs, _ = self.env.reset()
                
        except KeyboardInterrupt:
            print("\nVisualization stopped by user")
        finally:
            self.env.close()

if __name__ == "__main__":
    visualizer = ScenarioVisualizer(num_scenarios=1)
    print("按ESC退出,方向键或WSAD控制车辆")
    visualizer.run()
