from metadrive.envs.scenario_env import ScenarioEnv
from metadrive.envs.real_data_envs.waymo_env import WaymoEnv

from metadrive.engine.asset_loader import AssetLoader
import cv2
import os
import time
import numpy as np
from ray.rllib.algorithms.ppo import PPO
from gym.spaces import Box

class ScenarioVisualizer:
    def __init__(self, num_scenarios=100, checkpoint_path="/home/<USER>/rl/models/ppo_final/checkpoint_500272_20250323_123805/checkpoint_000039"):
        self.save_path = "/home/<USER>/data/plot"
        os.makedirs(self.save_path, exist_ok=True)

        asset_path = AssetLoader.asset_path
        self.env_config = {
            # "manual_control": True,
            "sequential_seed": True,
            "reactive_traffic": False,
            "use_render": True,
            # "data_directory": AssetLoader.file_path(asset_path, "waymo", unix_style=False),
            "num_scenarios": num_scenarios,
            "data_directory": "/home/<USER>/data/raw_scenes_500",
            "vehicle_config": {
            "lidar": {"num_lasers": 30, "distance": 50, "num_others": 3},
            "side_detector": {"num_lasers": 30},
            "lane_line_detector": {"num_lasers": 12}
            }
        }
        
        from metadrive.engine.engine_utils import close_engine
        close_engine()
        self.env = WaymoEnv(self.env_config)
        
        # 设置observation和action空间
        self.observation_space = Box(low=-0.0, high=1.0, shape=(101,), dtype=np.float32)
        self.action_space = Box(low=-1.0, high=1.0, shape=(2,), dtype=np.float32)
        
        # 加载模型
        self.trainer = self.load_pretrained_policy(checkpoint_path)
        
    def load_pretrained_policy(self, checkpoint_path):
        config = {
            "env": None,
            "framework": "torch", 
            "num_workers": 0,
            "explore": False,
            "observation_space": self.observation_space,
            "action_space": self.action_space,
        }
        
        trainer = PPO(config=config)
        try:
            trainer.restore(checkpoint_path)
            print(f"模型加载成功：{checkpoint_path}")
        except Exception as e:
            print(f"加载模型失败：{e}")
            raise e
        return trainer
        
    def run(self):
        obs, _ = self.env.reset()
        try:
            while True:
                # 使用模型计算action
                print(f'len of obs:{len(obs)}')
                action = self.trainer.compute_single_action(obs)
                obs, reward, term, trunc, info = self.env.step(action)
                done = term or trunc
                
                # 渲染场景
                img = self.env.render(
                    mode="top_down",
                    window=True,
                    screen_size=(800, 800),
                    film_size=(2000, 2000)
                )
                
                # 保存图片
                if done:
                    img_bgr = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
                    cur_time = time.strftime("%Y-%m-%d-%H-%M-%S", time.localtime())
                    cv2.imwrite(os.path.join(self.save_path, f"waymo_scene_{cur_time}.png"), 
                              img_bgr, [cv2.IMWRITE_PNG_COMPRESSION, 0])
                    obs, _ = self.env.reset()
                
        except KeyboardInterrupt:
            print("\nVisualization stopped by user")
        finally:
            self.env.close()

if __name__ == "__main__":
    visualizer = ScenarioVisualizer(num_scenarios=1)
    print("按ESC退出,方向键或WSAD控制车辆")
    visualizer.run()
