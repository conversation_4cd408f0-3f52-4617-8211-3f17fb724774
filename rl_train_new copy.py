import logging
from ray import tune
import ray
from ray.rllib.agents.ppo import PPOTrainer
from ray.rllib.algorithms.callbacks import DefaultCallbacks
from metadrive.envs.gym_wrapper import createGymWrapper
import torch.backends.cudnn as cudnn
import torch
import numpy as np
from typing import Dict
from gym.spaces import Box
from metadrive.envs.metadrive_env import MetaDriveEnv
from ray.tune import Callback
from ray.rllib.env import BaseEnv
from ray.rllib.evaluation import MultiAgentEpisode, RolloutWorker
from ray.rllib.policy import Policy
import gc

if torch.cuda.is_available():
    cudnn.benchmark = True
    cudnn.deterministic = False
logger = logging.getLogger(__name__)

class CustomPPOTrainer(PPOTrainer):
    def __init__(self, config=None, env=None, logger_creator=None):
        super().__init__(config=config, env=env, logger_creator=logger_creator)

    def training_step(self):
        result = super().training_step()
        
        # 每10次迭代清理一次内存
        if self._iterations % 10 == 0:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                
        print("Custom training step logic...")
        return result

class MemoryManagementCallback(Callback):
    def on_trial_result(self, iteration, trials, trial, result, **info):
        """Called after receiving a result during training."""
        if iteration % 10 == 0:  # 每10次迭代
            gc.collect()
            torch.cuda.empty_cache()

    def on_trial_complete(self, iteration, trials, trial, **info):
        """Called when a trial completes."""
        gc.collect()
        torch.cuda.empty_cache()

class DrivingCallbacks(DefaultCallbacks):
    def on_episode_start(
        self, *, worker: RolloutWorker, base_env: BaseEnv, policies: Dict[str, Policy], episode: MultiAgentEpisode,
        env_index: int, **kwargs
    ):
        print("episode {} started".format(episode.episode_id))
        episode.user_data["velocity"] = []
        episode.user_data["steering"] = []
        episode.user_data["step_reward"] = []
        episode.user_data["acceleration"] = []
        episode.user_data["cost"] = []

    def on_episode_step(
        self, *, worker: RolloutWorker, base_env: BaseEnv, episode: MultiAgentEpisode, env_index: int, **kwargs
    ):
        info = episode.last_info_for()
        if info is not None:
            episode.user_data["velocity"].append(float(info.get("velocity", 0.0)))
            episode.user_data["steering"].append(float(info.get("steering", 0.0)))
            episode.user_data["step_reward"].append(float(info.get("step_reward", 0.0)))
            episode.user_data["acceleration"].append(float(info.get("acceleration", 0.0)))
            episode.user_data["cost"].append(float(info.get("cost", 0.0)))

    def on_episode_end(
        self, *, worker: RolloutWorker, base_env: BaseEnv, policies: Dict[str, Policy], episode: MultiAgentEpisode,
        **kwargs
    ):
        last_info = episode.last_info_for()
        if last_info is None:
            return

        # 清理episode数据
        if hasattr(episode, "user_data"):
            arrive_dest = last_info.get("arrive_dest",0)
            crash = last_info.get("crash", 0)
            out_of_road = last_info.get("out_of_road", 0)
            max_step_rate = not (arrive_dest or crash or out_of_road)
            
            # 计算指标
            episode.custom_metrics["success_rate"] = float(arrive_dest)
            episode.custom_metrics["crash_rate"] = float(crash)
            episode.custom_metrics["out_of_road_rate"] = float(out_of_road)
            episode.custom_metrics["max_step_rate"] = float(max_step_rate)
            episode.custom_metrics["velocity_max"] = float(np.max(episode.user_data["velocity"]))
            episode.custom_metrics["velocity_mean"] = float(np.mean(episode.user_data["velocity"]))
            episode.custom_metrics["velocity_min"] = float(np.min(episode.user_data["velocity"]))
            episode.custom_metrics["steering_max"] = float(np.max(episode.user_data["steering"]))
            episode.custom_metrics["steering_mean"] = float(np.mean(episode.user_data["steering"]))
            episode.custom_metrics["steering_min"] = float(np.min(episode.user_data["steering"]))
            episode.custom_metrics["acceleration_min"] = float(np.min(episode.user_data["acceleration"]))
            episode.custom_metrics["acceleration_mean"] = float(np.mean(episode.user_data["acceleration"]))
            episode.custom_metrics["acceleration_max"] = float(np.max(episode.user_data["acceleration"]))
            episode.custom_metrics["step_reward_max"] = float(np.max(episode.user_data["step_reward"]))
            episode.custom_metrics["step_reward_mean"] = float(np.mean(episode.user_data["step_reward"]))
            episode.custom_metrics["step_reward_min"] = float(np.min(episode.user_data["step_reward"]))
            episode.custom_metrics["cost"] = float(sum(episode.user_data["cost"]))
            episode.custom_metrics["speed_stability"] = float(np.var(np.array(episode.user_data["velocity"])))
            
            # 清理数据
            episode.user_data.clear()
            
        # 定期清理内存
        if episode.episode_id % 5 == 0:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                
    def on_train_result(self, *, algorithm, result: dict, **kwargs):
        result["success"] = np.nan
        result["crash"] = np.nan
        result["out"] = np.nan
        result["max_step"] = np.nan
        result["length"] = result["episode_len_mean"]
        result["cost"] = np.nan
        
        if "custom_metrics" in result:
            if "success_rate_mean" in result["custom_metrics"]:
                result["success"] = result["custom_metrics"]["success_rate_mean"]
                result["crash"] = result["custom_metrics"]["crash_rate_mean"]
                result["out"] = result["custom_metrics"]["out_of_road_rate_mean"]
                result["max_step"] = result["custom_metrics"]["max_step_rate_mean"]
            if "cost_mean" in result["custom_metrics"]:
                result["cost"] = result["custom_metrics"]["cost_mean"]
                
        # 清理数据
        if result["training_iteration"] % 10 == 0:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                
            # 清理策略对象
            if hasattr(algorithm, "workers") and hasattr(algorithm.workers, "local_worker"):
                local_worker = algorithm.workers.local_worker()
                if hasattr(local_worker, "_policy_dict"):
                    for policy_id in list(local_worker._policy_dict.keys()):
                        del local_worker._policy_dict[policy_id]
                        gc.collect()

def main():
    worker_num = 5
    cpu_core_num = 22
    use_render = False
    
    # 预先清理内存
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    if use_render:
        worker_num = 1
    ray.shutdown()
    ray.init(num_gpus=1)

    obs_space = Box(low=0.0, high=1.0, shape=(259,), dtype=np.float32)
    action_space = Box(low=-1.0, high=1.0, shape=(2,), dtype=np.float32)
    
    config = {
        "grad_clip": 0.4,
        "lr": tune.grid_search([5e-6]),
        "log_level": "INFO",
        "env": createGymWrapper(MetaDriveEnv),
        "env_config": dict(
            # map="O",
            num_scenarios=100,
            use_render=use_render,
            random_spawn_lane_index=True,
            manual_control=False,
            traffic_density=0,
            force_destroy=True,  # 强制清理环境
            store_map=False,     # 不缓存地图
            # agent_configs={
            #     'default_agent':{
            #         'spawn_lane_index': ('>', '>>', 0),
            #         'max_speed_km_h': 22,
            #         # 'spawn_lateral':random.uniform(-2, 2),
            #         }
            #     },
        ),
        "vehicle_config": {
            "lidar": {"num_lasers": 30, "distance": 50},
            "side_detector": {"num_lasers": 30},
            "lane_line_detector": {"num_lasers": 12}
        },
        "num_workers": worker_num,
        "num_cpus_per_worker": 1,#int((cpu_core_num-1)/worker_num),
        "num_gpus":0.3,
        "framework": "torch",
        "entropy_coeff_schedule":True,
        "callbacks": DrivingCallbacks,  # 使用驾驶回调而不是内存管理回调
        "training":{
            "train_batch_size":2048,
        },
        "metrics_smoothing_episodes":200,
    }
    
    stop_criteria = {
        "timesteps_total": 1000e3
    } 
    
    restorePath = None
    
    # 创建内存管理回调实例
    memory_callback = MemoryManagementCallback()
    
    tune.run(
        PPOTrainer,
        config=config,
        stop=stop_criteria,
        local_dir="./ray_results",
        name="PPO",
        callbacks=[memory_callback],  # 添加内存管理回调
        progress_reporter=tune.CLIReporter(
            parameter_columns=["bc_rl_mix_and_pure_rl_start_step"],
            metric_columns=["episode_reward_mean", "training_iteration"]
        ),
        num_samples=3 ,
        checkpoint_freq=100,
        checkpoint_at_end=True,
        log_to_file=True,
        restore=restorePath if restorePath is not None else None,
        max_concurrent_trials=3,
    )

if __name__ == "__main__":
    main()
