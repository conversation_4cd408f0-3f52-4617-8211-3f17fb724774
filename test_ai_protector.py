#!/usr/bin/env python3
"""
测试MetaDrive中use_AI_protector参数的具体作用
"""

import numpy as np
from metadrive import MetaDriveEnv

def test_ai_protector_effect():
    """测试AI保护器的效果"""
    
    print("🧪 测试MetaDrive AI保护器的作用")
    print("="*50)
    
    # 测试配置
    base_config = {
        "use_render": False,
        "manual_control": False,
        "traffic_density": 0.2,
        "num_scenarios": 1,
        "start_seed": 4,
        "map": "CXO",
        "accident_prob": 0.0,
        "vehicle_config": {
            "lidar": {"num_lasers": 30, "distance": 50},
            "side_detector": {"num_lasers": 30},
            "lane_line_detector": {"num_lasers": 12}
        }
    }
    
    # 测试1: 不使用AI保护器
    print("\n📊 测试1: use_AI_protector = False")
    config_no_protector = base_config.copy()
    config_no_protector["use_AI_protector"] = False
    
    env1 = MetaDriveEnv(config_no_protector)
    test_dangerous_actions(env1, "无AI保护器")
    env1.close()
    
    # 测试2: 使用AI保护器
    print("\n📊 测试2: use_AI_protector = True")
    config_with_protector = base_config.copy()
    config_with_protector["use_AI_protector"] = True
    
    env2 = MetaDriveEnv(config_with_protector)
    test_dangerous_actions(env2, "有AI保护器")
    env2.close()

def test_dangerous_actions(env, test_name):
    """测试危险动作的执行结果"""
    print(f"\n🚗 {test_name}测试:")
    
    obs, info = env.reset(seed=4)
    
    # 测试危险动作序列
    dangerous_actions = [
        [1.0, 1.0],    # 极端右转 + 全油门
        [-1.0, 1.0],   # 极端左转 + 全油门
        [0.0, -1.0],   # 急刹车
        [1.0, -1.0],   # 右转 + 急刹车
        [-1.0, -1.0],  # 左转 + 急刹车
    ]
    
    results = []
    
    for i, action in enumerate(dangerous_actions):
        obs, reward, terminated, truncated, info = env.step(action)
        
        # 获取车辆状态
        agent = env.agent
        speed = getattr(agent, 'speed_km_h', 0)
        steering = getattr(agent, 'steering', 0)
        throttle_brake = getattr(agent, 'throttle_brake', 0)
        crash = getattr(agent, 'crash_vehicle', False) or getattr(agent, 'crash_object', False)
        out_of_road = getattr(agent, 'out_of_road', False)
        
        result = {
            'step': i,
            'input_action': action,
            'actual_steering': steering,
            'actual_throttle': throttle_brake,
            'speed': speed,
            'crash': crash,
            'out_of_road': out_of_road,
            'reward': reward
        }
        results.append(result)
        
        print(f"  步骤 {i}: 输入动作={action} -> 实际转向={steering:.3f}, 实际油门={throttle_brake:.3f}")
        print(f"         速度={speed:.1f}km/h, 碰撞={crash}, 出路={out_of_road}, 奖励={reward:.3f}")
        
        if terminated or truncated:
            print(f"         Episode结束: terminated={terminated}, truncated={truncated}")
            break
    
    # 分析结果
    analyze_results(results, test_name)
    
    return results

def analyze_results(results, test_name):
    """分析测试结果"""
    print(f"\n📈 {test_name}结果分析:")
    
    if not results:
        print("   无有效数据")
        return
    
    # 检查动作是否被修改
    action_modifications = []
    for r in results:
        input_steering, input_throttle = r['input_action']
        actual_steering = r['actual_steering']
        actual_throttle = r['actual_throttle']
        
        steering_diff = abs(input_steering - actual_steering)
        throttle_diff = abs(input_throttle - actual_throttle)
        
        if steering_diff > 0.1 or throttle_diff > 0.1:
            action_modifications.append({
                'step': r['step'],
                'steering_diff': steering_diff,
                'throttle_diff': throttle_diff
            })
    
    print(f"   动作修改次数: {len(action_modifications)}/{len(results)}")
    
    if action_modifications:
        print("   动作被修改的步骤:")
        for mod in action_modifications:
            print(f"     步骤{mod['step']}: 转向差异={mod['steering_diff']:.3f}, 油门差异={mod['throttle_diff']:.3f}")
    
    # 安全性统计
    crashes = sum(1 for r in results if r['crash'])
    out_of_roads = sum(1 for r in results if r['out_of_road'])
    
    print(f"   安全性统计:")
    print(f"     碰撞次数: {crashes}")
    print(f"     出路次数: {out_of_roads}")
    print(f"     平均速度: {np.mean([r['speed'] for r in results]):.1f}km/h")
    print(f"     平均奖励: {np.mean([r['reward'] for r in results]):.3f}")

def compare_protector_configs():
    """对比不同AI保护器配置"""
    print("\n🔍 AI保护器功能分析:")
    print("="*50)
    
    # 基于观察到的行为模式分析
    analysis = {
        "use_AI_protector = False": {
            "动作执行": "直接执行输入动作",
            "安全干预": "无安全干预",
            "适用场景": "专家策略训练、完全控制",
            "风险": "可能执行危险动作"
        },
        "use_AI_protector = True": {
            "动作执行": "可能修改危险动作",
            "安全干预": "自动安全保护",
            "适用场景": "新手训练、安全测试",
            "风险": "可能影响策略学习"
        }
    }
    
    for config, features in analysis.items():
        print(f"\n📋 {config}:")
        for feature, description in features.items():
            print(f"   {feature}: {description}")

if __name__ == "__main__":
    try:
        test_ai_protector_effect()
        compare_protector_configs()
        
        print("\n💡 建议:")
        print("   - 训练阶段: 使用 use_AI_protector=False 获得真实的专家动作")
        print("   - 测试阶段: 可以使用 use_AI_protector=True 增加安全性")
        print("   - 消融实验: 保持与原版一致的配置")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
