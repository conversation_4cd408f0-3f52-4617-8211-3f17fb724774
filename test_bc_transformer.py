import ray
from ray.rllib.algorithms.bc import BCConfig
from ray.tune.registry import register_env
import os
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.envs.gym_wrapper import createGymWrapper
import time
import sys
from ray.rllib.models import ModelCatalog
from models.attention_net import CustomAttentionNet

@ray.remote
class TestWorker:
    def __init__(self, checkpoint_path, worker_id, num_episodes, total_scenarios, num_workers):
        self.checkpoint_path = checkpoint_path
        self.worker_id = worker_id
        self.num_episodes = num_episodes
        self.use_render = True
        
        # 计算每个worker的场景id区间
        scenarios_per_worker = total_scenarios // num_workers
        self.start_scenario = worker_id * scenarios_per_worker
        self.end_scenario = self.start_scenario + scenarios_per_worker - 1
        self.current_scenario = self.start_scenario
        
        # 初始化指标
        self.arr_num = 0
        self.fail_arr_num = 0
        self.total_reward = 0
        self.episode_rewards = []
        self.scenarios_tested = set()
        
        # 新增统计指标
        self.episode_lengths = []
        self.episode_energies = []
        self.route_completions = []
        self.velocities = []
        self.overtakes = []
        self.crash_nums = 0
        self.out_of_road_nums = 0
        
        # 配置环境
        self.env_config = dict(
            map='O',  # 使用O型环形地图
            num_scenarios=1000,  # 每次只加载1个场景
            random_spawn_lane_index=True,
            traffic_density=0.1,
            # horizon=1000,  # 限制最大步数
            use_render=self.use_render,
            vehicle_config= {
                "lidar": {"num_lasers": 30, "distance": 50, "num_others": 3},
                "side_detector": {"num_lasers": 30},
                "lane_line_detector": {"num_lasers": 12}
            },
            # vehicle_config=dict(
            #     spawn_velocity=20.0,  # 初始速度
            #     mass=1400,  # 车辆质量
            #     width=2.0,  # 车辆宽度
            #     length=5.0,  # 车辆长度
            #     spawn_velocity_car_frame=True  # 速度在车体坐标系下
            # ),
            # agent_configs={
            #     'default_agent':{
            #         'max_speed_km_h': 22.222,
            #         "spawn_lane_index": ('>', '>>', 0)
            #     }
            # },
            start_seed=self.current_scenario,  # 当前要测试的场景ID
        )

    def env_creator(self, env_config):
        env = createGymWrapper(MetaDriveEnv)(config=self.env_config)
        return env

    def setup(self):
        from metadrive.engine.engine_utils import close_engine
        close_engine()
        
        # 注册环境和模型
        register_env('metadrive-env', self.env_creator)
        ModelCatalog.register_custom_model("attention_net", CustomAttentionNet)
        
        temp_env = self.env_creator({})
        
        # 配置算法
        config = BCConfig()
        config.framework('torch')
        config.environment('metadrive-env', disable_env_checking=True)
        
        # 配置模型
        config.model.update({
            "custom_model": "attention_net",
            "custom_model_config": {
                "attention_num_transformer_units": 1,
                "attention_dim": 64,
                "attention_num_heads": 8,
                "attention_head_dim": 32,
                "attention_memory_inference": 50,
                "attention_memory_training": 50,
                "attention_position_wise_mlp_dim": 64
            }
        })
        
        temp_env.close()
        
        self.algo = config.build()
        self.algo.restore(self.checkpoint_path)

    def run(self):
        try:
            self.setup()
            cur_episode = 0
            episode_reward = 0
            last_print_time = time.time()
            
            while cur_episode < self.num_episodes:
                # 更新环境配置和当前场景ID
                self.env_config["start_seed"] = self.current_scenario
                self.env = self.env_creator({})
                self.obs = self.env.reset()
                done = False
                episode_reward = 0
                
                while not done:
                    action = self.algo.compute_single_action(
                        self.obs,
                        explore=False
                    )
                    
                    obs, reward, done, info = self.env.step(action)
                    episode_reward += reward
                    
                    # 每30秒至少打印一次状态
                    current_time = time.time()
                    if current_time - last_print_time >= 30:
                        print(f"Worker {self.worker_id} - Still running... Episode {cur_episode + 1}/{self.num_episodes} (Scene {self.current_scenario})")
                        sys.stdout.flush()
                        last_print_time = current_time
                    
                    self.obs = obs
                
                # 记录已测试的场景
                self.scenarios_tested.add(self.current_scenario)
                
                # 收集episode数据
                if info.get('arrive_dest'):
                    self.arr_num += 1
                else:
                    self.fail_arr_num += 1
                
                self.episode_rewards.append(episode_reward)
                self.total_reward += episode_reward
                
                # 记录新增指标
                self.episode_lengths.append(info.get('episode_length', 0))
                self.episode_energies.append(info.get('episode_energy', 0))
                self.route_completions.append(info.get('route_completion', 0))
                self.velocities.append(info.get('velocity', 0))
                self.overtakes.append(info.get('overtake_vehicle_num', 0))
                
                if info.get('crash'):
                    self.crash_nums += 1
                if info.get('out_of_road'):
                    self.out_of_road_nums += 1
                
                print(f"Worker {self.worker_id} - Episode {cur_episode + 1}/{self.num_episodes}")
                print(f"Scene {self.current_scenario} - Episode reward: {episode_reward:.2f}")
                sys.stdout.flush()
                
                self.env.close()
                
                # 更新场景ID
                cur_episode += 1
                if self.current_scenario >= self.end_scenario:
                    self.current_scenario = self.start_scenario
                else:
                    self.current_scenario += 1
            
            return {
                'success': self.arr_num,
                'fail': self.fail_arr_num,
                'total_reward': self.total_reward,
                'episode_rewards': self.episode_rewards,
                'episodes': self.num_episodes,
                'scenarios_tested': sorted(list(self.scenarios_tested)),
                'scenario_range': f"{self.start_scenario}-{self.end_scenario}",
                'episode_lengths': self.episode_lengths,
                'episode_energies': self.episode_energies,
                'route_completions': self.route_completions,
                'velocities': self.velocities,
                'overtakes': self.overtakes,
                'crash_nums': self.crash_nums,
                'out_of_road_nums': self.out_of_road_nums
            }
            
        except Exception as e:
            print(f"Worker {self.worker_id} failed with error: {str(e)}")
            sys.stdout.flush()
            raise e

def run_parallel_test(checkpoint_path, num_workers=4, episodes_per_worker=25, total_scenarios=100):
    """
    使用多个worker并行运行测试
    
    Args:
        checkpoint_path: 模型检查点路径
        num_workers: worker数量
        episodes_per_worker: 每个worker测试的回合数
        total_scenarios: 总场景数量
    """
    ray.shutdown()
    ray.init()
    
    # 创建workers
    workers = [
        TestWorker.remote(
            checkpoint_path=checkpoint_path,
            worker_id=i,
            num_episodes=episodes_per_worker,
            total_scenarios=total_scenarios,
            num_workers=num_workers
        )
        for i in range(num_workers)
    ]
    
    # 并行执行测试
    results = ray.get([worker.run.remote() for worker in workers])
    
    # 汇总结果
    total_episodes = sum(r['episodes'] for r in results)
    total_success = sum(r['success'] for r in results)
    total_fail = sum(r['fail'] for r in results)
    total_reward = sum(r['total_reward'] for r in results)
    all_episode_rewards = []
    for r in results:
        all_episode_rewards.extend(r['episode_rewards'])
    
    # 汇总新增指标
    all_episode_lengths = []
    all_episode_energies = []
    all_route_completions = []
    all_velocities = []
    all_overtakes = []
    total_crashes = 0
    total_out_of_roads = 0
    
    for r in results:
        all_episode_lengths.extend(r['episode_lengths'])
        all_episode_energies.extend(r['episode_energies'])
        all_route_completions.extend(r['route_completions'])
        all_velocities.extend(r['velocities'])
        all_overtakes.extend(r['overtakes'])
        total_crashes += r['crash_nums']
        total_out_of_roads += r['out_of_road_nums']
    
    print("\n=== Final Results ===")
    print(f"Total Episodes: {total_episodes}")
    print(f"Success Rate: {total_success/(total_success + total_fail):.3f}")
    print(f"Average Episode Reward: {total_reward/total_episodes:.3f}")
    print(f"Total Reward: {total_reward:.3f}")
    print("\n=== Detailed Statistics ===")
    print(f"Average Episode Length: {sum(all_episode_lengths)/len(all_episode_lengths):.2f}")
    print(f"Average Episode Energy: {sum(all_episode_energies)/len(all_episode_energies):.2f}")
    print(f"Average Route Completion: {sum(all_route_completions)/len(all_route_completions):.2f}")
    print(f"Average Velocity: {sum(all_velocities)/len(all_velocities):.2f}")
    print(f"Total Overtakes: {sum(all_overtakes)}")
    print(f"Total Crashes: {total_crashes}")
    print(f"Total Out of Roads: {total_out_of_roads}")
    print("\nScenarios tested by each worker:")
    for i, r in enumerate(results):
        print(f"Worker {i} (Range {r['scenario_range']}):")
        print(f"  Tested scenes: {r['scenarios_tested']}")
    print("========================")
    
    ray.shutdown()

if __name__ == "__main__":
    checkpoint_path = '/home/<USER>/data/bc_models/BC_iteration_999_model.pth2025-06-10-21-54-08/checkpoint_001000'
    
    # 运行并行测试
    start_time = time.time()
    test_episode_num = 100  # 减少测试次数以便快速验证
    num_workers = 1  # 减少worker数量以降低资源消耗
    total_scenarios = 20  # 减少场景数量
    
    run_parallel_test(
        checkpoint_path=checkpoint_path,
        num_workers=num_workers,
        episodes_per_worker=int(test_episode_num/num_workers),
        total_scenarios=total_scenarios
    )
    
    end_time = time.time()
    print(f'Cost time: {end_time - start_time:.2f} seconds')
