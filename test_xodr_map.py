import os
import pickle
from metadrive.envs.scenario_env import ScenarioEnv
from metadrive.manager.scenario_data_manager import ScenarioOnlineDataManager
from metadrive.scenario.scenario_description import ScenarioDescription

class XODREnv(ScenarioEnv):
    def setup_engine(self):
        from metadrive.engine.engine_utils import initialize_engine
        self.engine = initialize_engine(self.config)
        self.engine.register_manager("data_manager", ScenarioOnlineDataManager())

def test_xodr_map():
    # 加载转换后的场景数据
    with open("tjdx0529.pkl", "rb") as f:
        scenario_data = pickle.load(f)
    
    # 初始化环境
    env = XODREnv(
        {
            "use_render": True,  # 启用渲染
            "manual_control": True,  # 启用手动控制
            "num_scenarios": 1,  # 只加载一个场景
            "start_scenario_index": 0,
            "horizon": None,  # 无限步数
            "vehicle_config": {
                "show_navi_mark": True,  # 显示导航标记
                "lidar": {
                    "num_lasers": 120, 
                    "distance": 50
                },
                # 添加车道探测器
                "lane_line_detector": {
                    "num_lasers": 12,
                    "distance": 50
                },
                # 添加侧向探测器
                "side_detector": {
                    "num_lasers": 120, 
                    "distance": 50
                }
            }
        }
    )

    try:
        # 先重置环境以初始化引擎
        obs = env.reset()
        
        # 设置场景数据
        env.engine.data_manager.set_scenario(scenario_data)
        
        # 再次重置以加载新场景
        obs = env.reset()
        print("地图加载完成，现在可以使用方向键控制车辆。按R键重置环境，按ESC键退出。")
        
        while True:
            # 执行空动作
            obs, reward, terminated, truncated, info = env.step([0, 0])
            
            # 渲染场景
            env.render(
                text={
                    "场景信息": "OpenDRIVE地图测试",
                    "方向键": "控制车辆",
                    "R键": "重置环境",
                    "ESC": "退出"
                }
            )
            
            # 如果回合结束则重置
            if terminated or truncated:
                obs = env.reset()

    finally:
        # 关闭环境
        env.close()

if __name__ == "__main__":
    test_xodr_map()
