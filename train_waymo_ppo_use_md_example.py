import ray
from ray.rllib.algorithms.ppo import PPOConfig
from ray.tune.registry import register_env
from tqdm import tqdm
import torch
import gym
from metadrive.envs.scenario_env import ScenarioEnv
from torch.utils.tensorboard import SummaryWriter
from metadrive.engine.engine_utils import close_engine
import time
from ray.rllib.algorithms.callbacks import DefaultCallbacks
import gc
from ray import tune
import os
from custom_policy import CustomPolicy
from gym.spaces import Box
import numpy as np
ray.init(
    ignore_reinit_error=True,  # 添加这个参数以允许重新初始化
    num_cpus=os.cpu_count(),
    num_gpus=1,
    _temp_dir="/tmp/ray_checkpoint"
)
# Observation space dimension for ScenarioEnv
total_dim = 100  # Based on environment output
obs_space = Box(low=0.0, high=1.0, shape=(total_dim,), dtype=np.float32)
action_space=Box(low=-1.0, high=1.0, shape=(2,), dtype=np.float32)
class WaymoPPOTrainer:
    def __init__(self,config=None):
        # 创建带时间戳的模型保存目录
        cur_time = time.strftime(r"%Y_%m_%d_%H_%M", time.localtime())
        self.model_dir = f"/home/<USER>/data/models/waymo_ppo_{cur_time}"
        if not os.path.exists(self.model_dir):
            os.makedirs(self.model_dir)
        print(f"Model checkpoints will be saved to: {self.model_dir}")

        # 默认配置
        default_config = {
            # 'traffic_density': 0.0,
            # "policies": {
            #     "default_policy": (
            #         CustomPolicy, 
            #         obs_space,
            #         action_space, 
            #         {
            #         "bc_rl_mix_and_pure_rl_start_step":[150e3, 200e3]
            #         }
            #         ),
            # },
            "iteration_num": 300,
            # "horizon":91,
            "iteration_step_num":1000e3,  # 增加训练步数到1M步
            "num_scenarios": 10,          # 增加场景数量
            "start_scenario_index": 0,
            "worker_num": 5,              # 减少worker数量
            "lr": 1e-5,                   # 提高学习率
            "grad_clip": 0.5,             # 增加梯度裁剪范围
            "train_batch_size": 2048,     # 增加batch size
            "sgd_minibatch_size": 512,    # 保持minibatch size
            # "entropy_coeff": 0.05,        # 增加熵系数以鼓励探索
            # "gamma": 0.99,
        }

        self.data_directory = "/home/<USER>/data/raw_scenes_500"  # raw_scenes_500 expert_scenes
        self.use_render = False

        # 更新配置
        self.config = default_config
        if config:
            self.config.update(config)

        # 使用配置
        self.iteration_num = self.config["iteration_num"]
        self.worker_num = self.config["worker_num"]
        self.iteration_step_num = self.config["iteration_step_num"]

        self.total_episodes = 0
        self.total_crashes = 0
        self.cpu_core_num = 22
        self.train_batch_size = self.config["train_batch_size"] 
        self.sgd_minibatch_size = self.train_batch_size
        self.env_config = {
            "manual_control": False,
            "sequential_seed": True,
            "reactive_traffic": True,
            "use_render": self.use_render,
            "data_directory": self.data_directory,
            "num_scenarios": self.config["num_scenarios"],
            "start_scenario_index": self.config["start_scenario_index"],
            "vehicle_config": {
                "lidar": {
                    "num_lasers": 30,
                    "distance": 50,
                },
                "side_detector": {
                    "num_lasers": 30, 
                    "distance": 50
                },
                "lane_line_detector": {
                    "num_lasers": 12,
                    "distance": 50
                }
            }
        }

        if self.use_render:
            self.worker_num = 1 # 避免多个worker同时渲染直接死机

        register_env('waymo-env', self.env_creator)
        self._setup_algorithm()
        self.logger = TrainingLogger()

    def env_creator(self, env_config):
        """创建环境并包装观察值处理"""
        base_env = ScenarioEnv(self.env_config)
        
        # 为环境添加观察值预处理
        class ObsWrapper(gym.ObservationWrapper):
            def observation(self, obs):
                """处理观察值"""
                if isinstance(obs, dict) and 0 in obs and "agent0" in obs[0]:
                    # 解析多智能体格式
                    obs = obs[0]["agent0"]

                if isinstance(obs, tuple) and len(obs) == 2:
                    # 获取状态数组部分
                    state = obs[0]
                    if isinstance(state, np.ndarray):
                        if state.shape == (100,):
                            return state.astype(np.float32)
                        elif state.shape == (2,):
                            # 将形状为 (2,) 的数组填充到形状为 (100,) 的数组
                            padded_state = np.zeros(100, dtype=np.float32)
                            padded_state[:2] = state
                            return padded_state
                        else:
                            print(f"Warning: unexpected state shape: {getattr(state, 'shape', None)}")
                    elif isinstance(state, list):
                        state = np.array(state, dtype=np.float32)
                        if state.shape == (100,):
                            return state
                        elif state.shape == (2,):
                            padded_state = np.zeros(100, dtype=np.float32)
                            padded_state[:2] = state
                            return padded_state
                        else:
                            print(f"Warning: unexpected state shape: {getattr(state, 'shape', None)}")
                    else:
                        print(f"Warning: unexpected state type: {type(state)}")

                print(f"Warning: unexpected observation format: {type(obs)}")
                if isinstance(obs, np.ndarray):
                    print(f"Observation dtype: {obs.dtype}")
                try:
                    print(f"Observation type: {type(obs)}")
                    print(f"Observation shape: {obs.shape}")
                    print(f"Observation dtype: {obs.dtype}")
                    if isinstance(obs, np.ndarray) and obs.dtype == np.dtype('O'):
                        obs = np.zeros(100, dtype=np.float32)
                    else:
                        obs = obs.astype(np.float32)
                        if obs.shape != (100,):
                            padded_obs = np.zeros(100, dtype=np.float32)
                            min_length = min(obs.shape[0], 100)
                            padded_obs[:min_length] = obs[:min_length]
                            obs = padded_obs
                except Exception as e:
                    print(f"Error converting observation to numpy array: {e}")
                    obs = np.zeros(100, dtype=np.float32)
                return obs

        return ObsWrapper(base_env)

    def _preprocess_obs(self, obs):
        """预处理观察值,将嵌套字典转换为纯数组"""
        if isinstance(obs, dict):
            if 0 in obs and "agent0" in obs[0]:
                return obs[0]["agent0"][0]  # 取第一个元素作为状态数组
        return obs

    def _setup_algorithm(self):
        # Create config with explicit policy
        self.algo_config = (
            PPOConfig()
            .environment('waymo-env', disable_env_checking=True)
            .rollouts(
                num_rollout_workers=self.worker_num,
                preprocessor_pref=None,  # 禁用默认预处理器
            )
            .framework('torch')
            .training(
                train_batch_size=self.train_batch_size,
                sgd_minibatch_size=self.sgd_minibatch_size,
                lr=self.config["lr"],
            )
            .multi_agent(
                policies={
                    "default_policy": (
                        None,  # use default policy class
                        obs_space,  # use our observation space
                        action_space,
                        {}
                    )
                }
            )
            .resources(
                num_gpus=1,
                num_cpus_per_worker=2,
                num_gpus_per_worker=1/(self.worker_num+1)
            )
            .callbacks(WaymoCallbacks)
            .debugging(log_level='INFO')
        )
        print(f"Algorithm config: {self.algo_config}")  # 新增日志
        self.algo = self.algo_config.build()

    def save_checkpoint(self, iteration):
        if iteration == "best":
            path = os.path.join(self.model_dir, "best")
            if os.path.exists(path):
                # 如果目录已存在,先删除
                import shutil
                shutil.rmtree(path)
            os.makedirs(path)
        else:
            path = os.path.join(self.model_dir, str(iteration))
            if not os.path.exists(path):
                os.makedirs(path)
                
        checkpoint_path = self.algo.save(path)
        print(f"Saved checkpoint to: {checkpoint_path}")
        return checkpoint_path

    def train(self):
        total_steps = 0
        iteration = 0
        best_completion = 0.0  # 跟踪最佳route completion
        print(f"Workers initialized: {self.algo.workers}")  # 新增日志

        with tqdm(total=self.iteration_step_num) as pbar:
            while total_steps < self.iteration_step_num:
                # 清理缓存
                if iteration % 10 == 0:
                    gc.collect()
                    torch.cuda.empty_cache()


                try:
                    try:
                        result = self.algo.train()
                    except Exception as e:
                        print(f"Exception in algo.train(): {e}")  # 新增日志
                        raise
                except Exception as e:
                    print(f"Exception in algo.train(): {e}")  # 新增日志
                    raise
                total_steps = int(result["num_env_steps_sampled"])
                iteration += 1
                
                # 更新进度条
                pbar.update(total_steps)
                pbar.set_description(f"Steps: {total_steps}/{self.iteration_step_num}")

                # 训练指标记录
                train_metrics = {
                    "train/episode_reward_mean": result["episode_reward_mean"],
                    "train/episode_len_mean": result["episode_len_mean"],
                    "train/route_completion_mean": result.get("custom_metrics", {}).get("route_completion_mean", 0),
                    "train/crash_rate":  result.get("custom_metrics", {}).get("crash_vehicle_rate_mean", 0), 
                    "train/success_rate": result.get("custom_metrics", {}).get("arrive_dest", 0)
                }
                
                route_completion_mean = result.get("custom_metrics", {}).get("route_completion_mean", 0)
                print(f'route_completion_mean:{route_completion_mean}')
                print(f'crash_rate:{result.get("custom_metrics", {}).get("crash_vehicle_rate_mean", 0)}')
                
                # 记录指标
                self.logger.log_metrics(train_metrics, total_steps)
                
                # 保存最优模型
                if route_completion_mean > best_completion:
                    best_completion = route_completion_mean
                    print(f"\nSaving new best model with route completion: {best_completion}")
                    self.save_checkpoint("best")
                
                # 定期保存checkpoint
                if iteration % 500 == 0:
                    self.save_checkpoint(iteration)

                del result  # 立即释放

        final_path = self.save_checkpoint("final")
        self.logger.close()
        return final_path

    def train_tune(self):
        self.best_completion = 0
        total_steps = 0
        iteration = 0

        while total_steps < self.iteration_step_num:
            print(f'self.iteration_step_num:{self.iteration_step_num}')
            if iteration % 10 == 0:
                gc.collect()
                torch.cuda.empty_cache()
            
            result = self.algo.train()
            total_steps = int(result["num_env_steps_sampled"])
            iteration += 1

            # 记录指标
            metrics = {
                "train/episode_reward_mean": result["episode_reward_mean"],
                "train/episode_len_mean": result["episode_len_mean"],
                "train/route_completion_mean": result.get("custom_metrics", {}).get("route_completion_mean", 0),
                "train/crash_rate":  result.get("custom_metrics", {}).get("crash_vehicle_rate_mean", 0)
            }

            # 记录到TensorBoard
            print(f'total_steps-self.checkpoint_total_steps:{total_steps-self.checkpoint_total_steps}')
            self.logger.log_metrics(metrics, total_steps-self.checkpoint_total_steps)
            
            # 准备tune报告的指标
            tune_metrics = {}
            for key, value in metrics.items():
                new_key = key.replace("train/", "")
                tune_metrics[new_key] = value
            tune_metrics["timesteps_total"] = total_steps-self.checkpoint_total_steps
            tune.report(**tune_metrics)

            # 保存最优模型
            if metrics["train/route_completion_mean"] > self.best_completion:
                self.best_completion = metrics["train/route_completion_mean"]
                self.save_checkpoint("best")

        self.logger.close()
        self.save_checkpoint("final")
        return metrics

class TrainingLogger:
    def __init__(self, log_dir=f'/home/<USER>/data/tensorboard_logs/{time.strftime(r"%Y_%m_%d_%H_%M", time.localtime())}'):
        self.writer = SummaryWriter(log_dir=log_dir)

    def _setup_metrics(self):
        self.metrics = {
            "success_rate": [],
            "route_completion_rate": [],
            "crash_rate": [],
            "avg_episode_reward": [],
            "avg_episode_length": [],
            "out_of_road_rate": []
        }

    def log_metrics(self, metrics_dict, iteration):
        for key, value in metrics_dict.items():
            self.writer.add_scalar(key, value, iteration)

    def close(self):
        self.writer.close()

class WaymoCallbacks(DefaultCallbacks):
    def on_episode_start(self, worker, base_env, policies, episode, **kwargs):
        episode.user_data["crash_count"] = 0

    def on_episode_end(self, worker, base_env, policies, episode, **kwargs):
        info = episode.last_info_for()
        if info:
            # 获取真实的info字典
            if isinstance(info, dict) and 0 in info and "agent0" in info[0]:
                info = info[0]["agent0"][1]  # 取第二个元素作为info

            if isinstance(info, dict):
                episode.custom_metrics["route_completion"] = info.get("route_completion", 0)
                crash_count = episode.user_data["crash_count"]
                episode.custom_metrics.update({
                    "crash_count": crash_count,
                    "crash_vehicle_rate": float(info.get("crash_vehicle", False)),
                    "crash_object_rate": float(info.get("crash_object", False)),
                    "crash_building_rate": float(info.get("crash_building", False)),
                    "arrive_dest": float(info.get("arrive_dest", False)),
                })

def main():
    trainer = WaymoPPOTrainer()
    final_checkpoint = trainer.train()
    print(f"Training completed. Final checkpoint at: {final_checkpoint}")

def tune_main():
    cpu_count = os.cpu_count()
    cpus_per_trial = max(2, cpu_count // 10)
    resources_per_trial = tune.PlacementGroupFactory([
        {"CPU": 1, "GPU": 0.19},
    ] + [{"CPU": 1, "GPU": 0} for _ in range(3)])

    def trainable(config):
        trainer = WaymoPPOTrainer(config)
        result = trainer.train_tune()
        return {
            "route_completion_mean": result["train/route_completion_mean"],
        }

    search_config = {
        "policies": {
            "default_policy": (
                CustomPolicy, 
                obs_space,
                action_space, 
                {
                "bc_rl_mix_and_pure_rl_start_step":tune.grid_search([[50e3, 100e3]])
                }
            ),
        },
        "iteration_num": 300,
        "iteration_step_num":1000e3,
        "num_scenarios": 100,
        "start_scenario_index": 0,
        "worker_num": 3,
        "lr": tune.grid_search([1e-5]),
        "entropy_coeff": tune.grid_search([0.01,0]),
        "driving_reward": tune.grid_search([10]),
        "success_reward": tune.grid_search([100]),
        "crash_vehicle_penalty": tune.grid_search([10]),
        "out_of_road_penalty": tune.grid_search([50,30]),
        "train_batch_size": tune.grid_search([2048]),
        "kl_coeff":tune.grid_search([0.01]),
        "gamma":tune.grid_search([0.99]),
    }

    reporter = tune.CLIReporter(
        metric_columns=["route_completion_mean", "episode_reward_mean", "timesteps_total"]
    )

    analysis = tune.run(
        trainable,
        config=search_config,
        resources_per_trial=resources_per_trial,
        local_dir=f'/home/<USER>/data/tensorboard_logs/{time.strftime(r"%m_%d_%H_%M", time.localtime())}',
        name="parameter_search",
        progress_reporter=reporter,
        sync_config=tune.SyncConfig(syncer=None),
        verbose=2,
        num_samples=1,
        max_concurrent_trials=4,
    )
    
    best_trial = analysis.get_best_trial("route_completion_mean", mode="max")
    print("\n=== 最佳参数组合 ===")
    print(f"Best trial config: {best_trial.config}")

    try:
        route_completion = best_trial.last_result.get('train/route_completion_mean') or \
                         best_trial.last_result.get('route_completion_mean')
        print(f"Best trial final route completion: {route_completion}")
    except Exception as e:
        print("Available metrics in last_result:", best_trial.last_result.keys())
        print(f"Error accessing route completion: {e}")

    with open("best_params.txt", "w") as f:
        f.write("Best Parameters:\n")
        for param, value in best_trial.config.items():
            f.write(f"{param}: {value}\n")

if __name__ == "__main__":
    main()
