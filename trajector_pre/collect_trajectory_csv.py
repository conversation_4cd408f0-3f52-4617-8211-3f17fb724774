
from ray.rllib.evaluation.sample_batch_builder import SampleBatchBuilder
from ray.rllib.offline.json_writer import JsonWriter
import os
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy
from metadrive.envs.gym_wrapper import createGymWrapper
import random
from metadrive.policy.expert_policy import ExpertPolicy
from metadrive.policy.lange_change_policy import LaneChangePolicy
import time 
import csv
class DataCollector:
    """
    Collects data from different driving policies to generate offline datasets
    for behavior cloning or other offline learning methods.
    """

    def __init__(self, arr_episode,drive_policy="IDM Policy", use_render=False):
        self.config = MetaDriveEnv.default_config()
        self.drive_policy = drive_policy
        self.use_render = use_render
        self.env = self.create_env()
        self.batch_builder = SampleBatchBuilder()
        self.writer = JsonWriter(os.path.join(f'./bc_data/{arr_episode}'))  # ,compress_columns=['obs','actions']
        self.episode = 0
        self.data_buffer = []  # 用于缓存数据的列表
        self.arr_episode = arr_episode


    def create_env(self):
        print(self.config)
        self.config.update({
            "use_render": self.use_render,
            "manual_control": False,
            # "horizon":5000,
            "traffic_density": 0,
            "num_scenarios":1000, # 线路数量
            "start_seed": random.randint(0,2**15),
            # "random_lane_width":False,
            # "random_agent_model":False,
            # "map":'O',
            'random_spawn_lane_index': True,
            'agent_configs':{
                'default_agent':{
                    # 'spawn_lane_index': ('>', '>>', 2),
                    # 'max_speed_km_h': 15,
                    # 'spawn_lateral':2.1,
                    }
                },
            'vehicle_config': {
                # "vehicle_model": "xl",
                'show_dest_mark': True, 
                'show_line_to_navi_mark': True,
                # 'show_lidar': True, 
                # 'random_color': True,
                }
            })
        if self.drive_policy == "IDM Policy":
            print("IDM Policy selected")
            self.config["agent_policy"] = ExpertPolicy
        elif self.drive_policy == "Trained Algorithm":
            print("Trained Algorithm selected")
            self.config["agent_policy"] = IDMPolicy  # TODO: Implement trained algorithm
        elif self.drive_policy == "Manual Control":
            print("Manual Control selected")
            self.config["manual_control"] = True
        else:
            raise ValueError(f"Unknown drive policy: {self.drive_policy}")
        env = createGymWrapper(MetaDriveEnv)(config=self.config)
        print("Environment created successfully")
        return env

    def save_to_csv(self,data_list):
        cur_time = time.strftime("%m%d-%H-%M", time.localtime())
        directory = os.path.join(os.path.dirname(__file__), 'data')
        if not os.path.exists(directory):
            os.makedirs(directory)
        file_path = os.path.join(directory, f'trajectory_data_{cur_time}.csv')
        with open(file_path, mode='w', newline='') as file:
            writer = csv.DictWriter(file, fieldnames=data_list[0].keys())
            writer.writeheader()
            for row in data_list:
                writer.writerow(row)
    def collect_data(self):
        env = self.create_env()
        obs = env.reset()
        all_data_list = []  # 存储所有成功轨迹
        current_episode_data = []  # 存储当前回合数据
        arrive_num = 0
        all_step = 0
        arr_step = 0
        vehicle = env.vehicle
        
        while arrive_num < self.arr_episode:
            # 打印车辆状态字典
            """
            === Vehicle State ===
            position: [149.6509552001953, 96.27996826171875, 0.5339695811271667]
            heading_theta: 1.5726570260838557
            roll: -0.00027922892929141787
            pitch: -0.012726957646839053
            velocity: [-4.82216803e-03  1.13607683e+01]
            type: <class 'metadrive.component.vehicle.vehicle_type.DefaultVehicle'>
            steering: -0.004863458685576916
            throttle_brake: 0.06950587034225464
            crash_vehicle: False
            crash_object: False
            crash_building: False
            crash_sidewalk: False
            size: (4.515, 1.852, 1.19)
            length: 4.515
            width: 1.852
            height: 1.19
            spawn_road: ('>', '>>')
            destination: ('3r0_1_', '3r0_2_')
            """
            # print("\n=== Vehicle State ===")
            # for key, value in vehicle.get_state().items():
            #     print(f"{key}: {value}")
            vehicle_position = vehicle.position
            all_step += 1
            arr_step += 1
            action = env.action_space.sample()
            new_obs, reward, done, info = env.step(action)
            # print(f'info:{info.keys()}')
            
            # 收集数据并缓存到内存
            data = {
                "episode": arrive_num,
                "step": arr_step,
                "vehicle_position_x": vehicle_position[0],
                "vehicle_position_y": vehicle_position[1],
                # "vehicle_position_z": vehicle_position[2],
                "velocity_x": vehicle.velocity[0],
                "velocity_y": vehicle.velocity[1],
                "steering": info["steering"],
                "acceleration": info["acceleration"],
                "heading_theta":vehicle.heading_theta,
                "throttle_brake":vehicle.throttle_brake,
            }
            current_episode_data.append(data)
            
            if info.get('out_of_road'):
                current_episode_data = []  # 清空当前回合数据
                arr_step = 0
            
            if info.get('arrive_dest', False):
                all_data_list.extend(current_episode_data)  # 添加成功轨迹
                current_episode_data = []  # 重置当前回合数据
                arrive_num += 1
                arr_step = 0
            
            if done:
                obs = env.reset()
    
        if all_data_list:  # 确保有数据才保存
            self.save_to_csv(all_data_list)
        

if __name__ == "__main__":
    collector = DataCollector(drive_policy="IDM Policy", use_render=False,arr_episode=50)
    collector.collect_data()
