
from ray.rllib.evaluation.sample_batch_builder import <PERSON><PERSON>BatchBuilder
from ray.rllib.offline.json_writer import JsonWriter
import os
from metadrive.envs.metadrive_env import MetaDriveEnv
from metadrive.policy.idm_policy import IDMPolicy
from metadrive.envs.gym_wrapper import createGymWrapper
import random
import h5py
import numpy as np

config = {
    "random_agent_model": True,
    "agent_configs": {
        "default_agent": {
            "use_special_color": True,
            "spawn_lane_index": [
                ">",
                ">>",
                1
            ]
        }
    },
    "num_agents": 1,
    "is_multi_agent": False,
    "allow_respawn": False,
    "delay_done": 0,
    "manual_control": False,
    "controller": "keyboard",
    "discrete_action": False,
    "use_multi_discrete": False,
    "discrete_steering_dim": 5,
    "discrete_throttle_dim": 5,
    "action_check": False,
    "norm_pixel": True,
    "stack_size": 3,
    "image_observation": False,
    "agent_observation": None,
    "horizon": None,
    "truncate_as_terminate": False,
    "use_chase_camera_follow_lane": False,
    "camera_height": 2.2,
    "camera_dist": 7.5,
    "camera_pitch": None,
    "camera_smooth": True,
    "camera_smooth_buffer_size": 20,
    "camera_fov": 65,
    "prefer_track_agent": None,
    "top_down_camera_initial_x": 0,
    "top_down_camera_initial_y": 0,
    "top_down_camera_initial_z": 200,
    "vehicle_config": {
        "vehicle_model": "default",
        "enable_reverse": False,
        "show_navi_mark": True,
        "show_dest_mark": False,
        "show_line_to_dest": False,
        "show_line_to_navi_mark": False,
        "show_navigation_arrow": True,
        "use_special_color": False,
        "no_wheel_friction": False,
        "image_source": "rgb_camera",
        "spawn_lane_index": None,
        "destination": None,
        "spawn_longitude": 5.0,
        "spawn_lateral": 0.0,
        "spawn_position_heading": None,
        "spawn_velocity": None,
        "spawn_velocity_car_frame": False,
        "overtake_stat": False,
        "random_color": False,
        "width": None,
        "length": None,
        "height": None,
        "mass": None,
        "scale": None,
        "top_down_width": None,
        "top_down_length": None,
        "lidar": {
            "num_lasers": 240,
            "distance": 50,
            "num_others": 0,
            "gaussian_noise": 0.0,
            "dropout_prob": 0.0,
            "add_others_navi": False
        },
        "side_detector": {
            "num_lasers": 0,
            "distance": 50,
            "gaussian_noise": 0.0,
            "dropout_prob": 0.0
        },
        "lane_line_detector": {
            "num_lasers": 0,
            "distance": 20,
            "gaussian_noise": 0.0,
            "dropout_prob": 0.0
        },
        "show_lidar": False,
        "show_side_detector": False,
        "show_lane_line_detector": False,
        "light": False
    },
    "sensors": {},
    "use_render": True,
    "window_size": [
        1200,
        900
    ],
    "physics_world_step_size": 0.02,
    "decision_repeat": 5,
    "image_on_cuda": False,
    "_render_mode": "none",
    "force_render_fps": None,
    "force_destroy": False,
    "num_buffering_objects": 200,
    "render_pipeline": False,
    "daytime": "19:00",
    "shadow_range": 50,
    "multi_thread_render": True,
    "multi_thread_render_mode": "Cull",
    "preload_models": True,
    "disable_model_compression": True,
    "disable_collision": False,
    "map_region_size": 1024,
    "cull_lanes_outside_map": False,
    "drivable_area_extension": 7,
    "height_scale": 50,
    "use_mesh_terrain": False,
    "full_size_mesh": True,
    "show_crosswalk": True,
    "show_sidewalk": True,
    "pstats": False,
    "debug": False,
    "debug_panda3d": False,
    "debug_physics_world": False,
    "debug_static_world": False,
    "log_level": 20,
    "show_coordinates": False,
    "show_fps": True,
    "show_logo": True,
    "show_mouse": True,
    "show_skybox": True,
    "show_terrain": True,
    "show_interface": True,
    "show_policy_mark": False,
    "show_interface_navi_mark": True,
    "interface_panel": [
        "dashboard"
    ],
    "record_episode": False,
    "replay_episode": None,
    "only_reset_when_replay": False,
    "force_reuse_object_name": False,
    "num_scenarios": 1,
    "start_seed": 0,
    "map": "O",
    "random_lane_width": False,
    "random_lane_num": False,
    "map_config": {
        "type": "block_num",
        "config": None,
        "lane_width": 3.5,
        "lane_num": 3,
        "exit_length": 50,
        "start_position": [
            0,
            0
        ]
    },
    "store_map": True,
    "traffic_density": 0.0,
    "need_inverse_traffic": False,
    "traffic_mode": "trigger",
    "random_traffic": False,
    "traffic_vehicle_config": {
        "show_navi_mark": False,
        "show_dest_mark": False,
        "enable_reverse": False,
        "show_lidar": False,
        " show_lane_line_detector": False,
        "show_side_detector": False
    },
    "accident_prob": 0.0,
    "static_traffic_object": True,
    "use_AI_protector": False,
    "save_level": 0.5,
    "random_spawn_lane_index": False,
    "success_reward": 10.0,
    "out_of_road_penalty": 5.0,
    "crash_vehicle_penalty": 5.0,
    "crash_object_penalty": 5.0,
    "crash_sidewalk_penalty": 0.0,
    "driving_reward": 1.0,
    "speed_reward": 0.1,
    "use_lateral_reward": False,
    "crash_vehicle_cost": 1.0,
    "crash_object_cost": 1.0,
    "out_of_road_cost": 1.0,
    "out_of_route_done": False,
    "out_of_road_done": True,
    "on_continuous_line_done": True,
    "on_broken_line_done": False,
    "crash_vehicle_done": True,
    "crash_object_done": True,
    "crash_human_done": True
}

class DataCollector:
    def __init__(self, drive_policy="IDM Policy", use_render=False):
        step_num=int(1e3)
        self.drive_policy = drive_policy
        self.use_render = use_render
        self.step_num = step_num
        self.env = self._create_env()
        self.batch_builder = SampleBatchBuilder()
        self.writer = JsonWriter(os.path.join('./bc_data/'),compress_columns=[])
        self.episode = 0
        self.data_buffer = []  # 用于缓存数据的列表

    def _create_env(self):
        config = MetaDriveEnv.default_config()
        config.update({
            "use_render": self.use_render,
            'image_on_cuda': True,
            'show_logo': False,
            'random_agent_model':True,
            "manual_control": False,
            # "horizon":5000,
            "traffic_density": 0.0,
            "num_scenarios":1000, # 线路数量
            "start_seed": random.randint(0,2**15),
            "random_lane_width":False,
            # "map":'O',
            'random_spawn_lane_index': False,
            'agent_configs':{
                'default_agent':{
                    'spawn_lane_index': ('>', '>>', 1)
                }
            },
            'vehicle_config': {
                # 'maesss':33333,
                "vehicle_model": "xl",
                'show_dest_mark': True, 
                'show_line_to_navi_mark': True,
                # 'show_lidar': True, 
                # 'random_color': True,
                }
        })
        
        if self.drive_policy == "IDM Policy":
            print("IDM Policy selected")
            config["agent_policy"] = IDMPolicy
        elif self.drive_policy == "Trained Algorithm":
            print("Trained Algorithm selected")
            config["agent_policy"] = IDMPolicy  # TODO: Implement trained algorithm
        elif self.drive_policy == "Manual Control":
            print("Manual Control selected")
            config["manual_control"] = True
        else:
            raise ValueError(f"Unknown drive policy: {self.drive_policy}")
        env = createGymWrapper(MetaDriveEnv)(config=config)
        print(f'config:{config}')
        return env

    def collect_data(self):
        obs = self.env.reset()
        vehicle = self.env.vehicle
        
        # print("\n=== 车辆物理参数 ===")
        # print(f"车长: {vehicle.LENGTH}")
        # print(f"车宽: {vehicle.WIDTH}")
        # print(f"最大转向角: {vehicle.MAX_STEERING}")

        # 5. 所有属性
        print("\n=== 全部属性列表 ===")
        for attr in dir(vehicle):
            if not attr.startswith('_'):  # 不打印私有属性
                try:
                    value = getattr(vehicle, attr)
                    print(f"{attr}: {value}")
                except:
                    print(f"{attr}: 无法获取值")
        
        for step in range(self.step_num):
            vehicle_position = vehicle.position
            # print(vehicle)
            action = self.env.action_space.sample() 
            new_obs, reward, done, info = self.env.step(action)
            
            
            # 收集数据并缓存到内存
            data = {
                "episode": self.episode,
                "step": step,
                "vehicle_position_x": vehicle_position[0],
                "vehicle_position_y": vehicle_position[1],
                "velocity": info["velocity"],
                "steering": info["steering"],
                "acceleration": info["acceleration"]
            }
            self.data_buffer.append(data)
            # print(f'\nself.episode:{self.episode},step: {step},info: {info},vehicle_position: {vehicle_position}')
            obs = new_obs
            if done:
                obs = self.env.reset()
                self.episode += 1
        self.save_to_hdf5()
    def save_to_hdf5(self):
        # 将缓存的数据转换为 NumPy 数组
        episodes = np.array([d["episode"] for d in self.data_buffer])
        steps = np.array([d["step"] for d in self.data_buffer])
        vehicle_position_x = np.array([d["vehicle_position_x"] for d in self.data_buffer])
        vehicle_position_y = np.array([d["vehicle_position_y"] for d in self.data_buffer])
        velocity = np.array([d["velocity"] for d in self.data_buffer])
        steering = np.array([d["steering"] for d in self.data_buffer])
        acceleration = np.array([d["acceleration"] for d in self.data_buffer])

        # 写入 HDF5 文件
        with h5py.File("simulation_data.h5", "w") as f:
            f.create_dataset("episode", data=episodes)
            f.create_dataset("step", data=steps)
            f.create_dataset("vehicle_position_x", data=vehicle_position_x)
            f.create_dataset("vehicle_position_y", data=vehicle_position_y)
            f.create_dataset("velocity", data=velocity)
            f.create_dataset("steering", data=steering)
            f.create_dataset("acceleration", data=acceleration)
        print("Data saved to simulation_data.h5")
if __name__ == "__main__":
    collector = DataCollector(drive_policy="IDM Policy", use_render=True)
    collector.collect_data()
