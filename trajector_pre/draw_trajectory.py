import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

def draw_trajectories(csv_path):
    # 读取CSV文件
    df = pd.read_csv(csv_path)
    
    # 获取不同的episode
    episodes = df['episode'].unique()
    
    # 创建颜色映射
    colors = plt.cm.rainbow(np.linspace(0, 1, len(episodes)))
    
    # 创建图形窗口
    plt.figure(figsize=(10, 8))
    
    # 为每个episode绘制轨迹
    for episode, color in zip(episodes, colors):
        episode_data = df[df['episode'] == episode]
        plt.plot(episode_data['vehicle_position_x'], 
                episode_data['vehicle_position_y'], 
                '-', 
                color=color, 
                label=f'Episode {episode}')
    
    # 设置图形属性
    plt.title('Vehicle Trajectories')
    plt.xlabel('X Position')
    plt.ylabel('Y Position')
    plt.legend()
    plt.grid(True)
    
    # 显示图形
    plt.show()

if __name__ == "__main__":
    csv_path = '/home/<USER>/rl/RL-IL/trajector_pre/data/trajectory_data_1227-12-10.csv'
    draw_trajectories(csv_path)