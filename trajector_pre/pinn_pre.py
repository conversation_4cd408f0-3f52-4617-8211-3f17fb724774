import torch
import torch.nn as nn
import torch.utils.data as data
import numpy as np
import pandas as pd
from pre import PhysicsModel,TrajectoryVisualizer

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

class TrajectoryDataset(data.Dataset):
    def __init__(self, csv_path, sequence_length=30):
        df = pd.read_csv(csv_path)
        self.episodes = df['episode'].unique()
        self.df = df
        self.sequence_length = sequence_length
        
    def __len__(self):
        return len(self.episodes)
    
    def __getitem__(self, idx):
        episode = self.episodes[idx]
        trajectory = self.df[self.df['episode'] == episode].reset_index(drop=True)
        
        # 获取输入特征
        current_states = trajectory[:-1][['vehicle_position_x', 'vehicle_position_y', 
                                       'velocity_x', 'velocity_y', 
                                       'steering', 'acceleration', 
                                       'heading_theta']].values
        
        # 获取目标位置 (next states)
        next_states = trajectory[1:][['vehicle_position_x', 'vehicle_position_y']].values
        
        # 转换为张量
        inputs = torch.FloatTensor(current_states)
        targets = torch.FloatTensor(next_states)
        
        return inputs, targets

def custom_collate(batch):
    # 分离输入和目标
    inputs = [item[0] for item in batch]
    targets = [item[1] for item in batch]
    
    max_len = min(max(len(x) for x in inputs), 500)  # 限制最大长度
    
    padded_inputs = []
    padded_targets = []
    
    for inp, targ in zip(inputs, targets):
        if len(inp) > max_len:
            inp = inp[:max_len]
            targ = targ[:max_len]
        elif len(inp) < max_len:
            pad_len = max_len - len(inp)
            inp = torch.cat([inp, torch.zeros(pad_len, inp.shape[1])])
            targ = torch.cat([targ, torch.zeros(pad_len, targ.shape[1])])
        
        padded_inputs.append(inp)
        padded_targets.append(targ)
    
    return torch.stack(padded_inputs), torch.stack(padded_targets)

class TrajectoryPINN(nn.Module):
    def __init__(self, input_dim=7, hidden_dim=128, output_dim=2):
        super().__init__()
        self.network = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        ).to(device)  # 移至GPU
        
        self.physics_model = PhysicsModel({
            'Lf': 1.25, 'Lr': 1.25,
            'mass': 1100, 'Iz': 2500,
            'Cf': 50000, 'Cr': 50000,
            'max_speed': 20.0,
            'max_acceleration': 1.0,
            'max_steering': np.radians(40)
        })
    
    def forward(self, x):
        return self.network(x)

def train_pinn(model, train_loader, epochs=1000, lr=1e-3):
    optimizer = torch.optim.AdamW(model.parameters(), lr=lr)  # 使用AdamW
    criterion = nn.MSELoss()
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, 'min', patience=5)
    
    for epoch in range(epochs):
        total_loss = 0
        model.train()  # 启用训练模式
        
        for inputs, targets in train_loader:
            inputs = inputs.to(device)
            targets = targets.to(device)
            
            optimizer.zero_grad(set_to_none=True)  # 更高效的梯度清零
            
            # 批量预测以减少循环
            predictions = model(inputs)
            data_loss = criterion(predictions, targets)
            
            # 批量计算物理损失
            with torch.no_grad():  # 物理模型计算不需要梯度
                phys_predictions = []
                for i in range(len(inputs)):
                    x0, y0 = inputs[i, 0, 0].item(), inputs[i, 0, 1].item()
                    vx, vy = inputs[i, 0, 2].item(), inputs[i, 0, 3].item()
                    v0 = np.sqrt(vx**2 + vy**2)
                    theta0 = inputs[i, 0, 6].item()
                    steering = inputs[i, 0, 4].item()
                    accel = inputs[i, 0, 5].item()
                    
                    px, py = model.physics_model.predict_trajectory(
                        x0, y0, v0, theta0,
                        np.array([steering]),
                        np.array([accel]),
                        0.1, 1
                    )
                    phys_predictions.append([px[-1], py[-1]])
            
            phys_predictions = torch.tensor(phys_predictions, dtype=torch.float32).to(device)
            physics_loss = criterion(predictions[:, 0, :], phys_predictions)
            
            loss = data_loss + 0.1 * physics_loss
            loss.backward()
            
            # 梯度裁剪防止梯度爆炸
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            total_loss += loss.item()
        
        avg_loss = total_loss / len(train_loader)
        scheduler.step(avg_loss)
        
        if (epoch + 1) % 10 == 0:
            print(f'Epoch [{epoch+1}/{epochs}], Loss: {avg_loss:.4f}')
    
    return model

class PINNPredictor:
    def __init__(self, model_path):
        self.model = TrajectoryPINN()
        # 添加 map_location 参数
        self.model.load_state_dict(torch.load(model_path, map_location=device))
        self.model.to(device)  # 确保模型在正确的设备上
        self.model.eval()
        
    def predict_trajectory(self, x0, y0, v0, theta0, steering, acceleration, dt, num_steps):
        with torch.no_grad():
            predictions = []
            current_state = torch.tensor([x0, y0, v0*np.cos(theta0), v0*np.sin(theta0),
                                        steering[0], acceleration[0], theta0], 
                                    dtype=torch.float32).to(device)  # 将输入数据移到正确的设备上
            
            for i in range(num_steps):
                pred = self.model(current_state.unsqueeze(0)).squeeze(0)
                predictions.append(pred.cpu().numpy())  # 转回 CPU 进行 numpy 操作
                # 更新下一步的输入状态
                current_state[0:2] = pred
            
            predictions = np.array(predictions)
            return predictions[:, 0], predictions[:, 1]

def main():
    dataset = TrajectoryDataset("/home/<USER>/rl/RL-IL/trajector_pre/data/trajectory_data_0108-00-02.csv")
    # train_loader = data.DataLoader(dataset, batch_size=256, shuffle=True, collate_fn=custom_collate)
    
    # 增大batch_size和num_workers加快训练
    train_loader = data.DataLoader(
        dataset, 
        batch_size=256,  
        shuffle=True, 
        collate_fn=custom_collate,
        num_workers=10,  # 多进程加载数据
        pin_memory=True  # 固定内存加速GPU传输
    )
    # model = TrajectoryPINN()
    # model = model.to(device)  # 将模型移到指定设备
    # model = train_pinn(model, train_loader)
    # torch.save(model.state_dict(), 'pinn_trajectory_model.pt')
    
    predictor = PINNPredictor('pinn_trajectory_model.pt')
    visualizer = TrajectoryVisualizer(predictor, 
                                    "/home/<USER>/rl/RL-IL/trajector_pre/data/trajectory_data_0108-00-02.csv",
                                    episode_num=0)
    visualizer.visualize_trajectory()

if __name__ == "__main__":
    main()