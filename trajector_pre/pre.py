import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.animation import FuncAnimation
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np


def load_trajectory(data_path: str, episode_num: int) -> pd.DataFrame:
    """
    Load trajectory data for a specific episode.

    Parameters:
    ----------
    data_path : str
        The CSV file path.
    episode_num : int
        The episode number to load.

    Returns:
    -------
    pd.DataFrame
        Data of the specified episode.
    """
    data = pd.read_csv(data_path)
    return data[data['episode'] == episode_num].reset_index(drop=True)


class PhysicsModel:
    """
    Simple physics model with RK4 integrator for vehicle state prediction.
    """

    def __init__(self, params: dict):
        """
        Initialize model with vehicle parameters.

        Parameters:
        ----------
        params : dict
            Dictionary of vehicle parameters.
        """
        self.params = params

    def vehicle_dynamics(self, state: np.ndarray, controls: np.ndar<PERSON>) -> np.ndarray:
        """
        Calculate vehicle state derivatives using a simplified model.

        Parameters:
        ----------
        state : np.ndarray
            [x, y, v, theta, beta, yaw_rate]
        controls : np.ndarray
            [steering, acceleration]

        Returns:
        -------
        np.ndarray
            Derivatives of the state.
        """
        x, y, v, theta, beta, yaw_rate = state
        # 方向盘转交 车速  一个初始时刻的位置  
        steering, acc = controls
        if v < 0.1:
            return np.zeros(6)

        alpha_f = steering - np.arctan2(v * np.sin(beta) + self.params['Lf']*yaw_rate,
                                        v * np.cos(beta))
        alpha_r = -np.arctan2(v * np.sin(beta) - self.params['Lr']*yaw_rate,
                              v * np.cos(beta))

        Fyf = self.params['Cf'] * np.sin(alpha_f)
        Fyr = self.params['Cr'] * np.sin(alpha_r)

        dx = v * np.cos(theta + beta)
        dy = v * np.sin(theta + beta)
        dv = acc
        dtheta = yaw_rate
        dbeta = (Fyf + Fyr)/(self.params['mass']*v) - yaw_rate
        dyaw_rate = (self.params['Lf']*Fyf - self.params['Lr']*Fyr)/self.params['Iz']
        return np.array([dx, dy, dv, dtheta, dbeta, dyaw_rate])

    def rk4_update(self, state: np.ndarray, controls: np.ndarray, dt: float) -> np.ndarray:
        """
        RK4 integrator step.

        Parameters:
        ----------
        state : np.ndarray
            Current vehicle state.
        controls : np.ndarray
            Control inputs [steering, acceleration].
        dt : float
            Integration timestep.

        Returns:
        -------
        np.ndarray
            Updated vehicle state.
        """
        k1 = self.vehicle_dynamics(state, controls)
        k2 = self.vehicle_dynamics(state + k1 * dt/2, controls)
        k3 = self.vehicle_dynamics(state + k2 * dt/2, controls)
        k4 = self.vehicle_dynamics(state + k3 * dt, controls)
        return state + (k1 + 2*k2 + 2*k3 + k4) * dt/6

    def predict_trajectory(self,
                           x0: float, y0: float, v0: float, theta0: float,
                           steering: np.ndarray, acceleration: np.ndarray,
                           dt: float, num_steps: int) -> tuple:
        """
        Predict future trajectory using the physics model.

        Parameters:
        ----------
        x0, y0 : float
            Initial position.
        v0 : float
            Initial speed magnitude.
        theta0 : float
            Initial heading.
        steering : np.ndarray
            Steering sequence.
        acceleration : np.ndarray
            Acceleration sequence.
        dt : float
            Timestep for integration.
        num_steps : int
            Number of steps to predict.

        Returns:
        -------
        tuple
            Arrays (px, py) of predicted x and y positions.
        """
        state = np.array([x0, y0, v0, theta0, 0, 0])
        states = [state.copy()]
        for i in range(num_steps):
            if state[2] < 0.1:
                break
            steering_i = np.clip(steering[i], -self.params['max_steering'], self.params['max_steering'])
            acc_i = np.clip(acceleration[i], -self.params['max_acceleration'], self.params['max_acceleration'])
            controls = np.array([steering_i, acc_i])
            state = self.rk4_update(state, controls, dt)
            state[2] = np.clip(state[2], 0, self.params['max_speed'])
            state[3] = np.arctan2(np.sin(state[3]), np.cos(state[3]))
            state[4] = np.clip(state[4], -np.pi/4, np.pi/4)
            states.append(state.copy())
        states = np.array(states)
        return states[:, 0], states[:, 1]


class TrajectoryVisualizer:
    """
    Handles visualization and animation of a vehicle trajectory.
    """

    def __init__(self, model: PhysicsModel, data_path: str, episode_num: int, dt: float = 0.1):
        """
        Initialize the visualizer.

        Parameters:
        ----------
        model : PhysicsModel
            An instance of the PhysicsModel class.
        data_path : str
            CSV file path of the trajectory data.
        episode_num : int
            Episode number to visualize.
        dt : float
            Timestep for predictions.
        """
        self.model = model
        self.data = load_trajectory(data_path, episode_num)
        self.dt = dt

    def visualize_trajectory(self):
        """
        Plot the actual trajectory and predicted trajectory in an animation.
        """
        fig, ax = plt.subplots(figsize=(8, 8))
        ax.set_xlim(self.data["vehicle_position_x"].min() - 10,
                    self.data["vehicle_position_x"].max() + 10)
        ax.set_ylim(self.data["vehicle_position_y"].min() - 10,
                    self.data["vehicle_position_y"].max() + 10)
        ax.set_xlabel("X Position")
        ax.set_ylabel("Y Position")
        ax.set_title("Vehicle Trajectory")
        ax.grid(True)

        ax.plot(self.data["vehicle_position_x"], self.data["vehicle_position_y"],
                label="Actual Trajectory", color="blue")

        predicted_line, = ax.plot([], [], label="Predicted Trajectory", color="red", linestyle="--")
        current_point = ax.scatter([], [], color="green", label="Current Position", s=100)

        steering_data = self.data["steering"].values
        accel_data = self.data["acceleration"].values
        heading_data = self.data["heading_theta"].values
        vx_data = self.data["velocity_x"].values
        vy_data = self.data["velocity_y"].values

        def init():
            predicted_line.set_data([], [])
            current_point.set_offsets(np.empty((0, 2)))
            return predicted_line, current_point

        def update(frame):
            x0 = self.data["vehicle_position_x"].iloc[frame]
            y0 = self.data["vehicle_position_y"].iloc[frame]
            vx0 = vx_data[frame]
            vy0 = vy_data[frame]
            v0 = np.sqrt(vx0**2 + vy0**2)
            heading0 = heading_data[frame]

            prediction_steps = 44
            steering_window = steering_data[frame:frame + prediction_steps]
            accel_window = accel_data[frame:frame + prediction_steps]
            if len(steering_window) < prediction_steps:
                steering_window = np.pad(steering_window,
                                         (0, prediction_steps - len(steering_window)), 'edge')
            if len(accel_window) < prediction_steps:
                accel_window = np.pad(accel_window,
                                      (0, prediction_steps - len(accel_window)), 'edge')

            px, py = self.model.predict_trajectory(x0, y0, v0, heading0,
                                                   steering_window, accel_window,
                                                   self.dt, prediction_steps)
            predicted_line.set_data(px, py)
            current_point.set_offsets([[x0, y0]])
            return predicted_line, current_point

        ani = FuncAnimation(fig, update, frames=len(self.data),
                            init_func=init, blit=True, interval=10)
        plt.legend()
        plt.show()



def main():
    # Example usage
    params = {
        'Lf': 1.25,
        'Lr': 1.25,
        'mass': 1100,
        'Iz': 2500,
        'Cf': 50000,
        'Cr': 50000,
        'max_speed': 20.0,
        'max_acceleration': 1.0,
        'max_steering': np.radians(40)
    }
    physics_model = PhysicsModel(params)
    data_path = "/home/<USER>/rl/RL-IL/trajector_pre/data/trajectory_data_0108-00-02.csv"
    episode_num = 14
    visualizer = TrajectoryVisualizer(physics_model, data_path, episode_num, dt=0.1)
    visualizer.visualize_trajectory()
    
    """
    写新的类，使用PINN神经网络训练模型预测轨迹，可以使用代码中的物理模型作为网络损失的约束。数据是这样的episode,step,vehicle_position_x,vehicle_position_y,velocity_x,velocity_y,steering,acceleration,heading_theta,throttle_brake 0,1,5.0,0.0,0.30695483088493347,-0.0018402024870738387,-0.018542364239692688,1.0,-2.6764768955800378e-05,1 同一个episode代表是同一条轨迹。 使用torch训练完以后，能保存模型，然后也要有一个新的类，加载模型，并且用现在代码里的画图类可视化出效果
    """


if __name__ == "__main__":
    main()